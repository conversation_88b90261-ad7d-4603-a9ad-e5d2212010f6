[{"changedTime": "2025-04-10T19:53:58.194868+00:00", "createdTime": "2025-04-10T19:43:36.812374+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-poc-dlp/providers/Microsoft.Storage/storageAccounts/atomsecpocdlpstorage", "identity": null, "kind": "StorageV2", "location": "centralus", "managedBy": null, "name": "atomsecpocdlpstorage", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-poc-dlp", "sku": {"capacity": null, "family": null, "model": null, "name": "Standard_RAGRS", "size": null, "tier": "Standard"}, "tags": {}, "type": "Microsoft.Storage/storageAccounts"}, {"changedTime": "2024-05-27T15:24:09.495665+00:00", "createdTime": "2024-05-27T15:14:09.173273+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/microsoft.insights/components/app-atomsec-dev01", "identity": null, "kind": "web", "location": "eastus", "managedBy": null, "name": "app-atomsec-dev01", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": null, "tags": {}, "type": "microsoft.insights/components"}, {"changedTime": "2025-04-09T17:42:33.463881+00:00", "createdTime": "2024-05-27T20:19:10.212782+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.Web/sites/app-atomsec-dev01", "identity": {"principalId": "9f0940f5-bbb4-4ae2-ae4d-648cc3ce2928", "tenantId": "41b676db-bf6f-46ae-a354-a83a1362533f", "type": "SystemAssigned", "userAssignedIdentities": null}, "kind": "app", "location": "eastus", "managedBy": null, "name": "app-atomsec-dev01", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": null, "tags": {"hidden-link: /app-insights-conn-string": "InstrumentationKey=d7cd2c1f-e00c-49f1-83eb-f500509d2c96;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=293ba9f2-649c-49c1-a738-b03a8729afae", "hidden-link: /app-insights-instrumentation-key": "d7cd2c1f-e00c-49f1-83eb-f500509d2c96", "hidden-link: /app-insights-resource-id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/microsoft.insights/components/app-atomsec-dev01202405271618"}, "type": "Microsoft.Web/sites"}, {"changedTime": "2024-05-27T15:34:12.543785+00:00", "createdTime": "2024-05-27T15:24:12.403757+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/microsoft.insights/actiongroups/Application Insights Smart Detection", "identity": null, "kind": null, "location": "global", "managedBy": null, "name": "Application Insights Smart Detection", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": null, "tags": null, "type": "microsoft.insights/actiongroups"}, {"changedTime": "2024-05-27T20:28:46.499730+00:00", "createdTime": "2024-05-27T20:18:46.229613+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/microsoft.insights/components/app-atomsec-dev01202405271618", "identity": null, "kind": "web", "location": "eastus", "managedBy": null, "name": "app-atomsec-dev01202405271618", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": null, "tags": {}, "type": "microsoft.insights/components"}, {"changedTime": "2024-06-10T03:08:56.499648+00:00", "createdTime": "2024-05-27T20:18:46.243210+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.Web/serverFarms/ASP-atomsecdevfrontend-b779", "identity": null, "kind": "app", "location": "eastus", "managedBy": null, "name": "ASP-atomsecdevfrontend-b779", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": {"capacity": 1, "family": "B", "model": null, "name": "B1", "size": "B1", "tier": "Basic"}, "tags": {}, "type": "Microsoft.Web/serverFarms"}, {"changedTime": "2024-05-31T20:12:56.308548+00:00", "createdTime": "2024-05-31T20:02:35.526542+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsecsfdc/providers/Microsoft.Storage/storageAccounts/atomsecsfdc", "identity": null, "kind": "Storage", "location": "eastus", "managedBy": null, "name": "atomsecsfdc", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsecsfdc", "sku": {"capacity": null, "family": null, "model": null, "name": "Standard_LRS", "size": null, "tier": "Standard"}, "tags": {}, "type": "Microsoft.Storage/storageAccounts"}, {"changedTime": "2024-05-31T20:34:43.592610+00:00", "createdTime": "2024-05-31T20:24:43.028911+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-backend/providers/microsoft.insights/components/func-atomsec-sfdc-dev", "identity": null, "kind": "web", "location": "eastus", "managedBy": null, "name": "func-atomsec-sfdc-dev", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-backend", "sku": null, "tags": {}, "type": "microsoft.insights/components"}, {"changedTime": "2025-04-24T07:47:30.110015+00:00", "createdTime": "2024-05-31T20:24:50.028454+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-backend/providers/Microsoft.Web/sites/func-atomsec-sfdc-dev", "identity": {"principalId": "76d3a3df-af2d-4736-95e2-d77d7ba875dd", "tenantId": "41b676db-bf6f-46ae-a354-a83a1362533f", "type": "SystemAssigned", "userAssignedIdentities": null}, "kind": "functionapp,linux", "location": "eastus", "managedBy": null, "name": "func-atomsec-sfdc-dev", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-backend", "sku": null, "tags": {"hidden-link: /app-insights-conn-string": "InstrumentationKey=cb3a8244-5600-41ad-93a9-337bbe9b262d;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=ac554bfb-97d0-4341-bee9-3f6adfb5d7f3", "hidden-link: /app-insights-instrumentation-key": "cb3a8244-5600-41ad-93a9-337bbe9b262d", "hidden-link: /app-insights-resource-id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-backend/providers/microsoft.insights/components/func-atomsec-sfdc-dev"}, "type": "Microsoft.Web/sites"}, {"changedTime": "2024-06-02T02:15:48.245127+00:00", "createdTime": "2024-06-02T02:05:27.336707+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-backend/providers/Microsoft.Storage/storageAccounts/statomsecfuncsfdcdev", "identity": null, "kind": "StorageV2", "location": "eastus", "managedBy": null, "name": "statomsecfuncsfdcdev", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-backend", "sku": {"capacity": null, "family": null, "model": null, "name": "Standard_RAGRS", "size": null, "tier": "Standard"}, "tags": {}, "type": "Microsoft.Storage/storageAccounts"}, {"changedTime": "2024-06-11T02:54:16.391488+00:00", "createdTime": "2024-06-11T02:21:06.218289+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-apim/providers/Microsoft.ApiManagement/service/apim-atomsec-dev", "identity": {"principalId": "86f63fe3-912c-480c-82a9-9d37f925aa18", "tenantId": "41b676db-bf6f-46ae-a354-a83a1362533f", "type": "SystemAssigned", "userAssignedIdentities": null}, "kind": null, "location": "eastus", "managedBy": null, "name": "apim-atomsec-dev", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-apim", "sku": {"capacity": 1, "family": null, "model": null, "name": "Developer", "size": null, "tier": null}, "systemData": {"createdAt": "2024-06-11T02:21:06.2360668Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2024-06-11T02:21:06.2360668Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": {}, "type": "Microsoft.ApiManagement/service"}, {"changedTime": "2024-07-01T20:44:19.548180+00:00", "createdTime": "2024-06-14T21:37:01.035442+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-backend/providers/Microsoft.Web/sites/func-atomsec-sfdc-dev/slots/stage", "identity": {"principalId": "34d562de-565a-47d4-b49b-1a885d52815d", "tenantId": "41b676db-bf6f-46ae-a354-a83a1362533f", "type": "SystemAssigned", "userAssignedIdentities": null}, "kind": "functionapp,linux", "location": "eastus", "managedBy": null, "name": "func-atomsec-sfdc-dev/stage", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-backend", "sku": null, "tags": {"hidden-link: /app-insights-conn-string": "InstrumentationKey=cb3a8244-5600-41ad-93a9-337bbe9b262d;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=ac554bfb-97d0-4341-bee9-3f6adfb5d7f3", "hidden-link: /app-insights-instrumentation-key": "cb3a8244-5600-41ad-93a9-337bbe9b262d", "hidden-link: /app-insights-resource-id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-backend/providers/microsoft.insights/components/func-atomsec-sfdc-dev"}, "type": "Microsoft.Web/sites/slots"}, {"changedTime": "2024-06-18T21:40:20.151158+00:00", "createdTime": "2024-06-18T21:30:19.755188+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-apim/providers/microsoft.insights/components/apim-atomsec-dev", "identity": null, "kind": "web", "location": "eastus", "managedBy": null, "name": "apim-atomsec-dev", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-apim", "sku": null, "tags": {}, "type": "microsoft.insights/components"}, {"changedTime": "2024-06-21T23:10:53.364683+00:00", "createdTime": "2024-06-21T23:00:52.947845+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/Microsoft.KeyVault/vaults/akv-atomsec-dev", "identity": null, "kind": null, "location": "eastus", "managedBy": null, "name": "akv-atomsec-dev", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": null, "tags": {}, "type": "Microsoft.KeyVault/vaults"}, {"changedTime": "2025-08-23T04:14:28.114350+00:00", "createdTime": "2024-06-21T23:43:59.463146+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/Microsoft.Sql/servers/sqldb-atomsec-dev", "identity": null, "kind": "v12.0", "location": "eastus2", "managedBy": null, "name": "sqldb-atomsec-dev", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": null, "tags": {}, "type": "Microsoft.Sql/servers"}, {"changedTime": "2025-08-23T04:18:53.610576+00:00", "createdTime": "2024-06-21T23:45:18.401008+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/Microsoft.Sql/servers/sqldb-atomsec-dev/databases/master", "identity": null, "kind": "v12.0,system", "location": "eastus2", "managedBy": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/Microsoft.Sql/servers/sqldb-atomsec-dev", "name": "sqldb-atomsec-dev/master", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": {"capacity": 0, "family": null, "model": null, "name": "System", "size": null, "tier": "System"}, "tags": null, "type": "Microsoft.Sql/servers/databases"}, {"changedTime": "2025-08-23T04:16:35.821188+00:00", "createdTime": "2024-06-22T00:02:43.379467+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/Microsoft.Sql/servers/sqldb-atomsec-dev/databases/sql-atomsec-dev", "identity": null, "kind": "v12.0,user", "location": "eastus2", "managedBy": null, "name": "sqldb-atomsec-dev/sql-atomsec-dev", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": {"capacity": 20, "family": null, "model": null, "name": "Standard", "size": null, "tier": "Standard"}, "tags": {}, "type": "Microsoft.Sql/servers/databases"}, {"changedTime": "2024-11-04T02:13:24.335756+00:00", "createdTime": "2024-11-04T02:03:23.722070+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.Web/serverFarms/ASP-atomsecdevfrontend-bb68", "identity": null, "kind": "app", "location": "centralus", "managedBy": null, "name": "ASP-atomsecdevfrontend-bb68", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": {"capacity": 0, "family": "D", "model": null, "name": "D1", "size": "D1", "tier": "Shared"}, "tags": {}, "type": "Microsoft.Web/serverFarms"}, {"changedTime": "2024-11-04T02:13:27.447488+00:00", "createdTime": "2024-11-04T02:03:27.045711+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.Web/sites/atomsec", "identity": null, "kind": "app", "location": "centralus", "managedBy": null, "name": "atomsec", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": null, "tags": {}, "type": "Microsoft.Web/sites"}, {"changedTime": "2024-11-04T02:23:22.138789+00:00", "createdTime": "2024-11-04T02:12:36.272858+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.Cdn/profiles/AtomCDN", "identity": null, "kind": "frontdoor", "location": "global", "managedBy": null, "name": "AtomCDN", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": {"capacity": null, "family": null, "model": null, "name": "Standard_AzureFrontDoor", "size": null, "tier": null}, "tags": {}, "type": "Microsoft.Cdn/profiles"}, {"changedTime": "2024-11-04T02:23:33.901787+00:00", "createdTime": "2024-11-04T02:13:19.017024+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.Cdn/profiles/AtomCDN/afdendpoints/PublicSite", "identity": null, "kind": null, "location": "global", "managedBy": null, "name": "AtomCDN/PublicSite", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": null, "tags": {}, "type": "Microsoft.Cdn/profiles/afdendpoints"}, {"changedTime": "2024-11-04T02:56:08.391334+00:00", "createdTime": "2024-11-04T02:46:08.069235+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.Network/dnszones/atomsec.ai", "identity": null, "kind": null, "location": "global", "managedBy": null, "name": "atomsec.ai", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": null, "tags": {}, "type": "Microsoft.Network/dnszones"}, {"changedTime": "2024-11-10T21:00:12.783632+00:00", "createdTime": "2024-11-10T20:49:58.238817+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.Cdn/profiles/AtomCDN/afdendpoints/WWW", "identity": null, "kind": null, "location": "global", "managedBy": null, "name": "AtomCDN/WWW", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": null, "tags": {}, "type": "Microsoft.Cdn/profiles/afdendpoints"}, {"changedTime": "2025-04-28T18:25:22.968580+00:00", "createdTime": "2025-04-28T18:15:22.383652+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.Web/staticSites/staticweb-atomsec-dev", "identity": null, "kind": null, "location": "eastus2", "managedBy": null, "name": "staticweb-atomsec-dev", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": {"capacity": null, "family": null, "model": null, "name": "Free", "size": null, "tier": "Free"}, "tags": {}, "type": "Microsoft.Web/staticSites"}, {"changedTime": "2025-05-02T21:40:13.060569+00:00", "createdTime": "2025-05-02T21:30:01.085816+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.Cdn/profiles/AtomCDN/afdendpoints/ATOM", "identity": null, "kind": null, "location": "global", "managedBy": null, "name": "AtomCDN/ATOM", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": null, "tags": {}, "type": "Microsoft.Cdn/profiles/afdendpoints"}, {"changedTime": "2025-05-04T14:10:52.525736+00:00", "createdTime": "2025-05-04T14:00:39.628585+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.Cdn/profiles/AtomCDN/afdendpoints/mavq", "identity": null, "kind": null, "location": "global", "managedBy": null, "name": "AtomCDN/mavq", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": null, "tags": {}, "type": "Microsoft.Cdn/profiles/afdendpoints"}, {"changedTime": "2025-05-05T13:37:21.087134+00:00", "createdTime": "2025-05-05T13:27:09.474463+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.Cdn/profiles/AtomCDN/afdendpoints/MTX", "identity": null, "kind": null, "location": "global", "managedBy": null, "name": "AtomCDN/MTX", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": null, "tags": {}, "type": "Microsoft.Cdn/profiles/afdendpoints"}, {"changedTime": "2025-05-15T16:22:16.325896+00:00", "createdTime": "2025-05-15T16:12:16.140563+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/Microsoft.CognitiveServices/accounts/atomsec-dev-aiservices", "identity": {"principalId": null, "tenantId": null, "type": "None", "userAssignedIdentities": null}, "kind": "CognitiveServices", "location": "eastus", "managedBy": null, "name": "atomsec-dev-aiservices", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": {"capacity": null, "family": null, "model": null, "name": "S0", "size": null, "tier": null}, "systemData": {"createdAt": "2025-05-15T16:12:16.1572768Z", "createdBy": "<PERSON>@atomsec.ai", "createdByType": "User", "lastModifiedAt": "2025-05-15T16:14:18.3863729Z", "lastModifiedBy": "<PERSON>@atomsec.ai", "lastModifiedByType": "User"}, "tags": {}, "type": "Microsoft.CognitiveServices/accounts"}, {"changedTime": "2025-05-25T02:37:14.297631+00:00", "createdTime": "2025-05-25T02:27:13.923428+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/microsoft.insights/components/func-atomsec-dbconnect-dev", "identity": null, "kind": "web", "location": "eastus", "managedBy": null, "name": "func-atomsec-dbconnect-dev", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": null, "tags": {}, "type": "microsoft.insights/components"}, {"changedTime": "2025-06-10T03:42:00.433466+00:00", "createdTime": "2025-06-09T05:15:10.710283+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/Microsoft.Web/sites/func-atomsec-dbconnect-dev", "identity": {"principalId": "b7c0b721-d905-4b4d-92c1-c1f352daf5c6", "tenantId": "41b676db-bf6f-46ae-a354-a83a1362533f", "type": "SystemAssigned", "userAssignedIdentities": null}, "kind": "functionapp,linux", "location": "eastus", "managedBy": null, "name": "func-atomsec-dbconnect-dev", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": null, "tags": {"hidden-link: /app-insights-resource-id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/microsoft.insights/components/func-atomsec-dbconnect-dev"}, "type": "Microsoft.Web/sites"}, {"changedTime": "2025-06-06T03:38:02.747356+00:00", "createdTime": "2025-06-05T03:36:42.255315+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/Microsoft.ServiceBus/namespaces/atomsec", "identity": null, "kind": null, "location": "eastus", "managedBy": null, "name": "atomsec", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": {"capacity": null, "family": null, "model": null, "name": "Standard", "size": null, "tier": "Standard"}, "tags": {}, "type": "Microsoft.ServiceBus/namespaces"}, {"changedTime": "2025-07-31T03:35:22.022781+00:00", "createdTime": "2025-06-11T03:07:39.763282+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/Microsoft.Web/sites/func-atomsec-dbconnect-dev/slots/stage", "identity": {"principalId": "6c2655dd-f4c0-4355-9bd6-2838fc206429", "tenantId": "41b676db-bf6f-46ae-a354-a83a1362533f", "type": "SystemAssigned", "userAssignedIdentities": null}, "kind": "functionapp,linux", "location": "eastus", "managedBy": null, "name": "func-atomsec-dbconnect-dev/stage", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": null, "tags": {"hidden-link: /app-insights-resource-id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/microsoft.insights/components/func-atomsec-dbconnect-dev"}, "type": "Microsoft.Web/sites/slots"}, {"changedTime": "2025-07-24T17:54:35.100839+00:00", "createdTime": "2025-07-24T17:44:34.811859+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-backend/providers/microsoft.insights/components/func-atomsec-sfdc-dev02", "identity": null, "kind": "web", "location": "eastus", "managedBy": null, "name": "func-atomsec-sfdc-dev02", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-backend", "sku": null, "tags": {}, "type": "microsoft.insights/components"}, {"changedTime": "2025-07-29T03:28:40.380095+00:00", "createdTime": "2025-07-24T17:45:02.285686+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-backend/providers/Microsoft.Web/sites/func-atomsec-sfdc-dev02", "identity": {"principalId": "824a1b4a-a6c2-49fd-bf3d-b744c7c1ba37", "tenantId": "41b676db-bf6f-46ae-a354-a83a1362533f", "type": "SystemAssigned", "userAssignedIdentities": null}, "kind": "functionapp,linux", "location": "eastus", "managedBy": null, "name": "func-atomsec-sfdc-dev02", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-backend", "sku": null, "tags": {"hidden-link: /app-insights-resource-id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-backend/providers/microsoft.insights/components/func-atomsec-sfdc-dev02"}, "type": "Microsoft.Web/sites"}, {"changedTime": "2025-08-05T03:33:33.972761+00:00", "createdTime": "2025-07-28T05:55:38.814865+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-backend/providers/Microsoft.Web/sites/func-atomsec-sfdc-dev02/slots/stage", "identity": {"principalId": "b7caecb4-ae6c-447f-bd31-2c6711fbef60", "tenantId": "41b676db-bf6f-46ae-a354-a83a1362533f", "type": "SystemAssigned", "userAssignedIdentities": null}, "kind": "functionapp,linux", "location": "eastus", "managedBy": null, "name": "func-atomsec-sfdc-dev02/stage", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-backend", "sku": null, "tags": {"hidden-link: /app-insights-resource-id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-backend/providers/microsoft.insights/components/func-atomsec-sfdc-dev02"}, "type": "Microsoft.Web/sites/slots"}, {"changedTime": "2025-08-01T03:35:11.340100+00:00", "createdTime": "2025-08-01T03:24:51.025254+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/Microsoft.Storage/storageAccounts/statomsecdbconnectdev02", "identity": null, "kind": "Storage", "location": "eastus", "managedBy": null, "name": "statomsecdbconnectdev02", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": {"capacity": null, "family": null, "model": null, "name": "Standard_LRS", "size": null, "tier": "Standard"}, "tags": {}, "type": "Microsoft.Storage/storageAccounts"}, {"changedTime": "2025-08-01T03:34:51.403357+00:00", "createdTime": "2025-08-01T03:24:51.023375+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/microsoft.insights/components/func-atomsec-dbconnect-dev02", "identity": null, "kind": "web", "location": "eastus", "managedBy": null, "name": "func-atomsec-dbconnect-dev02", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": null, "tags": {}, "type": "microsoft.insights/components"}, {"changedTime": "2025-08-08T03:16:10.644843+00:00", "createdTime": "2025-08-01T03:25:13.422134+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/Microsoft.Web/sites/func-atomsec-dbconnect-dev02", "identity": {"principalId": "634a883f-6ccc-4ff1-93dd-c2f69757aa03", "tenantId": "41b676db-bf6f-46ae-a354-a83a1362533f", "type": "SystemAssigned", "userAssignedIdentities": null}, "kind": "functionapp,linux", "location": "eastus", "managedBy": null, "name": "func-atomsec-dbconnect-dev02", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": null, "tags": {"hidden-link: /app-insights-resource-id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/microsoft.insights/components/func-atomsec-dbconnect-dev02"}, "type": "Microsoft.Web/sites"}, {"changedTime": "2025-08-01T03:44:53.330616+00:00", "createdTime": "2025-08-01T03:27:40.019330+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/Microsoft.Web/sites/func-atomsec-dbconnect-dev02/slots/stage", "identity": {"principalId": "2e8d6843-d545-46cd-b078-c2cd7ff0ded3", "tenantId": "41b676db-bf6f-46ae-a354-a83a1362533f", "type": "SystemAssigned", "userAssignedIdentities": null}, "kind": "functionapp,linux", "location": "eastus", "managedBy": null, "name": "func-atomsec-dbconnect-dev02/stage", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": null, "tags": null, "type": "Microsoft.Web/sites/slots"}, {"changedTime": "2025-08-05T03:20:22.217693+00:00", "createdTime": "2025-08-03T17:47:55.388639+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-data/providers/Microsoft.Web/sites/func-atomsec-dbconnect-dev02/slots/qa", "identity": {"principalId": "6482e529-1277-4337-86c3-915b26affd58", "tenantId": "41b676db-bf6f-46ae-a354-a83a1362533f", "type": "SystemAssigned", "userAssignedIdentities": null}, "kind": "functionapp,linux", "location": "eastus", "managedBy": null, "name": "func-atomsec-dbconnect-dev02/qa", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-data", "sku": null, "tags": null, "type": "Microsoft.Web/sites/slots"}, {"changedTime": "2025-08-08T07:13:29.334759+00:00", "createdTime": "2025-08-08T07:03:29.323423+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.CognitiveServices/accounts/atomsec-content-safety", "identity": null, "kind": "ContentSafety", "location": "eastus", "managedBy": null, "name": "atomsec-content-safety", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": {"capacity": null, "family": null, "model": null, "name": "S0", "size": null, "tier": null}, "systemData": {"createdAt": "2025-08-08T07:03:29.5620328Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-08-08T07:03:29.5620328Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": null, "type": "Microsoft.CognitiveServices/accounts"}, {"changedTime": "2025-08-08T07:14:26.248900+00:00", "createdTime": "2025-08-08T07:04:09.179666+00:00", "extendedLocation": null, "id": "/subscriptions/********-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/atomsec-dev-frontend/providers/Microsoft.CognitiveServices/accounts/atomsec-openai", "identity": null, "kind": "OpenAI", "location": "eastus", "managedBy": null, "name": "atomsec-openai", "plan": null, "properties": null, "provisioningState": "Succeeded", "resourceGroup": "atomsec-dev-frontend", "sku": {"capacity": null, "family": null, "model": null, "name": "S0", "size": null, "tier": null}, "systemData": {"createdAt": "2025-08-08T07:04:09.1995452Z", "createdBy": "<EMAIL>", "createdByType": "User", "lastModifiedAt": "2025-08-08T07:04:09.1995452Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User"}, "tags": null, "type": "Microsoft.CognitiveServices/accounts"}]