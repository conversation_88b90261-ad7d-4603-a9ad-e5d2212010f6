# Python Version Update Summary: 3.12 → 3.11

## Overview
Updated all Azure DevOps pipelines and configuration files to use Python 3.11 instead of Python 3.12 to resolve the Azure Functions runtime compatibility issue.

## Root Cause
Azure Functions v4 does not support Python 3.12. The error `Microsoft.Azure.WebJobs.Script: WorkerConfig for runtime: python not found` was occurring because:

### **Primary Issue:**
- Function App was configured for Python 3.12
- Azure Functions v4 only supports Python 3.7, 3.8, 3.9, 3.10, and 3.11
- Python 3.12 support was never added to Functions v4

### **Secondary Issue (Discovered):**
- Azure CLI in the pipeline was running on Python 3.12.10
- This caused Python 3.12 bytecode files (`.cpython-312.pyc`) to be generated
- These files were included in the deployment package, causing pipeline failures
- The verification step detected these files and failed the build

## Files Updated

### 1. Pipeline Files (Primary Changes)

#### `atomsec-func-db-r/pipeline-func-db-dev.yml`
- **Line 80**: `versionSpec: '3.12'` → `versionSpec: '3.11'`
- **Line 643**: `"PYTHON_VERSION=3.12"` → `"PYTHON_VERSION=3.11"`

#### `atomsec-func-db-r/pipeline-func-db-dev-fast.yml`
- **Line 24**: `versionSpec: '3.12'` → `versionSpec: '3.11'`
- **Line 83**: `runtimeStack: 'PYTHON|3.12'` → `runtimeStack: 'PYTHON|3.11'`

#### `atomsec-func-sfdc/pipeline-func-sfdc-dev.yml`
- **Line 30**: `versionSpec: '3.12'` → `versionSpec: '3.11'`
- **Line 394**: `versionSpec: '3.12'` → `versionSpec: '3.11'`
- **Line 63**: Comment updated from "Python 3.12" to "Python 3.11"

#### `atomsec-func-sfdc/pipeline-backups/pipeline-func-sfdc-dev-legacy.yml`
- **Line 27**: `versionSpec: '3.12'` → `versionSpec: '3.11'`
- **Line 60**: Comment updated from "Python 3.12" to "Python 3.11"

#### `atomsec-func-sfdc/scripts/direct_production_deploy.yml`
- **Line 26**: `runtimeStack: 'PYTHON|3.12'` → `runtimeStack: 'PYTHON|3.11'`

### 2. Pipeline Cleanup Steps Added

#### All Pipeline Files
- **Added Python bytecode cleanup step** before dependency installation
- **Removes all `__pycache__` directories** and `.pyc`, `.pyo`, `.pyd` files
- **Prevents Python 3.12 bytecode conflicts** during deployment
- **Ensures clean deployment package** without version mismatches

### 3. Pipeline Task Reordering
- **Moved Python cleanup step** to run BEFORE Azure CLI setup
- **Ensures Azure CLI uses Python 3.11** for all operations
- **Prevents Python 3.12 bytecode generation** during Azure CLI tasks
- **Added Python version verification** in Azure CLI task

### 4. Documentation Files

#### `atomsec-func-db-r/PYTHON_WORKER_RUNTIME_FIX.md`
- **Line 21**: "Pipeline was deploying Python 3.12" → "Pipeline was deploying Python 3.11"
- **Line 109**: "Updated Python version from 3.11 to 3.12" → "Updated Python version from 3.12 to 3.11"
- **Line 153**: "function app will use Python 3.12" → "function app will use Python 3.11"

#### `atomsec-func-sfdc/DEPLOYMENT_STRATEGY.md`
- **Line 19**: "Runtime: Python 3.12" → "Runtime: Python 3.11"
- **Line 76**: "Python Version: 3.12" → "Python Version: 3.11"
- **Line 94**: "Python 3.12" → "Python 3.11"
- **Line 108**: "Install Python 3.12" → "Install Python 3.11"

#### `atomsec-func-sfdc/STAGING_SLOT_ALTERNATIVE.md`
- **Line 20**: "Runtime shows Python|3.12" → "Runtime shows Python|3.11"

#### `atomsec-func-db-r/DEPLOYMENT_GUIDE.md`
- **Line 25**: "Python (3.12)" → "Python (3.11)"
- **Line 89**: "--runtime-version 3.12" → "--runtime-version 3.11"
- **Line 204**: "Verify Python version compatibility (3.12)" → "Verify Python version compatibility (3.11)"

## What This Fixes

1. **Resolves Runtime Error**: The `WorkerConfig for runtime: python not found` error will be resolved
2. **Version Compatibility**: Python 3.11 is fully supported by Azure Functions v4
3. **Consistent Configuration**: All pipelines now use the same Python version
4. **Function App Startup**: The Function App will be able to start properly and process requests
5. **Eliminates Bytecode Conflicts**: Removes Python 3.12 bytecode files that were causing deployment failures

## Next Steps

1. **Clean Up Bytecode Files**: Run `./cleanup_python_bytecode.sh` to remove all Python 3.12 bytecode files
2. **Commit Changes**: Commit all updated pipeline files and cleaned codebase to your repository
3. **Trigger New Build**: Run a new pipeline build to deploy with Python 3.11
4. **Verify Function App**: Check that the Function App starts without the worker runtime error
5. **Test Endpoints**: Verify that your function endpoints are responding correctly

## Verification

After deployment, verify in Azure Portal:
- **Application Settings**: `PYTHON_VERSION` should show `3.11`
- **Functions Host**: Should start without worker runtime errors
- **Function Endpoints**: Should respond to HTTP requests
- **Logs**: No more "WorkerConfig for runtime: python not found" errors

## Notes

- Python 3.11 is the latest supported version for Azure Functions v4
- This change maintains compatibility with all existing dependencies
- No code changes are required, only configuration updates
- The fix applies to both production and staging slots
- **New**: Added automatic bytecode cleanup in all pipelines to prevent future conflicts
- **New**: Created `cleanup_python_bytecode.sh` script for local cleanup before commits
