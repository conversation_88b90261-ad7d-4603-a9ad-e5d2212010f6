#!/usr/bin/env python3
"""
Test Script for SFDC Scan Fix (Restored Integration Endpoints)

This script tests that the SFDC service can now handle integration scan requests
after restoring the original integration_endpoints.py and task_endpoints.py files from git.
"""

import sys
import os

# Add the SFDC service directory to Python path
sfdc_dir = os.path.join(os.path.dirname(__file__), 'atomsec-func-sfdc')
sys.path.insert(0, sfdc_dir)

def test_task_endpoints():
    """Test that the task endpoints can be imported and created"""
    print("🔍 Testing SFDC Task Endpoints (Restored)...")
    print("=" * 50)
    
    try:
        # Test import
        import api.task_endpoints
        print("✅ task_endpoints imported successfully")
    except Exception as e:
        print(f"❌ Failed to import task_endpoints: {e}")
        return False
    
    try:
        # Test blueprint creation
        from api.task_endpoints import bp as task_bp
        print("✅ task_endpoints blueprint created successfully")
    except Exception as e:
        print(f"❌ Failed to create task_endpoints blueprint: {e}")
        return False
    
    try:
        # Check if key functions exist
        module = api.task_endpoints
        
        if hasattr(module, 'create_task'):
            print("✅ create_task function found")
        else:
            print("❌ create_task function not found")
            return False
        
        if hasattr(module, 'get_tasks'):
            print("✅ get_tasks function found")
        if hasattr(module, 'update_task_status'):
            print("✅ update_task_status function found")
            
        print("✅ Task endpoints module structure verified")
            
    except Exception as e:
        print(f"❌ Failed to inspect task endpoints: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 SFDC Task Endpoints are working (restored from git)!")
    return True

def test_integration_endpoints():
    """Test that the integration endpoints can be imported and created"""
    print("\n🔍 Testing SFDC Integration Endpoints (Restored)...")
    print("=" * 50)
    
    try:
        # Test import
        import api.integration_endpoints
        print("✅ integration_endpoints imported successfully")
    except Exception as e:
        print(f"❌ Failed to import integration_endpoints: {e}")
        return False
    
    try:
        # Test blueprint creation
        from api.integration_endpoints import bp as integration_bp
        print("✅ integration_endpoints blueprint created successfully")
    except Exception as e:
        print(f"❌ Failed to create integration_endpoints blueprint: {e}")
        return False
    
    try:
        # Test that the scan endpoint route exists by checking the module
        # Since we can't directly access routes from the blueprint, we'll verify
        # the module has the expected functions
        
        module = api.integration_endpoints
        
        # Check if the scan function exists
        if hasattr(module, 'scan_integration'):
            print("✅ scan_integration function found")
        else:
            print("❌ scan_integration function not found")
            return False
        
        # Check if other key functions exist
        if hasattr(module, 'get_integrations'):
            print("✅ get_integrations function found")
        if hasattr(module, 'create_integration'):
            print("✅ create_integration function found")
        if hasattr(module, 'get_integration_by_id'):
            print("✅ get_integration_by_id function found")
            
        print("✅ Integration endpoints module structure verified")
            
    except Exception as e:
        print(f"❌ Failed to inspect integration endpoints: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 SFDC Integration Endpoints are working (restored from git)!")
    return True

def test_dependencies():
    """Test that integration endpoints can import from task endpoints"""
    print("\n🔍 Testing Dependencies...")
    print("=" * 50)
    
    try:
        # Test that integration endpoints can import from task endpoints
        from api.integration_endpoints import scan_integration
        print("✅ scan_integration function imported successfully")
        
        # Check if it's callable
        if callable(scan_integration):
            print("✅ scan_integration is a callable function")
        else:
            print("❌ scan_integration is not callable")
            return False
            
    except Exception as e:
        print(f"❌ Failed to import scan_integration: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Dependencies are working correctly!")
    return True

def test_scan_endpoint_specifically():
    """Test that the specific scan endpoint function exists"""
    print("\n🔍 Testing Scan Endpoint Specifically...")
    print("=" * 50)
    
    try:
        import api.integration_endpoints
        
        # Check if the scan function exists
        if hasattr(api.integration_endpoints, 'scan_integration'):
            print("✅ scan_integration function found in integration_endpoints")
            
            # Check if it's a function
            scan_func = getattr(api.integration_endpoints, 'scan_integration')
            if callable(scan_func):
                print("✅ scan_integration is a callable function")
            else:
                print("❌ scan_integration is not callable")
                return False
        else:
            print("❌ scan_integration function not found in integration_endpoints")
            return False
            
        print("✅ Scan endpoint is properly configured")
        return True
        
    except Exception as e:
        print(f"❌ Failed to test scan endpoint: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 SFDC Scan Fix Test Script (Restored Integration & Task Endpoints)")
    print("=" * 50)
    
    # Test task endpoints first (since integration endpoints depend on them)
    if not test_task_endpoints():
        print("\n❌ Task endpoints test failed!")
        return False
    
    # Test integration endpoints
    if not test_integration_endpoints():
        print("\n❌ Integration endpoints test failed!")
        return False
    
    # Test dependencies
    if not test_dependencies():
        print("\n❌ Dependencies test failed!")
        return False
    
    # Test scan endpoint specifically
    if not test_scan_endpoint_specifically():
        print("\n❌ Scan endpoint test failed!")
        return False
    
    print("\n📋 Summary:")
    print("✅ SFDC task endpoints are working (restored from git)")
    print("✅ SFDC integration endpoints are working (restored from git)")
    print("✅ Dependencies between endpoints are working")
    print("✅ Integration scan endpoint is properly configured")
    print("✅ SFDC scan functionality should now work")
    print("\n🎯 Next Steps:")
    print("1. Restart the SFDC service")
    print("2. Test the scan endpoint: POST /api/integrations/{id}/scan")
    print("3. Verify the DB service proxy can successfully call the SFDC service")
    print("\n💡 Note: This fix restores the original integration_endpoints.py and")
    print("   task_endpoints.py from git, preserving all existing functionality.")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
