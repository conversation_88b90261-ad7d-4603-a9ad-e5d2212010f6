# CORS Manifest.json Fix - Deployment Instructions

## Problem Fixed
The `manifest.json` file was being intercepted by Azure App Service authentication and redirected to Azure AD login, causing CORS errors:
```
Access to manifest at 'https://login.windows.net/.../oauth2/v2.0/authorize...' has been blocked by CORS policy
```

## Files Modified

### 1. `staticwebapp.config.json`
- ✅ Added `"allowedRoles": ["anonymous"]` to all static file routes
- ✅ Enhanced CORS headers for manifest.json and other static assets
- ✅ Explicitly configured routes for `/manifest.json`, `/favicon.ico`, `/static/*`, `/assets/*`, and all file types

### 2. `authentication.json`
- ✅ Added `"requireAuthentication": false` to globalValidation
- ✅ Maintained comprehensive `excludedPaths` list including manifest.json
- ✅ Kept `"unauthenticatedClientAction": "AllowAnonymous"` configuration

### 3. `test-manifest.js`
- ✅ Enhanced test script with comprehensive CORS and authentication testing
- ✅ Tests multiple static file endpoints
- ✅ Provides detailed feedback on authentication redirects

## Deployment Steps

### Step 1: Deploy Files to Azure App Service
```bash
# Navigate to frontend directory
cd atomsec-app-frontend

# Build the application (if needed)
npm run build

# Deploy using your existing method:
# - Azure DevOps Pipeline
# - GitHub Actions
# - Azure CLI
# - Visual Studio Code Azure Extension
```

### Step 2: Verify Azure App Service Configuration
1. Go to Azure Portal → Your App Service
2. Navigate to **Authentication**
3. Ensure settings match:
   - **Require authentication**: No (for static files)
   - **Unauthenticated requests**: Allow anonymous requests
   - **Token store**: Enabled
   - **Identity provider**: Azure Active Directory configured

### Step 3: Check Static Web App Configuration (if applicable)
If using Azure Static Web Apps instead of App Service:
1. Verify `staticwebapp.config.json` is in the root directory
2. Check that routes are properly configured
3. Ensure authentication settings allow anonymous access to static files

### Step 4: Test the Fix

#### Option A: Node.js Automated Testing (Recommended)
```bash
# Navigate to frontend directory
cd atomsec-app-frontend

# Run the enhanced test script
node test-manifest.js
```

#### Option B: curl Command Line Testing
```bash
# Test manifest.json with CORS headers
curl -I -H "Origin: https://app-atomsec-dev01.azurewebsites.net" \
     https://app-atomsec-dev01.azurewebsites.net/manifest.json

# Test manifest.json content
curl -s https://app-atomsec-dev01.azurewebsites.net/manifest.json

# Test with verbose output to see redirects
curl -v -H "Origin: https://app-atomsec-dev01.azurewebsites.net" \
     https://app-atomsec-dev01.azurewebsites.net/manifest.json
```

Expected successful response headers:
```
HTTP/1.1 200 OK
Access-Control-Allow-Origin: *
Content-Type: application/json
```

#### Option C: Browser Network Tab Testing
1. **Direct URL Test**: Visit `https://app-atomsec-dev01.azurewebsites.net/manifest.json`
   - Should return JSON content, not redirect to login
   
2. **Browser Console Test**: 
   - Open browser developer tools
   - Check Network tab for manifest.json request
   - Verify no CORS errors in console

### Step 5: Clear Browser Cache
After deployment, users should:
1. Clear browser cache or test in incognito/private window
2. Hard refresh the application (Ctrl+F5 or Cmd+Shift+R)

## Expected Results

✅ **Success Indicators:**
- Manifest.json loads without authentication redirect
- No CORS errors in browser console
- PWA features work correctly
- Static assets load normally

❌ **Failure Indicators:**
- 302 redirects to `login.windows.net`
- CORS policy errors in console
- Manifest.json returns HTML login page instead of JSON

## Troubleshooting

### If Issues Persist:

1. **Check Azure App Service Logs**
   ```bash
   az webapp log tail --name app-atomsec-dev01 --resource-group [resource-group-name]
   ```

2. **Verify File Deployment**
   - Ensure all modified files are deployed to Azure
   - Check file timestamps in Azure Kudu console

3. **Authentication Configuration**
   - Azure Portal → App Service → Authentication
   - Verify configuration matches `authentication.json`
   - Check if any App Service settings override file configuration

4. **Network Investigation**
   ```bash
   # Test with verbose output
   curl -v -H "Origin: https://app-atomsec-dev01.azurewebsites.net" \
        https://app-atomsec-dev01.azurewebsites.net/manifest.json
   ```

5. **Static Web App vs App Service**
   - Confirm deployment target (Static Web App vs App Service)
   - Use appropriate configuration file:
     - Static Web App: `staticwebapp.config.json`
     - App Service: `web.config` + `authentication.json`

### Emergency Fallback

If the issue persists and is blocking production:

1. **Temporary Disable Authentication** (ONLY for testing):
   ```json
   // In authentication.json
   {
     "platform": {
       "enabled": false
     }
   }
   ```

2. **Move Static Files to CDN**:
   - Upload static assets to Azure CDN
   - Update references to use CDN URLs
   - CDN serves files without authentication

## Post-Deployment Verification Checklist

- [ ] Manifest.json accessible without authentication
- [ ] No CORS errors in browser console
- [ ] PWA installation prompt works
- [ ] All static assets load correctly
- [ ] User authentication still works for protected routes
- [ ] Test script passes all checks

## Files in This Fix

1. `staticwebapp.config.json` - Static Web App routing and authentication
2. `authentication.json` - Azure App Service authentication configuration  
3. `web.config` - IIS/App Service static file handling
4. `test-manifest.js` - Testing and verification script
5. `CORS_FIX_DEPLOYMENT_INSTRUCTIONS.md` - This deployment guide

## Contact

If deployment issues persist, check:
1. Azure Portal authentication settings
2. App Service logs
3. Network trace in browser developer tools
4. Compare with working environment configurations

**Note**: This fix addresses the specific issue where Azure App Service authentication was intercepting static file requests that should be publicly accessible.
