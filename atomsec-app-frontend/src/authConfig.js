/**
 * Configuration for Azure AD Authentication
 *
 * This file contains the configuration settings for Azure AD authentication.
 * Always uses Azure APIM for authentication and API calls.
 *
 * For Web app type registrations, the authentication flow is handled by APIM,
 * which exchanges the authorization code for tokens and validates JWT tokens.
 */

// Always prefer APIM; for specific endpoints we may provide a localhost fallback for dev
const isProduction = window.location.hostname !== 'localhost';
const isLocalhost = window.location.hostname === 'localhost';

// Base URL for API calls - Always use APIM
export const API_BASE_URL = process.env.REACT_APP_APIM_BASE_URL || '**************************************/db';

// API version (used for APIM)
export const API_VERSION = process.env.REACT_APP_API_VERSION || 'v1';

// APIM subscription key for authentication (required for all environments)
export const APIM_SUBSCRIPTION_KEY = process.env.REACT_APP_APIM_SUBSCRIPTION_KEY || 'bd50cc1018444ae987a04c465534e428';

// Helper function to get the correct URL - Always use APIM
const getServiceUrl = (endpoint = '') => {
  // Always use APIM with version
  const baseUrl = API_BASE_URL;
  const version = API_VERSION;
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${baseUrl}/${version}/${cleanEndpoint}`;
};

// Azure AD configuration
export const azureConfig = {
  clientId: process.env.REACT_APP_AZURE_CLIENT_ID,
  tenantId: process.env.REACT_APP_AZURE_TENANT_ID,
  // Use the correct redirect URI based on the environment
  redirectUri: isProduction
    ? (process.env.REACT_APP_REDIRECT_URI || "https://app-atomsec-dev01.azurewebsites.net/.auth/login/aad/callback")
    : (process.env.REACT_APP_REDIRECT_URI || "http://localhost:3000"),
  scopes: ["openid", "profile", "email", "User.Read"],
};

// Microsoft Graph API endpoints
export const graphConfig = {
  graphMeEndpoint: "https://graph.microsoft.com/v1.0/me",
};

// Authentication endpoints - Supports both local and APIM
export const authEndpoints = {
  login: getServiceUrl('/auth/login'),
  // Use DB function app endpoints in local dev to avoid APIM CORS during auth
  azureLogin: isLocalhost ? 'http://localhost:7072/api/db/auth/azure/login' : getServiceUrl('/auth/azure/login'),
  azureCallback: isLocalhost ? 'http://localhost:7072/api/db/auth/azure/callback' : getServiceUrl('/auth/azure/callback'),
  azureMe: isLocalhost ? 'http://localhost:7072/api/db/auth/azure/me' : getServiceUrl('/auth/azure/me'),
  // For dev, call the local function host directly for discovery until APIM route exists
  // host.json sets routePrefix to "/api/db", so include that in local fallback
  ssoDiscover: isLocalhost
    ? (process.env.REACT_APP_DB_FUNC_BASE || 'http://localhost:7072/api/db') + '/sso/discover'
    : getServiceUrl('/sso/discover'),
  verifyLogin: getServiceUrl('/users/login/verify'),
  // Azure-specific endpoints for backward compatibility
  azure: {
    login: isLocalhost ? 'http://localhost:7072/api/db/auth/azure/login' : getServiceUrl('/auth/azure/login'),
    callback: isLocalhost ? 'http://localhost:7072/api/db/auth/azure/callback' : getServiceUrl('/auth/azure/callback'),
    me: isLocalhost ? 'http://localhost:7072/api/db/auth/azure/me' : getServiceUrl('/auth/azure/me'),
  }
};

// Legacy configuration for backward compatibility
// This can be removed once all components are updated to use authEndpoints
export const azureAuthConfig = {
  loginEndpoint: authEndpoints.azure.login,
  callbackEndpoint: authEndpoints.azure.callback,
  meEndpoint: authEndpoints.azure.me,
  tokenRefreshEndpoint: authEndpoints.verifyLogin,
};
