@import './settings-risk-table.css';

/* Main content area */
.figma-main-content {
  padding: 32px;
  background: #fff;
  min-width: 1136px;
}

/* Figma-styled profiles tab */
.profiles-permissions-tab.figma-styled {
  background: #FFFFFF;
  border-radius: 4px;
  padding: 24px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
}

/* Charts row styling to match Figma exactly */
.figma-charts-row {
  display: flex;
  gap: 19px;
  align-items: stretch;
  margin-bottom: 24px;
  justify-content: flex-start;
}

/* Ensure proper alignment and spacing for both cards */
.figma-charts-row > * {
  flex-shrink: 0;
}

/* Risky Permissions Card - matches Figma exactly */
.figma-risky-permissions-card {
  background: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  min-width: 400px;
}

.figma-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
  width: 100%;
  margin-bottom: 16px;
}

/* Title and Picklist Row - matches Figma exactly */
.figma-title-picklist-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: nowrap !important;
  min-width: max-content;
  flex-shrink: 0;
}

/* Ensure title never wraps on smaller screens */
@media (max-width: 1440px) {
  .figma-card-title {
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    min-width: max-content !important;
  }
  
  .figma-title-picklist-row {
    flex-wrap: nowrap !important;
    min-width: max-content !important;
    flex-shrink: 0 !important;
  }
}

.figma-separator {
  color: #020A07;
  font-size: 18px;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
}

.figma-card-title {
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 700;
  color: #020A07;
  margin: 0;
  line-height: 1.6em;
  white-space: nowrap !important;
  flex-shrink: 0 !important;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: max-content;
}

/* View All button removed - no longer needed */

.figma-bubble-chart-container {
  height: 311px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}

/* Bubble Chart Picklist - now directly in controls */

.figma-bubble-select {
  padding: 6px 10px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  background: #FFFFFF;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #020A07;
  cursor: pointer;
  min-width: 180px;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%2351D59C%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.4-12.8z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 10px auto;
  padding-right: 28px;
  margin-top: -20px;
}

.figma-bubble-select:hover {
  border-color: #51D59C;
}

.figma-bubble-select:focus {
  outline: none;
  border-color: #51D59C;
  box-shadow: 0 0 0 2px rgba(81, 213, 156, 0.2);
}

/* Bubble Chart Controls Layout */
.figma-bubble-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* Reset Button for Bubble Selections */
.figma-bubble-reset-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  background: #FFFFFF;
  border: 1px solid #51D59C;
  border-radius: 4px;
  color: #020A07;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 100px;
  margin-top: -20px;
}

.figma-bubble-reset-btn:hover:not(:disabled) {
  background: #51D59C;
  color: #FFFFFF;
}

.figma-bubble-reset-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: #ccc;
  color: #999;
}

.figma-bubble-reset-btn svg {
  width: 16px;
  height: 16px;
  color: inherit;
}

/* Assignment Graph Card - matches Figma exactly */
.figma-assignment-graph-card {
  background: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 24px;
  width: 400px;
  height: 397px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex-shrink: 0;
}

.figma-assignment-graph-card .figma-card-header {
  margin-bottom: 8px;
}

.figma-assignment-graph-card .figma-card-title {
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #020A07;
  margin: 0;
  line-height: 1.6em;
}

.figma-pie-chart-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  min-height: 264px;
  position: relative;
}

/* Pie Chart specific styling to match Figma exactly */
.figma-pie-chart-container canvas {
  max-width: 100%;
  max-height: 100%;
}

/* Ensure the Assignment Graph card maintains exact Figma dimensions */
@media (max-width: 1440px) {
  .figma-assignment-graph-card {
    width: 400px !important;
    height: auto;
    flex-shrink: 0 !important;
  }
}

/* Chart.js Pie Chart specific overrides to match Figma exactly */
.figma-pie-chart-container .chartjs-render-monitor {
  width: 100% !important;
  height: 100% !important;
}

.figma-pie-chart-container .chartjs-tooltip {
  background: rgba(0, 0, 0, 0.8) !important;
  border-radius: 4px !important;
  color: white !important;
  font-family: 'Lato', sans-serif !important;
  font-size: 14px !important;
}

/* Security Findings Section - matches Figma exactly */
.figma-security-findings-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.figma-security-findings-header {
  display: flex;
  flex-direction: column;
  gap: 2px;
  width: 1088px;
}

.figma-security-findings-title {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 700;
  color: #020A07;
  margin: 0;
  line-height: 1.6em;
}

/* Filter Controls - matches Figma exactly */
.figma-filter-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 1088px;
  height: 42px;
}

.figma-filter-group {
  display: flex;
  align-items: center;
  gap: 16px;
}

.figma-filter-label {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #393E3C;
  line-height: 1.6em;
}

.figma-filter-dropdown {
  position: relative;
  width: 280px;
  height: 42px;
}

.figma-filter-select {
  width: 100%;
  height: 100%;
  padding: 8px 12px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  background: #FFFFFF;
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #020A07;
  appearance: none;
  cursor: pointer;
}

.figma-filter-dropdown svg {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #51D59C;
  pointer-events: none;
}

.figma-reset-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 24px;
  background: #FFFFFF;
  border: 1px solid #51D59C;
  border-radius: 4px;
  color: #020A07;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.figma-reset-btn svg {
  width: 24px;
  height: 24px;
  color: #020A07;
}

/* Security Settings Table - matches Figma exactly */
.figma-security-settings-table {
  width: 100%;
}

.figma-table {
  width: 100%;
  border-collapse: collapse;
}

.figma-table-header {
  background: rgba(0, 0, 0, 0.04);
  text-align: left;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1D2433;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-bottom: none;
}

.figma-table-header:first-child {
  width: 320px;
}

.figma-table-header:nth-child(2) {
  width: 280px;
}

.figma-table-header:nth-child(3) {
  width: 240px;
}

.figma-table-header:nth-child(4) {
  width: 160px;
}

.figma-table-header:nth-child(5) {
  width: 40px;
}

.figma-table-row {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.figma-table-cell {
  padding: 14px 8px 14px 16px;
  vertical-align: top;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-bottom: none;
  border-right: none;
}

.figma-table-cell:first-child {
  width: 320px;
}

.figma-table-cell:nth-child(2) {
  width: 280px;
}

.figma-table-cell:nth-child(3) {
  width: 240px;
}

.figma-table-cell:nth-child(4) {
  width: 160px;
}

.figma-table-cell:nth-child(5) {
  width: 40px;
}

.figma-setting-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.figma-setting-name svg {
  width: 16px;
  height: 16px;
  color: #666666;
  flex-shrink: 0;
}

.figma-org-value {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.figma-org-value svg {
  width: 16px;
  height: 16px;
  color: #D32F2F;
  flex-shrink: 0;
  margin-top: 2px;
}

.figma-more-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #155B55;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

/* Divider Line */
.figma-divider-line {
  width: 1082px;
  height: 0;
  border: 1px solid rgba(0, 0, 0, 0.12);
  margin: 24px 0;
}

/* Roles and Permissions Section - matches Figma exactly */
.figma-roles-permissions-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.figma-roles-permissions-header {
  display: flex;
  flex-direction: column;
  gap: 2px;
  width: 1088px;
}

.figma-roles-permissions-title {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 700;
  color: #020A07;
  margin: 0;
  line-height: 1.6em;
}

.figma-roles-permissions-table {
  width: 100%;
}

/* Checkbox Styling - matches Figma exactly */
.figma-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.figma-checkbox input[type="checkbox"] {
  width: 20px;
  height: 20px;
  margin: 0;
  cursor: pointer;
}

.figma-checkbox.checked input[type="checkbox"] {
  accent-color: #51D59C;
}

/* Section padding for existing content */
.figma-section-padding {
  padding: 0 25px;
}

/* Figma Table Container Styling - matches Figma exactly */
.figma-table-container {
  width: 100%;
  background: #FFFFFF;
  border-radius: 4px;
  padding: 24px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
}

/* Table Header Section - matches Figma exactly */
.figma-table-header-section {
  margin-bottom: 16px;
}

.figma-table-title {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 700;
  color: #020A07;
  margin: 0;
  line-height: 1.6em;
}

/* Table Controls - matches Figma exactly */
.figma-table-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.figma-export-btn {
  background: #F8FDFB;
  border: 1px solid #51D59C;
  color: #51D59C;
  border-radius: 6px;
  padding: 8px 20px;
  font-weight: 600;
  cursor: pointer;
  min-width: 150px;
  text-align: center;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  transition: all 0.2s;
}

.figma-export-btn:hover {
  background: #51D59C;
  color: #FFFFFF;
}

.figma-filter-mode-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.figma-filter-mode-label {
  color: #393E3C;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  font-family: 'Lato', sans-serif;
}

.figma-filter-mode-select {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #51D59C;
  background: #F8FDFB;
  color: #51D59C;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  min-width: 200px;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%2351D59C%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.4-12.8z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 12px auto;
  padding-right: 32px;
  font-family: 'Lato', sans-serif;
}

.figma-reset-filters-btn {
  padding: 8px 16px;
  background: #51D59C;
  border: none;
  border-radius: 6px;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Lato', sans-serif;
  transition: all 0.2s;
}

.figma-reset-filters-btn:hover {
  background: #45c28f;
}

/* AG Grid Container with Figma styling */
.figma-ag-grid-container {
  height: 500px;
  min-height: 400px;
  width: 100%;
  border-radius: 4px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.12);
}

/* AG Grid Theme Overrides for Figma look */
.figma-ag-grid-container .ag-theme-quartz {
  --ag-background-color: #fff;
  --ag-header-background-color: rgba(0, 0, 0, 0.04);
  --ag-header-foreground-color: #1D2433;
  --ag-row-hover-color: #f8fdfb;
  --ag-font-family: 'Lato', sans-serif;
  --ag-border-radius: 4px;
  --ag-card-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  border: none;
  border-radius: 4px;
  overflow: visible;
  box-sizing: border-box;
}

.figma-ag-grid-container .ag-theme-quartz .ag-header-cell-label {
  font-weight: 600;
  font-size: 14px;
  color: #1D2433;
}

.figma-ag-grid-container .ag-theme-quartz .ag-cell {
  border-radius: 0;
  font-size: 14px;
  color: #020A07;
}

.figma-ag-grid-container .ag-theme-quartz .ag-root-wrapper {
  border-radius: 4px;
  overflow: visible;
  border: none;
}

/* Enhanced AG Grid Styling for Figma Design */
.figma-ag-grid-container .ag-theme-quartz .ag-header-cell,
.figma-ag-grid-container .ag-theme-quartz .ag-header-group-cell {
  background: #F5F7FA !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  border-bottom: none !important;
  color: #1D2433 !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  font-family: 'Lato', sans-serif !important;
  padding: 14px 8px 14px 16px !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-cell {
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  border-bottom: none !important;
  border-right: none !important;
  padding: 14px 8px 14px 16px !important;
  font-size: 14px !important;
  color: #020A07 !important;
  font-family: 'Lato', sans-serif !important;
  vertical-align: middle !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-row {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08) !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-row-hover {
  background: #f8fdfb !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-paging-panel {
  height: 60px !important;
  padding: 12px !important;
  border-top: 1px solid #E0E0E0 !important;
  background: #fff !important;
  font-family: 'Lato', sans-serif !important;
}

/* Column Width Specifications */
.figma-ag-grid-container .ag-theme-quartz .ag-header-cell:first-child,
.figma-ag-grid-container .ag-theme-quartz .ag-cell:first-child {
  width: 320px !important;
  min-width: 320px !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-header-cell:nth-child(2),
.figma-ag-grid-container .ag-theme-quartz .ag-cell:nth-child(2) {
  width: 280px !important;
  min-width: 280px !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-header-cell:nth-child(3),
.figma-ag-grid-container .ag-theme-quartz .ag-cell:nth-child(3) {
  width: 240px !important;
  min-width: 240px !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-header-cell:nth-child(4),
.figma-ag-grid-container .ag-theme-quartz .ag-cell:nth-child(4) {
  width: 160px !important;
  min-width: 160px !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-header-cell:nth-child(5),
.figma-ag-grid-container .ag-theme-quartz .ag-cell:nth-child(5) {
  width: 40px !important;
  min-width: 40px !important;
}

/* Remove Default AG Grid Styling */
.figma-ag-grid-container .ag-theme-quartz .ag-root-wrapper {
  border: none !important;
  border-radius: 4px !important;
  overflow: visible !important;
  box-shadow: none !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-root {
  border: none !important;
  border-radius: 4px !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-cell:focus {
  outline: none !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-header-cell:focus {
  outline: none !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
}

/* Custom Checkbox Styling for AG Grid */
.figma-ag-grid-container .ag-theme-quartz .ag-cell .ag-checkbox-input-wrapper {
  width: 20px !important;
  height: 20px !important;
  border: 1.5px solid #B7C2C7 !important;
  border-radius: 0 !important;
  background: #F5F7FA !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-cell .ag-checkbox-input-wrapper.ag-checked {
  background: #51D59C !important;
  border-color: #51D59C !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-cell .ag-checkbox-input-wrapper.ag-checked::after {
  content: '✓' !important;
  color: white !important;
  font-size: 14px !important;
  font-weight: bold !important;
  line-height: 1 !important;
}

/* Text alignment for specific columns */
.figma-ag-grid-container .ag-theme-quartz .ag-cell:first-child {
  text-align: left !important;
  font-weight: 600 !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-cell:not(:first-child) {
  text-align: center !important;
}

/* Header text alignment */
.figma-ag-grid-container .ag-theme-quartz .ag-header-cell:first-child {
  text-align: left !important;
}

.figma-ag-grid-container .ag-theme-quartz .ag-header-cell:not(:first-child) {
  text-align: center !important;
}

/* Card styling */
.figma-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);
  padding: 40px 32px 32px 32px;
  margin-bottom: 24px;
  overflow: hidden;
}

.figma-card .ag-root-wrapper {
  border-radius: 16px;
  overflow: hidden;
}

/* AG Grid overrides for Figma look */
.ag-theme-quartz {
  --ag-background-color: #fff;
  --ag-header-background-color: #fff;
  --ag-header-foreground-color: #222;
  --ag-row-hover-color: #f5f7fa;
  --ag-font-family: 'Inter', sans-serif;
  --ag-border-radius: 12px;
  --ag-card-shadow: 0px 4px 20px 0px rgba(0,0,0,0.1);
  font-size: 15px;
  border: 1.5px solid #E0E0E0;
  border-radius: 12px;
  overflow: visible;
  box-sizing: border-box;
}

.ag-theme-quartz .ag-header-cell-label {
  font-weight: 600;
  font-size: 16px;
}

.ag-theme-quartz .ag-cell {
  border-radius: 4px;
}

.ag-theme-quartz .ag-root-wrapper {
  border-radius: 12px;
  overflow: visible;
  border: none;
}

.bubble-chart-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 24px;
  justify-content: left;
  align-items: stretch;
  width: 100%;
  min-height: 0;
  margin-left: auto;
  margin-right: auto;
}

.bubble-chart-card, .assignment-chart-card {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0px 4px 20px 0px rgba(0,0,0,0.1);
  padding: 24px;
  min-width: 320px;
  min-height: 320px;
  margin-bottom: 24px;
}

.bubble-chart-card-inner {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.bubble-chart-title {
  font-weight: 900;
  font-size: 20px;
  margin-bottom: 12px;
  text-align: center;
}

.bubble-chart-svg {
  width: 100%;
  max-width: 100%;
  height: 360px !important;
  display: flex;
  justify-content: center;
  overflow: visible !important;
}

.bubble-chart-svg text {
  white-space: pre;
  text-anchor: middle;
  dominant-baseline: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.highlight-row {
  background: #e6f7f0 !important;
}

.bubble-chart-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.figma-dropdown {
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 8px 32px 8px 16px;
  font-size: 16px;
  color: #393E3C;
  background: #fff;
  appearance: none;
  outline: none;
  min-width: 180px;
  margin-right: 8px;
}

.settings-risk-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0px 4px 20px 0px rgba(0,0,0,0.1);
  font-size: 15px;
  margin-top: 0;
  overflow: hidden;
}
.settings-risk-table th, .settings-risk-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #F0F0F0;
  text-align: left;
}
.settings-risk-table th {
  background: #F8FDFB;
  color: #393E3C;
  font-weight: 600;
}
.settings-risk-table tr:last-child td {
  border-bottom: none;
}
.settings-risk-table td a {
  color: #51D59C;
  text-decoration: underline;
  font-weight: 500;
  transition: color 0.2s;
}
.settings-risk-table td a:hover {
  color: #393E3C;
}

@media (max-width: 1000px) {
  .bubble-chart-card {
    max-width: 100%;
    min-width: 90vw;
    flex: 1 1 100%;
  }
  .bubble-chart-row {
    flex-direction: column;
    gap: 16px;
  }
}

.bubble-chart-section,
.assignment-chart-section {
  border: 1.5px solid #E0E0E0;
  border-radius: 12px;
  overflow: visible !important;
}

.figma-picklist-row {
  position: relative;
  display: flex;
  align-items: center;
  gap: 4px;
  width: fit-content;
  height: fit-content;
  margin-bottom: 8px;
}

.figma-dropdown-container {
  position: relative;
  display: inline-block;
}

.figma-dropdown-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  pointer-events: none;
  z-index: 1;
}

.figma-section-padding {
  padding-left: 32px;
  padding-right: 32px;
}

.assignment-donut-card {
  background: #fff;
  border: 1.5px solid #E0E0E0;
  border-radius: 12px;
  padding: 32px;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0;
  min-width: 320px;
  min-height: unset;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  box-shadow: 0 1.5px 4px rgba(0,0,0,0.06);
  margin: 25px 0 25px 0;
}
.assignment-donut-chart-container {
  flex: 1;
  min-height: 260px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.assignment-donut-title {
  font-weight: 700;
  font-size: 28px;
  color: #181C1A;
  margin-bottom: 24px;
  text-align: left;
} 