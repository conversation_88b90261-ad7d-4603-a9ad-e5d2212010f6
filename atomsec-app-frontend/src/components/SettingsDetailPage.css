/* Figma Design Implementation for Settings Detail Page */

/* Main Content Layout */
.figma-breadcrumbs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  padding: 32px 32px 0 32px;
}

.figma-breadcrumb-link {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.6em;
  color: #020A07;
  text-decoration: none;
}

.figma-breadcrumb-link:hover {
  color: #51D59C;
}

.figma-breadcrumb-icon {
  width: 16px;
  height: 16px;
}

/* Loading and Error States */
.figma-loading {
  color: #666666;
  padding: 32px;
  text-align: center;
  font-family: 'Lato', sans-serif;
  font-size: 16px;
}

.figma-error {
  color: #D32F2F;
  padding: 32px;
  text-align: center;
  font-family: 'Lato', sans-serif;
  font-size: 16px;
}

/* Table - EXACT SAME STRUCTURE AS SETTINGS-RISK-TABLE */
.figma-table {
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-x: auto; /* Add horizontal scroll */
  max-width: 100%; /* Ensure table doesn't exceed container */
  box-sizing: border-box; /* Include padding in width calculation */
}

/* Table Header - EXACT SAME AS SETTINGS-RISK-TABLE */
.figma-table-header {
  display: flex;
  background: #DEE2E6;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  min-width: 800px; /* Ensure minimum width for all columns */
  width: 100%;
}

.figma-table-header-cell {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  padding: 14px 8px 14px 16px !important;
  font-family: 'Lato', sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 1.6em !important;
  color: #393E3C !important;
  border-right: 1px solid #ADB5BD !important;
  flex-shrink: 0 !important;
  background: #DEE2E6 !important;
  justify-content: flex-start !important;
  min-height: 120px !important;
}

.figma-table-header-cell:last-child {
  border-right: none;
}

/* Column Widths - EXACT SAME AS SETTINGS-RISK-TABLE */
.figma-profile-name-col {
  flex: 0.8; /* Reduced from 1 */
  min-width: 160px; /* Reduced from 200px */
  color: #1D2433;
}

.figma-org-value-col {
  flex: 1; /* Reduced from 1.2 */
  min-width: 180px; /* Reduced from 220px */
}

.figma-standard-value-col {
  flex: 1.2; /* Reduced from 1.5 */
  min-width: 200px; /* Reduced from 250px */
}

.figma-owasp-category-col {
  flex: 0.8; /* Reduced from 1 */
  min-width: 160px; /* Reduced from 200px */
}

.figma-risk-col {
  flex: 0.4; /* Reduced from 0.5 */
  min-width: 80px; /* Reduced from 100px */
}

/* Table Body - EXACT SAME AS SETTINGS-RISK-TABLE */
.figma-table-body {
  background: #FFFFFF;
  width: 100%;
  min-width: 800px; /* Ensure minimum width for all columns */
}

.figma-table-row {
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  min-height: 120px;
  align-items: stretch;
  width: 100%;
}

.figma-table-row:last-child {
  border-bottom: none;
}

.figma-table-row:hover {
  background: #F8FDFB;
}

.figma-table-cell {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  padding: 14px 8px 14px 16px;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.6em;
  color: #020A07;
  flex-shrink: 0;
  overflow: hidden;
}

.figma-table-cell:last-child {
  border-right: none;
}

/* Ensure table cells match header widths exactly - EXACT SAME AS SETTINGS-RISK-TABLE */
.figma-table-cell.figma-profile-name-col {
  flex: 0.8; /* Reduced from 1 */
  min-width: 160px; /* Reduced from 200px */
}

.figma-table-cell.figma-org-value-col {
  flex: 1; /* Reduced from 1.2 */
  min-width: 180px; /* Reduced from 220px */
}

.figma-table-cell.figma-standard-value-col {
  flex: 1.2; /* Reduced from 1.5 */
  min-width: 200px; /* Reduced from 250px */
}

.figma-table-cell.figma-owasp-category-col {
  flex: 0.8; /* Reduced from 1 */
  min-width: 160px; /* Reduced from 200px */
}

.figma-table-cell.figma-risk-col {
  flex: 0.4; /* Reduced from 0.5 */
  min-width: 80px; /* Reduced from 100px */
}

/* Profile Name Column */
.figma-profile-name {
  color: #020A07;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.6em;
}

/* Org Value Column */
.figma-org-value-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  height: 100%;
}

.figma-warning-icon {
  color: #D32F2F;
  flex-shrink: 0;
  margin-top: 2px;
}

.figma-org-value-text {
  color: #1D2433;
  line-height: 1.4em;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.figma-setting-name {
  color: #1D2433;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4em;
}

.figma-equals {
  color: #666666;
  font-size: 14px;
  line-height: 1.4em;
}

.figma-value {
  color: #020A07;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4em;
}

.figma-risk-value {
  color: #D32F2F;
  font-weight: 500;
}

.figma-issue-details {
  color: #D32F2F;
  font-size: 12px;
  line-height: 1.4em;
  font-style: italic;
  margin-top: 4px;
}

/* Standard Value Column */
.figma-standard-value-text {
  color: #1D2433;
  line-height: 1.4em;
  width: 100%;
}

/* OWASP Category Column */
.figma-owasp-category-text {
  color: #1D2433;
  line-height: 1.4em;
  width: 100%;
}

/* Risk Column */
.figma-risk-text {
  color: #1D2433;
  line-height: 1.6em;
  width: 100%;
  text-align: center;
}

/* Pagination */
.figma-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 24px 32px 32px 32px;
  gap: 16px;
}

.figma-pagination-btn {
  background: #FFFFFF;
  color: #020A07;
  border: 1px solid rgba(0, 0, 0, 0.08);
  padding: 8px 16px;
  border-radius: 4px;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.figma-pagination-btn:hover:not(:disabled) {
  background: #F8FDFB;
  border-color: #51D59C;
}

.figma-pagination-btn:disabled {
  background: #F1F1F1;
  color: #999999;
  cursor: not-allowed;
}

.figma-pagination-info {
  font-size: 14px;
  color: #666666;
  font-family: 'Lato', sans-serif;
  font-weight: 400;
}

/* Responsive Design - EXACT SAME AS SETTINGS-RISK-TABLE */
@media (max-width: 1200px) {
  .figma-table {
    margin: 0 16px 16px 16px;
  }
  
  .figma-breadcrumbs {
    padding: 16px 16px 0 16px;
  }
  
  .figma-pagination {
    margin: 24px 16px 16px 16px;
  }
  
  .figma-table-header,
  .figma-table-row {
    overflow-x: auto;
  }
  
  .figma-table-header-cell,
  .figma-table-cell {
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .figma-table {
    margin: 0 8px 8px 8px;
  }
  
  .figma-breadcrumbs {
    padding: 8px 8px 0 8px;
    flex-direction: column;
    gap: 4px;
  }
  
  .figma-pagination {
    margin: 16px 8px 8px 8px;
    flex-direction: column;
    gap: 12px;
  }
  
  .figma-table-header,
  .figma-table-row {
    flex-direction: column;
  }
  
  .figma-table-header-cell,
  .figma-table-cell {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  }
  
  .figma-table-header-cell:last-child,
  .figma-table-cell:last-child {
    border-bottom: none;
  }
} 

.figma-main-content {
  padding: 32px;
  background: #F8FDFB;
  min-height: 100vh;
  overflow: hidden; /* Prevent any overflow from child elements */
  box-sizing: border-box;
} 