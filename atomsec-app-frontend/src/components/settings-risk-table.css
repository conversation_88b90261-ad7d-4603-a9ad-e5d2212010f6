/* Security Findings Table Styles - Figma Design */

/* Security Findings Title */
.figma-security-findings-title {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 20px;
  line-height: 1.6em;
  color: #020A07;
  margin-bottom: 16px;
}

/* Filter Section */
.figma-filter-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  width: 100%;
  height: 42px;
}

.figma-filter-label {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.6em;
  color: #393E3C;
}

.figma-filter-dropdown {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  cursor: pointer;
  min-width: 200px;
  position: relative;
}

.figma-filter-dropdown:hover {
  border-color: #51D59C;
}

.figma-filter-text {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.6em;
  color: #020A07;
}

.figma-reset-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 24px;
  background: #FFFFFF;
  border: 1px solid #51D59C;
  border-radius: 4px;
  color: #020A07;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.6em;
  cursor: pointer;
  transition: all 0.2s ease;
}

.figma-reset-button:hover {
  background: #F8FDFB;
}

.figma-reset-button svg {
  color: #020A07;
}

/* Security Table */
.figma-security-table {
  width: 100%;
  max-width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  overflow: hidden;
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
}

/* Table Header */
.figma-table-header {
  display: flex !important;
  background: #DEE2E6 !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08) !important;
  width: 100% !important;
  min-height: 120px !important;
  align-items: stretch !important;
}

.figma-table-header-cell {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  padding: 14px 8px 14px 16px !important;
  font-family: 'Lato', sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 1.6em !important;
  color: #393E3C !important;
  border-right: 1px solid #ADB5BD !important;
  flex-shrink: 0 !important;
  background: #DEE2E6 !important;
  justify-content: flex-start !important;
  min-height: 120px !important;
}

.figma-table-header-cell:last-child {
  border-right: none;
}

.figma-setting-name-col {
  flex: 1;
  min-width: 200px;
  color: #1D2433;
}

.figma-org-value-col {
  flex: 2;
  min-width: 300px;
}

.figma-standard-value-col {
  flex: 1.5;
  min-width: 250px;
}

.figma-owasp-category-col {
  flex: 1;
  min-width: 200px;
}

/* Table Body */
.figma-table-body {
  background: #FFFFFF;
  width: 100%;
}

.figma-table-row {
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  min-height: 120px;
  align-items: stretch;
  width: 100%;
}

.figma-table-row:last-child {
  border-bottom: none;
}

.figma-table-row:hover {
  background: #F8FDFB;
}

.figma-table-cell {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  padding: 14px 8px 14px 16px;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.6em;
  color: #020A07;
  flex-shrink: 0;
  overflow: hidden;
}

.figma-table-cell:last-child {
  border-right: none;
}

/* Ensure table cells match header widths exactly */
.figma-table-cell.figma-setting-name-col {
  flex: 1;
  min-width: 200px;
}

.figma-table-cell.figma-org-value-col {
  flex: 2;
  min-width: 300px;
}

.figma-table-cell.figma-standard-value-col {
  flex: 1.5;
  min-width: 250px;
}

.figma-table-cell.figma-owasp-category-col {
  flex: 1;
  min-width: 200px;
}

/* Setting Name Column */
.figma-setting-name {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #020A07;
  white-space: nowrap;
  width: auto;
}

.figma-info-icon {
  color: #666666;
  font-size: 14px;
  font-weight: bold;
  cursor: help;
  margin-left: 6px;
  flex-shrink: 0;
}

/* Org Value Column */
.figma-org-value-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  height: 100%;
}

.figma-warning-icon {
  color: #D32F2F;
  flex-shrink: 0;
  margin-top: 2px;
}

.figma-org-value-text {
  color: #1D2433;
  line-height: 1.4em;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.figma-org-value-main {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.figma-view-link {
  color: #1D2433;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.figma-view-link:hover {
  color: #51D59C;
}

/* Standard Value Column */
.figma-standard-value-text {
  color: #1D2433;
  line-height: 1.4em;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  width: 100%;
}

/* OWASP Category Column */
.figma-owasp-category-text {
  color: #1D2433;
  line-height: 1.4em;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.figma-owasp-item {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  white-space: normal;
  overflow: visible;
  text-overflow: unset;
}

/* Actions Column */
.figma-more-actions {
  color: #155B55;
  cursor: pointer;
  flex-shrink: 0;
}

.figma-more-actions:hover {
  color: #51D59C;
}

/* Pagination */
.figma-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
  gap: 16px;
}

.figma-pagination-btn {
  padding: 6px 16px;
  border-radius: 4px;
  border: 1px solid #E0E0E0;
  background: #FFFFFF;
  color: #393E3C;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.figma-pagination-btn:hover:not(:disabled) {
  background: #F8FDFB;
  border-color: #51D59C;
}

.figma-pagination-btn:disabled {
  background: #F1F1F1;
  color: #999999;
  cursor: not-allowed;
}

.figma-pagination-info {
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #393E3C;
}

/* Filter Select Styling */
.figma-filter-select {
  background: transparent;
  border: none;
  padding: 0;
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.6em;
  color: #020A07;
  cursor: pointer;
  outline: none;
  width: 100%;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .figma-filter-section,
  .figma-security-table {
    width: 100%;
  }
  
  .figma-table-header,
  .figma-table-row {
    overflow-x: auto;
  }
  
  .figma-table-header-cell,
  .figma-table-cell {
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .figma-filter-section {
    flex-direction: column;
    align-items: flex-start;
    height: auto;
    gap: 12px;
  }
  
  .figma-filter-dropdown {
    width: 100%;
    max-width: 280px;
  }
  
  .figma-table-header,
  .figma-table-row {
    flex-direction: column;
  }
  
  .figma-table-header-cell,
  .figma-table-cell {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  }
  
  .figma-table-header-cell:last-child,
  .figma-table-cell:last-child {
    border-bottom: none;
  }
} 