import React, { useEffect, useState } from 'react';
import './SettingsDetailPage.css';
import { fetchProfilesPermissions } from '../api';
import keyboardArrowRight from '../assets/icons/keyboard_arrow_right.svg';
import warningAmber from '../assets/icons/warning_amber.svg';
import moreVert from '../assets/icons/more_vert.svg';

export default function SettingsDetailPage({ orgId, taskType, executionLogId, onBack }) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Add sorting state and logic
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const handleSort = (key) => {
    setSortConfig((prev) => {
      if (prev.key === key) {
        // Toggle direction
        return { key, direction: prev.direction === 'asc' ? 'desc' : 'asc' };
      }
      return { key, direction: 'asc' };
    });
  };

  useEffect(() => {
    setLoading(true);
    setError(null);
    setData(null);
    fetchProfilesPermissions(orgId, executionLogId, taskType)
      .then(json => {
        setData(json);
        setLoading(false);
      })
      .catch(err => {
        setError('Failed to fetch details');
        setLoading(false);
      });
  }, [orgId, taskType, executionLogId]);

  // Extract all discrepancies (flattened)
  let rows = [];
  if (data && data.policies) {
    data.policies.forEach(policy => {
      let profileName = policy.ProfileName || policy.profileName || policy.RowKey || '';
      if (!profileName && policy.RowKey && policy.RowKey.includes('-')) {
        profileName = policy.RowKey.split('-')[0];
      }
      
      // Special handling for Login IP Ranges
      if (taskType === 'login_ip_ranges') {
        try {
          const arr = JSON.parse(policy.OrgValue);
          arr.forEach(s => {
            rows.push({
              profileName,
              settingName: 'IP Range',
              orgValue: `${s.StartIP} - ${s.EndIP}`,
              standardValue: 'Restricted IP Range',
              owasp: 'A1: Broken Access Control, A6: Security Misconfiguration',
              risk: 'High',
              isRisk: true,
              issue: s.Issue,
              issueDetails: s.Issue // Store the full issue details
            });
          });
        } catch {}
      } else if (taskType === 'session_timeout') {
        try {
          const arr = JSON.parse(policy.OrgValue);
          arr.forEach(s => {
            rows.push({
              profileName,
              settingName: s.SalesforceSetting || 'Session Timeout',
              orgValue: s.OrgValue || s.orgValue || s.value || '-',
              standardValue: s.StandardValue || s.standardValue || s.standard || '30 minutes',
              owasp: s.OWASP || 'A7: Identification & Authentication Failures',
              risk: s.Risk || s.risk || 'Medium',
              isRisk: s.Issue && s.Issue.toLowerCase().includes('timeout') || 
                      s.issue && s.issue.toLowerCase().includes('timeout') ||
                      s.OrgValue > 30 || s.orgValue > 30 || s.value > 30,
              issueDetails: s.Issue || s.issue
            });
          });
        } catch {}
      } else if (taskType === 'login_hours') {
        try {
          const arr = JSON.parse(policy.OrgValue);
          arr.forEach(s => {
            rows.push({
              profileName,
              settingName: s.SalesforceSetting || 'Login Hours',
              orgValue: s.OrgValue || s.orgValue || s.value || '-',
              standardValue: s.StandardValue || s.standardValue || s.standard || '9 AM - 6 PM',
              owasp: s.OWASP || 'A5: Security Misconfiguration',
              risk: s.Risk || s.risk || 'Medium',
              isRisk: s.Issue && s.Issue.toLowerCase().includes('wide range') || 
                      s.issue && s.issue.toLowerCase().includes('wide range'),
              issueDetails: s.Issue || s.issue
            });
          });
        } catch {}
      } else if (taskType === 'api_whitelisting') {
        try {
          const arr = JSON.parse(policy.OrgValue);
          arr.forEach(s => {
            rows.push({
              profileName,
              settingName: s.SalesforceSetting || 'API Access',
              orgValue: s.OrgValue || s.orgValue || s.value || '-',
              standardValue: s.StandardValue || s.standardValue || s.standard || 'Restricted',
              owasp: s.OWASP || 'A5: Security Misconfiguration',
              risk: s.Risk || s.risk || 'High',
              isRisk: s.Issue && s.Issue.toLowerCase().includes('private ip') || 
                      s.issue && s.issue.toLowerCase().includes('private ip'),
              issueDetails: s.Issue || s.issue
            });
          });
        } catch {}
      } else {
        // Default handling for other task types
        try {
          const arr = JSON.parse(policy.OrgValue);
          arr.forEach(s => {
            rows.push({
              profileName,
              settingName: s.SalesforceSetting || s.settingName || 'Setting',
              orgValue: s.OrgValue || s.orgValue || s.value || '-',
              standardValue: s.StandardValue || s.standardValue || s.standard || 'Standard Value',
              owasp: s.OWASP || s.owasp || 'A7: Identification & Authentication Failures',
              risk: s.Risk || s.risk || 'Medium',
              isRisk: s.Issue && (s.Issue.toLowerCase().includes('missing') || 
                      s.Issue.toLowerCase().includes('discrepancy') || 
                      s.Issue.toLowerCase().includes('false')) || 
                      s.issue && (s.issue.toLowerCase().includes('missing') || 
                      s.issue.toLowerCase().includes('discrepancy') || 
                      s.issue.toLowerCase().includes('false')),
              issueDetails: s.Issue || s.issue
            });
          });
        } catch {}
      }
    });
  }

  // Instead of grouping by profile, flatten to one row per profile+setting, and show Setting Name = Value in Org/Standard columns
  const detailRows = rows.map(row => ({
    profileName: row.profileName,
    settingName: row.SalesforceSetting || row.settingName,
    orgValue: row.OrgValue || row.orgValue,
    standardValue: row.StandardValue || row.standardValue,
    owasp: row.OWASP || row.owasp,
    risk: row.Risk || row.risk || 'High',
    isRisk: row.Issue && row.Issue.toLowerCase().includes('missing') || 
            row.Issue && row.Issue.toLowerCase().includes('discrepancy') || 
            row.Issue && row.Issue.toLowerCase().includes('false') ||
            row.issue && row.issue.toLowerCase().includes('wide range') ||
            row.issue && row.issue.toLowerCase().includes('private ip') ||
            row.isRisk,
    issueDetails: row.issueDetails || row.Issue || row.issue
  }));

  // Sort detailRows based on sortConfig
  const sortedRows = React.useMemo(() => {
    if (!sortConfig.key) return detailRows;
    const sorted = [...detailRows].sort((a, b) => {
      const aVal = (a[sortConfig.key] || '').toString().toLowerCase();
      const bVal = (b[sortConfig.key] || '').toString().toLowerCase();
      if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
    return sorted;
  }, [detailRows, sortConfig]);

  // Pagination logic (10 rows per page)
  const [page, setPage] = useState(1);
  const perPage = 10;
  const totalPages = Math.ceil(sortedRows.length / perPage);
  const paginatedRows = sortedRows.slice((page - 1) * perPage, page * perPage);

  // Format task type for display
  const formatTaskType = (type) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
  };

  return (
    <>
      {/* Breadcrumbs */}
      <div className="figma-breadcrumbs">
        <button className="figma-breadcrumb-link" onClick={onBack}>
          <span>Back to Dashboard</span>
          <img src={keyboardArrowRight} alt=">" className="figma-breadcrumb-icon" />
        </button>
        <button className="figma-breadcrumb-link">
          <span>{formatTaskType(taskType)}</span>
          <img src={keyboardArrowRight} alt=">" className="figma-breadcrumb-icon" />
        </button>
      </div>

      {/* Main Content */}
      {loading && <div className="figma-loading">Loading...</div>}
      {error && <div className="figma-error">{error}</div>}
      {!loading && !error && (
        <>
          {/* Table */}
          <div className="figma-table">
            {/* Table Header */}
            <div className="figma-table-header" style={{ minHeight: '120px' }}>
              <div className="figma-table-header-cell figma-profile-name-col" style={{ minHeight: '120px', alignItems: 'flex-start' }}>
                Profile Name
              </div>
              <div className="figma-table-header-cell figma-org-value-col" style={{ minHeight: '120px', alignItems: 'flex-start' }}>
                Org Value
              </div>
              <div className="figma-table-header-cell figma-standard-value-col" style={{ minHeight: '120px', alignItems: 'flex-start' }}>
                Standard Value
              </div>
              <div className="figma-table-header-cell figma-owasp-category-col" style={{ minHeight: '120px', alignItems: 'flex-start' }}>
                OWASP Category
              </div>
              <div className="figma-table-header-cell figma-risk-col" style={{ minHeight: '120px', alignItems: 'flex-start' }}>
                Risk
              </div>
            </div>

            {/* Table Body */}
            <div className="figma-table-body">
              {paginatedRows.length === 0 && (
                <div className="figma-table-empty">
                  <div className="figma-table-row" style={{ minHeight: '120px' }}>
                    <div className="figma-table-cell figma-profile-name-col" colSpan={5} style={{ minHeight: '120px' }}>
                      No discrepancies found.
                    </div>
                  </div>
                </div>
              )}
              {paginatedRows.map((row, i) => (
                <div key={i} className="figma-table-row" style={{ minHeight: '120px' }}>
                  <div className="figma-table-cell figma-profile-name-col" style={{ minHeight: '120px' }}>
                    <span className="figma-profile-name">{row.profileName}</span>
                  </div>
                  <div className="figma-table-cell figma-org-value-col" style={{ minHeight: '120px' }}>
                    <div className="figma-org-value-content">
                      {row.orgValue == null || row.orgValue === '' ? (
                        <span>-</span>
                      ) : (
                        <>
                          <img 
                            src={warningAmber} 
                            alt="Warning" 
                            className="figma-warning-icon" 
                            style={{ 
                              fill: '#D32F2F',
                              filter: 'brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%)'
                            }}
                          />
                          <div className="figma-org-value-text">
                            <span className="figma-setting-name">{row.settingName}</span>
                            <span className="figma-equals"> = </span>
                            <span className={`figma-value ${row.isRisk ? 'figma-risk-value' : ''}`}>
                              {row.orgValue}
                            </span>
                            {taskType === 'login_ip_ranges' && row.issueDetails && (
                              <div className="figma-issue-details">
                                Issue: {row.issueDetails}
                              </div>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="figma-table-cell figma-standard-value-col" style={{ minHeight: '120px' }}>
                    <div className="figma-standard-value-text">
                      <span className="figma-setting-name">{row.settingName}</span>
                      <span className="figma-equals"> = </span>
                      <span className="figma-value">{row.standardValue}</span>
                    </div>
                  </div>
                  <div className="figma-table-cell figma-owasp-category-col" style={{ minHeight: '120px' }}>
                    <div className="figma-owasp-category-text">
                      {row.owasp}
                    </div>
                  </div>
                  <div className="figma-table-cell figma-risk-col" style={{ minHeight: '120px' }}>
                    <div className="figma-risk-text">
                      {row.risk}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="figma-pagination">
              <button 
                onClick={() => setPage(p => Math.max(1, p - 1))} 
                disabled={page === 1}
                className="figma-pagination-btn"
              >
                Previous
              </button>
              <span className="figma-pagination-info">
                Page {page} of {totalPages}
              </span>
              <button 
                onClick={() => setPage(p => Math.min(totalPages, p + 1))} 
                disabled={page === totalPages}
                className="figma-pagination-btn"
              >
                Next
              </button>
            </div>
          )}
        </>
      )}
    </>
  );
} 