.health-check-details {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: var(--font-family-sans);
}

/* Header Card */
.header-card {
  margin-bottom: 2rem;
  background-color: #FFFFFF;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.org-name {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 20px;
  line-height: 1.6em;
  color: #020A07;
  margin: 0;
}

.last-synced {
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.6em;
  color: #666666;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 24px;
}

.rescan-button {
  background-color: #51D59C;
  color: #FFFFFF;
  border: none;
  border-radius: 4px;
  padding: 8px 24px;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.6em;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.rescan-button:hover {
  background-color: #3DC488;
}

.rescan-button:disabled {
  background-color: #9CA3AF;
  cursor: not-allowed;
  opacity: 0.7;
}

.rescan-icon {
  font-size: 16px;
}

.more-options-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  color: #6B7280;
  font-size: 18px;
  transition: color 0.2s;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-options-button:hover {
  color: #374151;
}

/* Tabs */
.tabs-container {
  margin-bottom: 2rem;
}

.tabs {
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.26);
  overflow-x: auto;
  scrollbar-width: thin;
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab-button {
  background: none;
  border: none;
  padding: 14px 16px;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.6em;
  color: #393E3C;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
  white-space: nowrap;
  min-width: 120px;
  text-align: center;
}

.tab-button:hover {
  background-color: rgba(81, 213, 156, 0.1);
  color: #020A07;
}

.tab-button.active {
  background-color: #F8FDFB;
  color: #020A07;
  border-bottom-color: #51D59C;
}

/* Tab Content */
.tab-content {
  background-color: #FFFFFF;
  border-radius: 4px;
  padding: 24px;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.12);
}

/* Overview Tab */
.org-summary {
  margin-bottom: 2rem;
}

.org-summary h3 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: var(--color-text-primary);
}

.summary-card {
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.summary-item {
  display: flex;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-gray-200);
}

.summary-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.summary-label {
  font-weight: 500;
  color: var(--color-text-secondary);
  width: 180px;
  flex-shrink: 0;
}

.summary-value {
  color: var(--color-text-primary);
  font-weight: 500;
}

.health-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius-sm);
  font-weight: 600;
  color: white;
}

.health-badge.high {
  background-color: var(--color-success);
}

.health-badge.medium {
  background-color: var(--color-warning);
}

.health-badge.low {
  background-color: var(--color-danger);
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius-sm);
  font-weight: 600;
}

.status-badge.active {
  background-color: var(--color-success-light);
  color: var(--color-success);
}

.status-badge.inactive {
  background-color: var(--color-gray-200);
  color: var(--color-gray-600);
}

/* Health Check Tab */
.health-check-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
}

.health-score-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.health-score-label {
  font-weight: 600;
  font-size: 1.2rem;
  color: var(--color-text-primary);
}

.health-score-value {
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  font-weight: 700;
  font-size: 1.5rem;
  color: white;
  min-width: 80px;
  text-align: center;
}

.health-score-grade {
  font-weight: 600;
  font-size: 1.2rem;
}

.rescan-button {
  padding: 0.75rem 1.5rem;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rescan-button:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px);
}

.rescan-button:disabled {
  background-color: var(--color-gray-400);
  cursor: not-allowed;
  transform: none;
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.chart-card {
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.chart-card h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--color-text-primary);
  text-align: center;
}

.pie-chart-container,
.bar-chart-container {
  height: 300px;
  position: relative;
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
}

.filter-group,
.search-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label,
.search-group label {
  font-weight: 500;
  color: var(--color-text-secondary);
}

.filter-group select,
.search-group input {
  padding: 0.5rem;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius-sm);
  min-width: 200px;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h4 {
  font-size: 1.2rem;
  font-weight: 600;
}

.section-filters {
  display: flex;
  gap: 1rem;
}

.filter-dropdown {
  padding: 0.5rem 1rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background-color: white;
}

.search-container {
  position: relative;
}

.search-input {
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  width: 250px;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  opacity: 0.5;
}

/* Policies Result Section */
.policies-result-section {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.policies-result-table-wrapper {
  overflow-x: auto;
  margin-top: 1rem;
}

.policies-result-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.policies-result-table th {
  background-color: #f8f9fa;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #dee2e6;
}

.policies-result-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #dee2e6;
}

.severity-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.high-severity {
  background-color: #f8d7da;
  color: #dc3545;
}

.medium-severity {
  background-color: #fff3cd;
  color: #856404;
}

.low-severity {
  background-color: #d1e7dd;
  color: #0f5132;
}

/* Additional styles for the new layout */
.health-check-figma-ui {
  background: #FFFFFF !important;
  padding: 32px;
  border-radius: 16px;
  font-family: 'Lato', Arial, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
}

.filter-bar {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: #fff;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
}

.filter-label {
  font-weight: 500;
  color: #333;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 150px;
}

.filter-reset-btn {
  padding: 8px 16px;
  background: #51D59C;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-reset-btn:hover {
  background: #45c492;
}

/* Security Risks Section */
.security-risks-section {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.risks-table-wrapper {
  overflow-x: auto;
  margin-top: 1rem;
}

.risks-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.risks-table th {
  background-color: #f8f9fa;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #dee2e6;
}

.risks-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #dee2e6;
}

.risks-table tr.high-risk {
  background-color: rgba(220, 53, 69, 0.05);
}

.risks-table tr.medium-risk {
  background-color: rgba(255, 193, 7, 0.05);
}

.risks-table tr.meets-standard {
  background-color: rgba(40, 167, 69, 0.05);
}

.risk-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  white-space: nowrap;
}

.risk-badge.high-risk {
  background-color: #f8d7da;
  color: #dc3545;
}

.risk-badge.medium-risk {
  background-color: #fff3cd;
  color: #856404;
}

.risk-badge.meets-standard {
  background-color: #d1e7dd;
  color: #0f5132;
}

.info-button {
  background: none;
  border: none;
  cursor: pointer;
  opacity: 0.5;
  transition: opacity 0.2s;
}

.info-button:hover {
  opacity: 1;
}

/* Loading, Error, and Empty States */
.loading-indicator,
.pending-indicator,
.error-indicator,
.empty-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon,
.empty-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
}

.retry-button:hover {
  background-color: #e9ecef;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: var(--color-gray-300);
  color: var(--color-text-primary);
}

.btn-secondary:hover {
  background-color: var(--color-gray-400);
}

.btn-warning {
  background-color: var(--color-warning);
  color: white;
  margin-left: 0.5rem;
}

.btn-warning:hover {
  background-color: #e0a800;
}

/* Messages */
.loading-message,
.error-message,
.no-data-message,
.coming-soon {
  padding: 2rem;
  text-align: center;
  font-size: 1.1rem;
  color: var(--color-text-secondary);
}

.error-message {
  color: var(--color-danger);
}

.coming-soon {
  font-style: italic;
  color: var(--color-text-secondary);
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
  padding: 3rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .health-check-details {
    padding: 1rem;
  }

  .charts-container {
    grid-template-columns: 1fr;
  }

  .filters-container {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

/* Coming Soon */
.coming-soon {
  font-style: italic;
  color: var(--color-text-secondary);
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
  padding: 5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  text-align: center;
}

.coming-soon h3 {
  font-size: 1.5rem;
  color: var(--color-primary);
  margin-bottom: 1rem;
}

/* Authentication Error */
.auth-error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.auth-error-message {
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
  border-radius: var(--border-radius-md);
  padding: 2rem;
  text-align: center;
  max-width: 500px;
}

.auth-error-message h3 {
  color: var(--color-danger);
  margin-bottom: 1rem;
}

.auth-error-message p {
  margin-bottom: 1.5rem;
  color: var(--color-text-secondary);
}

.auth-error-message .btn {
  margin-top: 1rem;
}

/* Health Check Content */
.health-check-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Filter Section */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.filter-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-title {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.6em;
  color: #393E3C;
  margin: 0;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 8px 12px;
  min-width: 280px;
}

.filter-select {
  border: none;
  background: none;
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.6em;
  color: #020A07;
  width: 100%;
  outline: none;
}

.filter-arrow {
  width: 16px;
  height: 16px;
  color: #51D59C;
  margin-left: 8px;
}

.filter-reset-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #FFFFFF;
  color: #020A07;
  border: 1px solid #51D59C;
  border-radius: 4px;
  padding: 8px 24px;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.6em;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-reset-btn:hover {
  background-color: rgba(81, 213, 156, 0.1);
}

.reset-icon {
  width: 24px;
  height: 24px;
  color: #020A07;
}

/* Charts Section */
.charts-section {
  display: flex;
  gap: 19px;
  align-items: flex-start;
}

.chart-card {
  background-color: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 12px 24px;
  flex: 1;
}

.chart-title {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 1.6em;
  color: #000000;
  margin: 0 0 16px 0;
  text-align: center;
}

.chart-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.pie-chart-container {
  width: 260px;
  height: 260px;
  margin: 0 auto;
}

.bar-chart-container {
  width: 100%;
  height: 260px;
}

.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.legend-color.high-risk {
  background-color: rgba(211, 47, 47, 0.6);
}

.legend-color.medium-risk {
  background-color: rgba(255, 180, 0, 0.6);
}

.legend-color.meets-standard {
  background-color: #51D59C;
}

.legend-text {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.6em;
  color: #000000;
}

/* Details Section */
.details-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.details-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.details-title {
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 20px;
  line-height: 1.6em;
  color: #020A07;
  margin: 0;
}

.details-description {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.6em;
  color: #393E3C;
  margin: 0;
}

.details-table {
  overflow-x: auto;
}

.details-table table {
  width: 100%;
  border-collapse: collapse;
  background-color: #FFFFFF;
}

.details-table th {
  background-color: #F1FCF7;
  border: 1px solid rgba(0, 0, 0, 0.08);
  padding: 14px 8px 14px 16px;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.6em;
  color: #393E3C;
  text-align: left;
}

.details-table td {
  border: 1px solid #B8D8CB;
  padding: 14px 8px 14px 16px;
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.6em;
  color: #020A07;
}

.details-table th:nth-child(2),
.details-table th:nth-child(4),
.details-table th:nth-child(5) {
  color: #1D2433;
}

.details-table td:nth-child(1),
.details-table td:nth-child(3) {
  color: #1D2433;
}

/* Severity Badges */
.severity-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.severity-badge.high-severity {
  background-color: #dc3545;
  color: white;
}

.severity-badge.medium-severity {
  background-color: #ffc107;
  color: #212529;
}

.severity-badge.low-severity {
  background-color: #28a745;
  color: white;
}
