import React, { useState, useEffect, useRef, useLayoutEffect } from 'react';
import ProfilesPermissionsTable from './ProfilesPermissionsTable';
import '../figma-profiles-permissions.css';
import { fetchProfilesPermissions } from '../api';
import './IntegrationTabs.css';
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title } from 'chart.js';
import * as d3 from 'd3';
import bubbleGroups from './bubbleGroupsConfig';
import SettingsRiskTable from './SettingsRiskTable.jsx';
import { useParams, useNavigate } from 'react-router-dom';
import DropdownIcon from '../assets/icons/DropdownIcon.svg';
import SettingsDetailPage from './SettingsDetailPage.jsx';
import { taskAPI } from '../api';
ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title);

const getExecutionLogId = (task) =>
  task.execution_log_id ||
  task.ExecutionLogId ||
  task.task_status_id ||
  task.TaskStatusId;

const keyPermissions = [
  'Modify All Data',
  'View All Data',
  'Manage Users',
  'Reset Passwords',
  'Data Export',
  'Manage Sharing',
  'Manage Roles',
  'Edit Readonly Fields',
  'Manage Encryption Keys',
  'View Encrypted Data',
  'View Setup'
];

const permissionFieldMap = {
  'Modify All Data': 'PermissionsModifyAllData',
  'View All Data': 'PermissionsViewAllData',
  'Manage Users': 'PermissionsManageUsers',
  'Reset Passwords': 'PermissionsResetPasswords',
  'Data Export': 'PermissionsDataExport',
  'Manage Sharing': 'PermissionsManageSharing',
  'Manage Roles': 'PermissionsManageRoles',
  'Edit Readonly Fields': 'PermissionsEditReadonlyFields',
  'Manage Encryption Keys': 'PermissionsManageEncryptionKeys',
  'View Encrypted Data': 'PermissionsViewEncryptedData',
  'View Setup': 'PermissionsViewSetup'
};

const tabOptions = [
  { label: 'All', value: 'all' },
  { label: 'Profiles', value: 'Profile' },
  { label: 'Permission Sets', value: 'Permission Set' }
];

// Brand color palette for permissions
const permissionColors = {
  ViewAllUsers: '#FFB400',
  ViewAllData: '#6C63FF',
  ModifyAllData: '#51D59C',
  ResetPasswords: '#A393E6',
  ManageUsers: '#FFD6A5',
  ManageRoles: '#FFFFB5',
  ManageSharing: '#B5EAD7',
  ViewSetup: '#FFB7B2',
  ManageEncryptionKeys: '#A0CED9',
  CustomizeApplication: '#B5EAD7',
  ApiEnabled: '#C7CEEA',
  ManageCustomReportTypes: '#FFDAC1',
  ManagePasswordPolicies: '#FFFFB5',
  RunReports: '#E2F0CB',
};

// Gradient color palette for bubbles
const bubbleGradients = [
  ['#51d59c', '#4ecb90'],
  ['#51d59c', '#5edfa7'],
  ['#51d59c', '#3fbf85'],
  ['#51d59c', '#63dbac'],
  ['#51d59c', '#45c28f'],
  ['#51d59c', '#6ee2b2'],
  ['#51d59c', '#37b77a'],
  ['#51d59c', '#78e7b9'],
  ['#51d59c', '#2ead6e'],
  ['#51d59c', '#82ecc1'],
  ['#51d59c', '#249e63'],
  ['#51d59c', '#8cf1c9'],
  ['#51d59c', '#1d8955'],
  ['#51d59c', '#96f6d0'],
  ['#51d59c', '#167948'],
  ['#51d59c', '#a0fbda'],
  ['#51d59c', '#0e5e35'],
  ['#51d59c', '#aefde2'],
  ['#51d59c', '#3fc395'],
  ['#51d59c', '#56d6a2']
];

const customBubbleColors = ['#51D49D', '#6CDCAD', '#C9F2E1', '#D3FFEC', '#9CFFD5', '#C9F2E1', '#6CDCAD'];

// Helper to measure text width for SVG
function measureTextWidth(text, fontSize, fontFamily = 'Inter, sans-serif') {
  if (typeof document === 'undefined') return text.length * fontSize * 0.6; // fallback for SSR
  const canvas = measureTextWidth._canvas || (measureTextWidth._canvas = document.createElement('canvas'));
  const context = canvas.getContext('2d');
  context.font = `${fontSize}px ${fontFamily}`;
  return context.measureText(text).width;
}

// Helper to split and truncate label to fit bubble
function getBubbleLabelLines(label, r, maxLines = 2, minFontSize = 7, fontFamily = 'Inter, sans-serif') {
  if (!label) return { lines: [''], fontSize: minFontSize, truncated: false };
  let fontSize = Math.max(8, r * 0.28);
  let diameter = 2 * r * 0.85; // 85% of bubble diameter for padding
  let words = label.split(' ');
  let lines = [];
  let truncated = false;
  while (fontSize >= minFontSize) {
    lines = [];
    let line = '';
    for (let i = 0; i < words.length; i++) {
      let testLine = line ? line + ' ' + words[i] : words[i];
      let testWidth = measureTextWidth(testLine, fontSize, fontFamily);
      if (testWidth > diameter && line) {
        lines.push(line);
        line = words[i];
        if (lines.length === maxLines - 1) {
          // Last line, truncate with ellipsis if needed
          let lastLine = line;
          while (measureTextWidth(lastLine + '...', fontSize, fontFamily) > diameter && lastLine.length > 1) {
            lastLine = lastLine.slice(0, -1);
            truncated = true;
          }
          lines.push(lastLine + (truncated ? '...' : ''));
          break;
        }
      } else {
        line = testLine;
      }
    }
    if (lines.length < maxLines && line) lines.push(line);
    // Check if text block fits vertically
    const lineHeight = fontSize * 1.1;
    const totalHeight = lines.length * lineHeight;
    if (totalHeight <= diameter) break;
    fontSize -= 1;
    truncated = true;
  }
  return { lines, fontSize, truncated };
}

// Data-driven bubble radius: scale by count
// This function is no longer used - we're using a different approach for bubble sizing
function getBubbleRadius(label, count, baseR = 36, fontSize = 16, padding = 12, minR = 60, maxR = 180, maxCount = 1) {
  return minR; // Default value, not actually used
}

// D3 Packed Bubble Chart Component
function PackedBubbleChart({ data, title, onBubbleClick, selectedPermission }) {
  const wrapperRef = useRef();
  const svgRef = useRef();
  const [dimensions, setDimensions] = useState({ width: 600, height: 420 });
  const [wrapperHeight, setWrapperHeight] = useState(420);

  useLayoutEffect(() => {
    function updateSize() {
      if (wrapperRef.current) {
        const rect = wrapperRef.current.getBoundingClientRect();
        setDimensions({ width: rect.width, height: 420 });
      }
    }
    updateSize();
    const ro = new window.ResizeObserver(updateSize);
    if (wrapperRef.current) ro.observe(wrapperRef.current);
    return () => ro.disconnect();
  }, []);

  useLayoutEffect(() => {
    if (!data || data.length === 0) return;

    const width = dimensions.width;
    const height = dimensions.height;
    const minPadding = 15; // Increased padding for better visibility
    const topPadding = 30; // Extra padding for top to prevent cutoff
    
    // Calculate available space
    const availableArea = (width - minPadding * 2) * (height - minPadding * 2 - topPadding);
    const numBubbles = data.length;
    
    // Calculate optimal base bubble size
    const optimalBaseArea = availableArea / (numBubbles * 1.5);
    const optimalBaseRadius = Math.sqrt(optimalBaseArea / Math.PI);
    
    // Find max count for scaling
    const maxCount = Math.max(...data.map(d => {
      const count = typeof d.count === 'number' ? d.count : parseInt(d.count) || 0;
      return count;
    }));

    // Calculate bubble sizes
    let bubblesWithR = data.map((d, i) => {
      const count = typeof d.count === 'number' ? d.count : parseInt(d.count) || 0;
      
      let radius;
      if (count === 0) {
        radius = optimalBaseRadius * 0.8;
      } else {
        const scale = Math.sqrt(count / maxCount);
        radius = optimalBaseRadius * (0.8 + scale * 0.4);
      }
      
      return {
        ...d,
        r: Math.max(25, Math.min(radius, optimalBaseRadius * 1.2)),
        id: i,
        color: customBubbleColors[i % customBubbleColors.length]
      };
    });

    // Sort bubbles by size (largest first)
    bubblesWithR.sort((a, b) => b.r - a.r);

    // Create SVG and draw bubbles with adjusted viewBox
    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();
    svg.attr('width', '100%')
       .attr('height', 360)
       .attr('viewBox', `0 0 ${width} 360`)
       .attr('preserveAspectRatio', 'xMidYMid meet');

    // Force-directed layout simulation with improved bounds
    const simulation = d3.forceSimulation(bubblesWithR)
      .force('charge', d3.forceManyBody().strength(5))
      .force('collide', d3.forceCollide().radius(d => d.r + 4).strength(1)) // Increased buffer
      .force('x', d3.forceX(width / 2).strength(0.07))
      .force('y', d3.forceY(360 / 2).strength(0.07)) // Center vertically in 360px height
      .force('bounds', () => {
        // Keep bubbles within bounds with adjusted padding
        const padding = 10;
        bubblesWithR.forEach(bubble => {
          bubble.x = Math.max(bubble.r + padding, Math.min(width - bubble.r - padding, bubble.x));
          bubble.y = Math.max(bubble.r + padding, Math.min(360 - bubble.r - padding, bubble.y));
        });
      });

    // Run simulation with more iterations for better layout
    for (let i = 0; i < 400; ++i) simulation.tick();

    // Draw the nodes after simulation
    const node = svg.selectAll('g')
      .data(bubblesWithR)
      .join('g')
      .attr('transform', d => `translate(${d.x},${d.y})`)
      .style('cursor', 'pointer')
      .on('click', (event, d) => {
        if (onBubbleClick) onBubbleClick(d.permission);
      })
      .on('mouseover', function(event, d) {
        // Show tooltip
        const tooltip = d3.select('body')
          .append('div')
          .attr('class', 'bubble-tooltip')
          .style('position', 'absolute')
          .style('visibility', 'visible')
          .style('background', 'white')
          .style('border', '1px solid #ddd')
          .style('border-radius', '4px')
          .style('padding', '8px')
          .style('box-shadow', '0 2px 4px rgba(0,0,0,0.1)')
          .style('z-index', '1000')
          .style('top', (event.pageY - 10) + 'px')
          .style('left', (event.pageX + 10) + 'px')
          .html(`<strong>${d.label}</strong><br>Count: ${d.count}`);
      })
      .on('mousemove', function(event) {
        d3.selectAll('.bubble-tooltip')
          .style('top', (event.pageY - 10) + 'px')
          .style('left', (event.pageX + 10) + 'px');
      })
      .on('mouseout', function() {
        d3.selectAll('.bubble-tooltip').remove();
      });

    // Add circles
    node.append('circle')
      .attr('r', d => d.r)
      .attr('fill', (d, i) => {
        const isSelected = selectedPermission === d.permission ||
          (Array.isArray(selectedPermission) && selectedPermission.includes(d.permission));
        return isSelected ? d3.color(d.color).darker(0.3) : d.color;
      })
      .attr('stroke', d => {
        const isSelected = selectedPermission === d.permission ||
          (Array.isArray(selectedPermission) && selectedPermission.includes(d.permission));
        return isSelected ? '#155b55' : '#51D59C';
      })
      .attr('stroke-width', d => {
        const isSelected = selectedPermission === d.permission ||
          (Array.isArray(selectedPermission) && selectedPermission.includes(d.permission));
        return isSelected ? 3 : 1;
      })
      .attr('opacity', d => {
        const isSelected = selectedPermission === d.permission ||
          (Array.isArray(selectedPermission) && selectedPermission.includes(d.permission));
        return Array.isArray(selectedPermission) && selectedPermission.length > 0
          ? (isSelected ? 1 : 0.7)
          : 0.95;
      });

    // Add labels
    node.each(function(d) {
      const g = d3.select(this);
      const fontSize = Math.min(14, d.r * 0.4); // Scale font size with bubble size
      const words = d.label.split(' ');
      let lines = [];
      let line = '';

      for (let i = 0; i < words.length; i++) {
        const testLine = line ? line + ' ' + words[i] : words[i];
        if (testLine.length * fontSize * 0.5 > d.r * 1.5 && line) {
          lines.push(line);
          line = words[i];
        } else {
          line = testLine;
        }
      }
      if (line) lines.push(line);

      if (lines.length > 2) {
        lines = lines.slice(0, 2);
        lines[1] = lines[1] + '...';
      }

      const text = g.append('text')
        .attr('text-anchor', 'middle')
        .attr('y', -fontSize / 2)
        .attr('fill', '#222')
        .style('font-weight', 500)
        .style('font-size', `${fontSize}px`)
        .attr('dominant-baseline', 'middle');

      const lineHeight = fontSize * 1.2;
      lines.forEach((line, i) => {
        text.append('tspan')
          .attr('x', 0)
          .attr('dy', i === 0 ? 0 : lineHeight)
          .text(line);
      });

      text.append('title').text(d.label);
    });

    setWrapperHeight(height);
  }, [data, onBubbleClick, selectedPermission, dimensions]);

  return (
    <div ref={wrapperRef} style={{ 
      width: '100%', 
      minWidth: 320, 
      maxWidth: 900, 
      margin: '0 auto', 
      height: 360,
      overflow: 'visible' // Changed to visible
    }}>
      {title && <div className="bubble-chart-title">{title}</div>}
      <svg ref={svgRef} className="bubble-chart-svg" style={{ 
        width: '100%', 
        height: 360,
        overflow: 'visible' // Changed to visible
      }} />
    </div>
  );
}

// BubbleChartCard component (smaller, responsive)
function BubbleChartCard({ title, data, onBubbleClick, selectedPermission }) {
  return (
    <div className="bubble-chart-card">
      <PackedBubbleChart
        data={data}
        title={title}
        onBubbleClick={onBubbleClick}
        selectedPermission={selectedPermission}
        width={100}
        height={100}
      />
    </div>
  );
}

// Helper to normalize permission field names
function normalizeFieldName(name) {
  return (name || '').replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
}

// Helper to get bubble data for a group - count actual true values from table
const getBubbleDataForGroup = (rows, group) => {
  return group.settings.map(({ permission, label }) => {
    const normKey = normalizeFieldName(permission);
    let count = 0;
    
    // Count rows where this permission is true
    rows.forEach(row => {
      const val = row[normKey];
      if (val === true || val === 'true' || (val && val.toString().toLowerCase() === 'true')) {
        count += 1;
      }
    });
    
    // Also check inside OrgValue JSON for this permission
    rows.forEach(row => {
      if (row.OrgValue) {
        try {
          const orgSettings = typeof row.OrgValue === 'string' ? JSON.parse(row.OrgValue) : row.OrgValue;
          if (Array.isArray(orgSettings)) {
            const matchingSetting = orgSettings.find(s =>
              s.SalesforceSetting === permission ||
              normalizeFieldName(s.SalesforceSetting) === normKey
            );
            
            if (matchingSetting) {
              const possibleValueFields = ['ProfileValue', 'PermissionSetValue-UserPermissions', 'profileValue', 'Match'];
              for (const field of possibleValueFields) {
                const fieldVal = matchingSetting[field];
                if (fieldVal === true || fieldVal === 'true') {
                  if (!(row[normKey] === true || row[normKey] === 'true')) {
                    count += 1;
                    break;
                  }
                }
              }
            }
          }
        } catch (e) {
          console.error(`Error parsing OrgValue for ${label}:`, e);
        }
      }
    });
    
    return {
      permission: normKey,
      label: `${label} (${count})`,
      count: count
    };
  });
};



// Build settings array for table columns
const allBubbleSettings = bubbleGroups.flatMap(group => group.settings);
const tableSettings = allBubbleSettings.map(s => ({
  field: normalizeFieldName(s.permission),
  label: s.label
}));

// Dynamically build bubble chart picklist options from bubbleGroups
const bubbleChartOptions = bubbleGroups.map((group, idx) => ({
  label: group.title,
  value: `group${idx}`
}));

const ProfilesAndPermissionsTab = ({ orgId }) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('all');
  const [selectedBubbles, setSelectedBubbles] = useState([]);
  const [selectedBubbleType, setSelectedBubbleType] = useState('group0');
  const tableRef = useRef();
  const { taskType, executionLogId } = useParams();
  const navigate = useNavigate();
  const [detailView, setDetailView] = useState(null);

  

  useEffect(() => {
    setLoading(true);
    setError(null);
    taskAPI.getTasks({
      integration_id: orgId,
      status: 'completed',
      limit: 1,
      task_type: 'profiles_permission_sets'
    })
      .then(taskData => {
        const arr = Array.isArray(taskData)
          ? taskData
          : (Array.isArray(taskData.data) ? taskData.data : []);
        const latest = arr.length > 0 ? arr[0] : null;
        const executionLogId = latest && getExecutionLogId(latest);
        if (!executionLogId) {
          setError('No completed profiles_permission_sets task found');
          setLoading(false);
          return;
        }
        fetchProfilesPermissions(orgId, executionLogId)
          .then(apiData => {
            setData(apiData);
            setLoading(false);
          })
          .catch(err => {
            setError('Failed to fetch profiles-permissions data');
            setLoading(false);
          });
      })
      .catch(err => {
        setError('Failed to fetch task status');
        setLoading(false);
      });
  }, [orgId]);

  if (loading) {
    return (
      <div className="loading-state">
        <div className="spinner"></div>
        <p>Loading profiles and permission sets data...</p>
      </div>
    );
  }

  if (error) {
    // Check if it's the "no completed task" error to show empty state
    if (error.includes('No completed profiles_permission_sets task found')) {
      return (
        <div className="empty-state">
          <div className="empty-icon">👤</div>
          <h3>No Profiles and Permission Sets Data Available</h3>
          <p>No profiles and permission sets data was found in the database. Click the <strong>Rescan</strong> button at the top of the page to fetch data from Salesforce.</p>
          <button className="refresh-button primary" onClick={() => window.location.reload()}>
            Rescan
          </button>
        </div>
      );
    }

    // For other errors, show error state
    return (
      <div className="error-state">
        <div className="error-icon">⚠️</div>
        <p>{error}</p>
        <button className="refresh-button" onClick={() => window.location.reload()}>
          Try Again
        </button>
      </div>
    );
  }

  if (!data || data.status !== 'completed') {
    return (
      <div className="empty-state">
        <div className="empty-icon">📊</div>
        <h3>No Completed Scan Data Available</h3>
        <p>No completed scan data is available. Please wait for the scan to complete or click the <strong>Rescan</strong> button at the top of the page.</p>
      </div>
    );
  }

  // If detail view is set, show the subtab (Figma style)
  if (detailView) {
    return (
      <div className="settings-risk-subtab-wrapper">
        <div className="settings-risk-detail-card">
          <SettingsDetailPage 
            orgId={orgId} 
            taskType={detailView.taskType} 
            executionLogId={detailView.executionLogId}
            onBack={() => setDetailView(null)}
          />
        </div>
      </div>
    );
  }

  // If detail view is requested via route params, render it as a sub-view with back button
  if (taskType && executionLogId) {
    return (
      <div className="profiles-permissions-detail-wrapper">
        <div className="settings-risk-detail-card">
          <SettingsDetailPage 
            orgId={orgId} 
            taskType={taskType} 
            executionLogId={executionLogId}
            onBack={() => navigate(`/integration/${orgId}?tab=profilesPermissions`)}
          />
        </div>
      </div>
    );
  }

  // --- Combine profiles and permission sets into one array ---
  const profiles = Array.isArray(data?.policies) ? data.policies.map(p => ({
    ...p,
    Type: p.Type === 'PermissionSetPermissions' ? 'Permission Set' : 'Profile',
    Name: p.ProfileName
  })) : [];
  const permissionSets = Array.isArray(data?.permissionSetAssignments) ? data.permissionSetAssignments.map(ps => ({
    ...ps,
    Type: 'Permission Set',
    Name: ps.PermissionSetName || ps.ProfileName
  })) : [];
  let unifiedRows = [...profiles, ...permissionSets];
  if (activeTab !== 'all') {
    unifiedRows = unifiedRows.filter(row => row.Type === activeTab);
  }

  // --- Group and roll up assignments like the table ---
  function getTypeLabel(type) {
    if (type === 'ProfilePermissions') return 'Profile';
    if (type === 'ProfilePermissionSetAssignment') return 'Permission Set';
    if (type === 'PermissionSetPermissions') return 'Permission Set';
    return type || '';
  }
  // Build grouped rows for summary cards
  function getGroupedRows() {
    // Parse OrgValue and map permissions to normalized fields for each row
    const allRows = [
      ...(Array.isArray(data?.policies) ? data.policies : []),
      ...(Array.isArray(data?.permissionSetAssignments) ? data.permissionSetAssignments : [])
    ];
    const parsedRows = allRows.map(row => {
      let newRow = { ...row };
      if (row.OrgValue) {
        let orgSettings = [];
        try {
          orgSettings = JSON.parse(row.OrgValue);
        } catch (e) {
          orgSettings = [];
        }
        const type = row.Type || row.type || row.rowType || row.row_type || row.Type__c || '';
        (orgSettings || []).forEach(setting => {
          const colKey = normalizeFieldName(setting.SalesforceSetting);
          // For Permission Set, use PermissionSetValue-UserPermissions if present
          let value;
          if (type === 'ProfilePermissionSetAssignment' || type === 'PermissionSetPermissions' || type === 'Permission Set') {
            if (setting['PermissionSetValue-UserPermissions'] !== undefined) {
              value = setting['PermissionSetValue-UserPermissions'];
            } else if (setting.ProfileValue !== undefined) {
              value = setting.ProfileValue;
            } else if (setting.profileValue !== undefined) {
              value = setting.profileValue;
            } else {
              value = setting.Match;
            }
          } else {
            value = setting.ProfileValue;
            if (value === undefined && setting['PermissionSetValue-UserPermissions'] !== undefined) {
              value = setting['PermissionSetValue-UserPermissions'];
            }
            if (value === undefined && setting.profileValue !== undefined) {
              value = setting.profileValue;
            }
            if (value === undefined) value = setting.Match;
          }
          newRow[colKey] = (value === true || value === 'true') ? 'true' : 'false';
          newRow[`${colKey}_desc`] = setting.Description;
        });
      }
      return newRow;
    });
    // Group by APIName+Type, sum assignments
    const groupMap = {};
    parsedRows.forEach(row => {
      const type = row.Type || row.type || row.rowType || row.row_type || row.Type__c || '';
      const typeLabel = getTypeLabel(type);
      const apiName = typeLabel === 'Permission Set' ? (row.PermissionSetName || row.permission_set_name || row.ProfileName || row.profile_name || row.APIName) : (row.ProfileName || row.profile_name || row.APIName);
      if (!apiName) return;
      const groupKey = `${apiName}__${typeLabel}`;
      if (!groupMap[groupKey]) {
        groupMap[groupKey] = {
          APIName: apiName,
          Type: typeLabel,
          AssignmentCount: 0,
        };
      }
      let assignCount = 0;
      if (row.AssignmentCount !== undefined) assignCount = Number(row.AssignmentCount) || 0;
      if (row.assignment_count !== undefined) assignCount = Number(row.assignment_count) || assignCount;
      if (data.profileAssignments && typeLabel === 'Profile') {
        const pa = data.profileAssignments.find(a => a.profile_name === apiName);
        if (pa && pa.assignment_count !== undefined) assignCount = Number(pa.assignment_count) || assignCount;
      }
      groupMap[groupKey].AssignmentCount += assignCount;
    });
    return Object.values(groupMap);
  }
  // --- Summary cards data (tab aware) ---
  const groupedRows = getGroupedRows();
  let filteredRowsForSummary = groupedRows;
  if (activeTab === 'Profile') {
    filteredRowsForSummary = groupedRows.filter(r => r.Type === 'Profile');
  } else if (activeTab === 'Permission Set') {
    filteredRowsForSummary = groupedRows.filter(r => r.Type === 'Permission Set');
  }
  const totalProfiles = groupedRows.filter(r => r.Type === 'Profile').length;
  const totalPermissionSets = groupedRows.filter(r => r.Type === 'Permission Set').length;
  const totalAssignments = filteredRowsForSummary.reduce((sum, row) => sum + (row.AssignmentCount || 0), 0);

  // --- Collect all present settings from backend data ---
  const presentSettings = new Set();
  (Array.isArray(data?.policies) ? data.policies : []).forEach(policy => {
    let orgSettings = [];
    try {
      orgSettings = JSON.parse(policy.OrgValue);
    } catch (e) {
      orgSettings = [];
    }
    orgSettings.forEach(setting => {
      if (setting.SalesforceSetting) {
        presentSettings.add(setting.SalesforceSetting);
      }
    });
  });

  // --- Filter bubbleGroups to only show settings present in backend ---
  const filteredBubbleGroups = bubbleGroups.map(group => ({
    ...group,
    settings: group.settings.filter(s => presentSettings.has(s.permission))
  }));

  // --- Dynamically generate bubble chart data for each group ---
  const bubbleChartData = {};
  filteredBubbleGroups.forEach((group, idx) => {
    bubbleChartData[`group${idx}`] = getBubbleDataForGroup(unifiedRows, group);
  });


  
  // --- Assignment Donut Chart Data (correct calculation based on data structure) ---
  const profileAssignments = Array.isArray(data?.profileAssignments) 
    ? data.profileAssignments.reduce((sum, pa) => sum + (Number(pa.assignment_count) || 0), 0)
    : 0;
  const permissionSetAssignments = Array.isArray(data?.permissionSetAssignments) 
    ? data.permissionSetAssignments.reduce((sum, psa) => sum + (Number(psa.assignment_count) || 0), 0)
    : 0;
  
  console.log('DEBUG Assignment data:', { profileAssignments, permissionSetAssignments });
  const assignmentDonutData = {
    labels: ['Profiles', 'Permission Sets'],
    datasets: [{
      data: [profileAssignments, permissionSetAssignments],
      backgroundColor: ['#51D59C', '#FFD54F'],
      borderColor: ['#fff', '#fff'],
      borderWidth: 2
    }]
  };

  // Updated row highlighting logic for multiple bubble filters
  const getRowClass = (row) => {
    if (!selectedBubbles || selectedBubbles.length === 0) return '';
    
    // Check if row has any of the selected permissions
    for (const permission of selectedBubbles) {
      const normKey = (permission || '').replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
      const val = row[normKey];
      if (val === true || val === 'true' || (val && val.toString().toLowerCase() === 'true')) {
        return 'highlight-row';
      }
    }
    return '';
  };

  // Updated click-to-filter logic for multiple selection
  const handleBubbleClick = (permission) => {
    const normKey = (permission || '').replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
    console.log('DEBUG handleBubbleClick permission:', permission, 'normKey:', normKey);
    
    setSelectedBubbles(prev => {
      // If already selected, remove it (deselect)
      if (prev.includes(normKey)) {
        console.log(`Removing ${normKey} from selections`);
        return prev.filter(p => p !== normKey);
      }
      // Otherwise add it to selections
      console.log(`Adding ${normKey} to selections`);
      const newSelection = [...prev, normKey];
      console.log('New selections:', newSelection);
      return newSelection;
    });
  };

  // --- Reset Filters handler ---
  const handleResetFilters = () => {
    setSelectedBubbles([]);
    setActiveTab('all');
    if (tableRef.current && tableRef.current.resetFilters) {
      tableRef.current.resetFilters();
    }
  };



  // Layout as per Figma design: charts row (bubble + assignment), then main table, then settings risk table
  return (
    <>
      {/* Charts Row - matches Figma exactly */}
      <div className='figma-charts-row'>
        {/* Risky Permissions Card - matches Figma exactly */}
        <div className='figma-risky-permissions-card'>
          <div className="figma-card-header">
            <div className="figma-title-picklist-row">
              <h3 className="figma-card-title">Risky Permissions</h3>
              <select
                value={selectedBubbleType}
                onChange={(e) => setSelectedBubbleType(e.target.value)}
                className="figma-bubble-select"
              >
                {bubbleChartOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              
              {/* Reset Button - Only visible when there are selections or filters */}
              {(selectedBubbles.length > 0 || activeTab !== 'all') && (
                <button
                  onClick={handleResetFilters}
                  className="figma-bubble-reset-btn"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                    <path d="M21 3v5h-5"/>
                    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                    <path d="M3 21v-5h5"/>
                  </svg>
                  Reset
                </button>
              )}
            </div>
          </div>
          
          <div className="figma-bubble-chart-container">
            <PackedBubbleChart
              data={bubbleChartData[selectedBubbleType]}
              title={null}
              onBubbleClick={handleBubbleClick}
              selectedPermission={selectedBubbles}
            />
          </div>
        </div>
        
        {/* Assignment Graph Card - matches Figma exactly */}
        <div className="figma-assignment-graph-card">
          <div className="figma-card-header">
            <h3 className="figma-card-title">Assignment Graph</h3>
          </div>
          <div className="figma-pie-chart-container">
                                 <Pie
                       data={assignmentDonutData}
                       options={{
                         plugins: {
                           legend: { 
                             display: true, 
                             position: 'bottom', 
                             labels: { 
                               boxWidth: 18, 
                               font: { size: 16, weight: 600 }, 
                               color: '#393E3C' 
                             } 
                           },
                           title: { display: false },
                           tooltip: {
                             enabled: true,
                             callbacks: {
                               label: function(context) {
                                 const label = context.label || '';
                                 const value = context.parsed || 0;
                                 return `${label}: ${value}`;
                               }
                             }
                           }
                         },
                         responsive: true,
                         maintainAspectRatio: true,
                         cutout: '70%',
                         layout: {
                           padding: {
                             top: 0,
                             bottom: 20
                           }
                         }
                       }}
                     />
          </div>
        </div>
      </div>

      {/* Original content preserved with Figma styling */}
      <ProfilesPermissionsTable
        ref={tableRef}
        rowData={unifiedRows}
        settings={tableSettings}
        profileAssignments={data.profileAssignments}
        permissionSetAssignments={data.permissionSetAssignments}
        getRowClass={getRowClass}
        bubbleFilter={selectedBubbles}
        handleResetFilters={handleResetFilters}
      />
      
      <div style={{ marginTop: 32 }}>
        <SettingsRiskTable orgId={orgId} setDetailView={setDetailView} />
      </div>
    </>
  );
};

export default ProfilesAndPermissionsTab;
