import React, { useState, useEffect } from 'react';
import { ResponsivePie } from '@nivo/pie';
import './SeverityChart.css';

// Import SVG assets
import moreVertSvg from '../assets/dashboard/more_vert.svg';
import refreshSvg from '../assets/dashboard/refresh.svg';

/**
 * SeverityChart component for displaying issue severity distribution using Nivo
 * Updated to use professional Nivo pie charts with proper label positioning
 *
 * @param {Object} props - Component props
 * @param {Object} props.data - Chart data with high, medium, and low counts
 * @param {Function} [props.onRefresh] - Optional refresh callback function
 * @param {boolean} [props.isRefreshing] - Whether the chart is currently refreshing
 */
const SeverityChart = ({ data, onRefresh, isRefreshing }) => {
  const [activeTab, setActiveTab] = useState('7days'); // '7days', '30days', 'allTime'
  const [chartData, setChartData] = useState([]);

  // Update chart data when tab changes or data changes
  useEffect(() => {
    // Recalculate time periods data when data changes
    const updatedTimePeriodsData = {
      '7days': {
        high: data?.high || 0,
        medium: data?.medium || 0,
        low: data?.low || 0
      },
      '30days': {
        high: Math.round((data?.high || 0) * 1.5),
        medium: Math.round((data?.medium || 0) * 1.8),
        low: Math.round((data?.low || 0) * 1.3)
      },
      'allTime': {
        high: Math.round((data?.high || 0) * 2.2),
        medium: Math.round((data?.medium || 0) * 2.5),
        low: Math.round((data?.low || 0) * 2.0)
      }
    };

    // Update chart data based on active tab
    const currentData = updatedTimePeriodsData[activeTab];
    
    // Transform data for Nivo pie chart
    const nivoData = [
      {
        id: 'High',
        label: 'High',
        value: currentData.high,
        color: '#C23934'
      },
      {
        id: 'Medium',
        label: 'Medium',
        value: currentData.medium,
        color: '#FFB400'
      },
      {
        id: 'Low',
        label: 'Low',
        value: currentData.low,
        color: '#4BCA81'
      }
    ].filter(item => item.value > 0); // Only show segments with data

    setChartData(nivoData);
  }, [activeTab, data]);

  // Calculate total for display
  const total = chartData.reduce((sum, item) => sum + item.value, 0);

  // Handle refresh button click
  const handleRefresh = () => {
    if (onRefresh && !isRefreshing) {
      onRefresh();
    }
  };

  return (
    <div className="severity-chart-card">
      <div className="severity-chart-header">
        <h3 className="severity-chart-title">Issue Severity Distribution</h3>
        <div className="severity-chart-actions">
          <button
            className="severity-chart-refresh-btn"
            aria-label="Refresh data"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <img
              src={refreshSvg}
              alt="Refresh"
              className={isRefreshing ? 'rotating' : ''}
            />
          </button>
          <button
            className="severity-chart-menu-btn"
            aria-label="More options"
          >
            <img src={moreVertSvg} alt="More options" />
          </button>
        </div>
      </div>

      <div className="severity-chart-tabs">
        <button
          className={`severity-chart-tab ${activeTab === '7days' ? 'active' : ''}`}
          onClick={() => setActiveTab('7days')}
        >
          Last 7 Days
        </button>
        <button
          className={`severity-chart-tab ${activeTab === '30days' ? 'active' : ''}`}
          onClick={() => setActiveTab('30days')}
        >
          Last 30 Days
        </button>
        <button
          className={`severity-chart-tab ${activeTab === 'allTime' ? 'active' : ''}`}
          onClick={() => setActiveTab('allTime')}
        >
          All Time
        </button>
      </div>

      <div className="severity-chart-content">
        <div className="severity-chart-visualization">
          {isRefreshing ? (
            <div className="pie-chart-loading">
              <div className="pie-chart-spinner"></div>
              <div className="pie-chart-loading-text">Loading...</div>
            </div>
          ) : total > 0 ? (
            <div className="nivo-pie-chart-container">
              <ResponsivePie
                data={chartData}
                margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
                innerRadius={0.6}
                padAngle={0.7}
                cornerRadius={3}
                activeOuterRadiusOffset={8}
                colors={{ datum: 'data.color' }}
                borderWidth={1}
                borderColor={{ from: 'color', modifiers: [['darker', 0.2]] }}
                enableArcLinkLabels={false}
                arcLabelsSkipAngle={10}
                arcLabelsTextColor="#FFFFFF"
                arcLabelsRadiusOffset={0.6}
                layers={[
                  'arcs',
                  'arcLabels',
                  'arcLinkLabels',
                  'legends'
                ]}
                tooltip={({ datum }) => (
                  <div className="nivo-tooltip">
                    <strong>{datum.label}:</strong> {datum.value}
                  </div>
                )}
              />
              {/* Center total display */}
              <div className="pie-center-total">
                <div className="total-number">{total}</div>
                <div className="total-label">Total Issues</div>
              </div>
            </div>
          ) : (
            <div className="empty-state-container">
              <div className="empty-state-icon">
                <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="#E0E0E0"/>
                  <path d="M9 12L11 14L15 10" stroke="#FFFFFF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <div className="empty-state-title">No Data Available</div>
              <div className="empty-state-description">
                Run a scan to see your security metrics
              </div>
            </div>
          )}
        </div>

        <div className="severity-chart-legend">
          <div className="severity-legend-item">
            <div className="severity-legend-color high"></div>
            <div className="severity-legend-label">High</div>
          </div>
          <div className="severity-legend-item">
            <div className="severity-legend-color medium"></div>
            <div className="severity-legend-label">Medium</div>
          </div>
          <div className="severity-legend-item">
            <div className="severity-legend-color low"></div>
            <div className="severity-legend-label">Low</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SeverityChart;
