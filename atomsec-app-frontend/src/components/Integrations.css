.integrations-container {
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Styles */
.page-header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.page-title {
  font-family: 'Poppins', sans-serif;
  font-size: 32px;
  font-weight: 700;
  color: #020A07;
  margin: 0;
}

.page-description {
  font-size: 16px;
  color: #6C757D;
}

.new-connection-btn {
  background-color: #51D59C;
  color: #020A07;
  border: none;
  border-radius: 4px;
  padding: 8px 24px;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
}

.new-connection-btn:hover {
  background-color: #3DC488;
}

.btn-icon {
  font-size: 16px;
  color: #020A07;
}

/* Toolbar Styles */
.integrations-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 20px;
  padding: 0 4px;
}

.search-container {
  position: relative;
  flex: 1;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666666;
}

/* Specific styling for All Integrations search icon */
.integrations-toolbar .search-container .search-icon {
  color: #9CA3AF;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 40px;
  border: 1px solid rgba(0, 0, 0, 0.26);
  border-radius: 4px;
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  color: #666666;
  transition: all 0.2s;
}

.search-input::placeholder {
  color: #9CA3AF;
}

.search-input:focus {
  outline: none;
  border-color: #51D59C;
}

/* Specific styling for All Integrations search bar */
.integrations-toolbar .search-container .search-input {
  background-color: #FFFFFF !important;
  background: #FFFFFF !important;
  border: 1px solid #D1D5DB !important;
  border-radius: 8px !important;
  color: #374151 !important;
  padding: 8px 16px 8px 40px !important;
  font-family: 'Lato', sans-serif !important;
  font-weight: 500 !important;
  font-size: 16px !important;
}

/* Even more specific selector */
.integrations-container .integrations-toolbar .search-container .search-input {
  background-color: #FFFFFF !important;
  background: #FFFFFF !important;
  border: 1px solid #D1D5DB !important;
  border-radius: 8px !important;
  color: #374151 !important;
  padding: 8px 16px 8px 40px !important;
  font-family: 'Lato', sans-serif !important;
  font-weight: 500 !important;
  font-size: 16px !important;
}

/* Most specific selector using the exact HTML structure */
div.integrations-container div.integrations-toolbar div.search-container input.search-input {
  background-color: #FFFFFF !important;
  background: #FFFFFF !important;
  border: 1px solid #D1D5DB !important;
  border-radius: 8px !important;
  color: #374151 !important;
  padding: 8px 16px 8px 40px !important;
  font-family: 'Lato', sans-serif !important;
  font-weight: 500 !important;
  font-size: 16px !important;
}

/* Placeholder styles with high specificity */
div.integrations-container div.integrations-toolbar div.search-container input.search-input::placeholder {
  color: #9CA3AF !important;
}

/* Focus styles with high specificity */
div.integrations-container div.integrations-toolbar div.search-container input.search-input:focus {
  background-color: #FFFFFF !important;
  background: #FFFFFF !important;
  border: 1px solid #51D59C !important;
  outline: none !important;
}

/* Override unwanted padding from Header.css */
.integrations-toolbar .search-container {
  padding: 0 !important;
}

.toolbar-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-container {
  position: relative;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 24px;
  background-color: #FFFFFF;
  border: 1px solid #51D59C;
  border-radius: 8px;
  color: #374151;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-btn:hover {
  background-color: #F9FAFB;
  border-color: #3DC488;
}

.filter-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background-color: white;
  border: 1px solid #DEE2E6;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 160px;
  padding: 8px 0;
}

.filter-option {
  display: block;
  width: 100%;
  text-align: left;
  padding: 12px 16px;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  color: #374151;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.filter-option:hover {
  background-color: #F9FAFB;
}

.filter-option.active {
  background-color: #EAFAF3;
  color: #51D59C;
  font-weight: 500;
}

.view-toggle {
  display: flex;
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  overflow: hidden;
  background-color: #FFFFFF;
}

.view-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  background: none;
  border: none;
  cursor: pointer;
  color: #374151;
  transition: all 0.2s;
  min-width: 44px;
}

.view-btn:hover {
  background-color: #F9FAFB;
}

.view-btn.active {
  background-color: #EAFAF3;
  color: #51D59C;
  border: 1px solid #51D59C;
  border-radius: 6px;
  margin: 2px;
}

/* Main Content Card */
.main-content-card {
  background-color: #FFFFFF;
  border-radius: 4px;
  padding: 24px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
}

/* Empty State Styles */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24px;
  padding: 48px 0;
}

.empty-state-illustration {
  width: 472px;
  height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state-content {
  text-align: center;
  max-width: 472px;
}

.empty-state-content h2 {
  font-family: 'Poppins', sans-serif;
  font-size: 32px;
  font-weight: 700;
  color: #020A07;
  margin-bottom: 8px;
}

.empty-state-content p {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  color: #2A292F;
  line-height: 1.6;
}

/* Grid View Styles */
.integrations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

/* List View Styles */
.integrations-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.integration-card.list-view {
  flex-direction: row;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.integration-card.list-view .integration-header {
  margin-bottom: 0;
  margin-right: 0;
}

.integration-card.list-view .integration-content {
  flex-direction: row;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.integration-card.list-view .integration-info {
  flex: 1;
  margin-right: 12px;
}

.integration-card.list-view .integration-name {
  margin-bottom: 4px;
}

.integration-card.list-view .integration-description {
  margin-bottom: 8px;
}

.integration-card.list-view .integration-last-synced {
  margin-bottom: 0;
}

.integration-card.list-view .integration-actions {
  margin-top: 0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.integration-card.list-view .integration-error-banner {
  left: 16px;
  right: auto;
  width: auto;
}

/* Card Styles */
.integration-card {
  background-color: #FFFFFF;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.1), 0px 4px 8px rgba(0, 0, 0, 0.14);
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  position: relative;
  gap: 16px;
  z-index: 1;
}

/* When menu is open, increase z-index */
.integration-card.menu-open {
  z-index: 2000;
}

.integration-card:hover {
  transform: translateY(-2px);
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.1), 0px 8px 16px rgba(0, 0, 0, 0.14);
}

.integration-card.connected {
  border-color: #51D59C;
}

.integration-card.active-scan {
  border-color: #51D59C;
  box-shadow: 0 0 0 2px rgba(81, 213, 156, 0.3);
}

.integration-card.inactive-integration {
  border-color: #D32F2F;
  box-shadow: 0 0 0 2px rgba(211, 47, 47, 0.1);
  position: relative;
  overflow: visible;
}

.integration-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 6px;
  position: relative;
}

.integration-icon-container {
  position: relative;
  width: 67px;
  height: 74px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.integration-icon {
  width: 67px;
  height: 74px;
  object-fit: contain;
  display: flex;
  align-items: center;
  justify-content: center;
}

.salesforce-icon {
  background-color: transparent;
}

.salesforce-svg {
  width: 100%;
  height: 100%;
}

.integration-status-icon {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
}

.status-icon-bg {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #EAFAF3;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
}

.status-icon-bg.inactive {
  background-color: rgba(211, 47, 47, 0.1);
}

.integration-error-banner {
  position: absolute;
  top: -12px;
  left: 16px;
  right: 16px;
  background-color: #D32F2F;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

.integration-error-banner .error-icon {
  width: 16px;
  height: 16px;
}

.scan-btn.fix-connection {
  background-color: transparent;
  border: 1px solid #D32F2F;
  color: #D32F2F;
}

.scan-btn.fix-connection:hover {
  background-color: rgba(211, 47, 47, 0.1);
}

.status-check {
  width: 18px;
  height: 18px;
}

.integration-logo {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F1F4F9;
  border-radius: 4px;
  margin-bottom: 16px;
}

.popular-badge {
  font-size: 12px;
  padding: 4px 8px;
  background-color: #FFF3CD;
  color: #856404;
  border-radius: 4px;
  font-weight: 500;
}

.integration-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.integration-info {
  flex-grow: 1;
}

.integration-name-container {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.integration-name {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 0;
  color: #020A07;
  line-height: 1.6em;
}

.integration-type {
  display: flex;
  align-items: center;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #666666;
  margin-bottom: 4px;
}

.keyboard-right-icon {
  width: 16px;
  height: 16px;
  margin-left: 4px;
}

.integration-description {
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #393E3C;
  margin-bottom: 8px;
  line-height: 1.6em;
  font-weight: 400;
}

.integration-last-synced {
  font-family: 'Lato', sans-serif;
  font-size: 12px;
  color: #666666;
  margin-bottom: 8px;
  font-weight: 500;
  line-height: 1.6em;
}

.integration-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.scan-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 16px;
  background-color: transparent;
  border: 1px solid #51D59C;
  border-radius: 4px;
  color: #020A07;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  height: 32px;
}

.scan-btn:hover {
  background-color: rgba(81, 213, 156, 0.1);
}

.scan-btn.active {
  background-color: #51D59C;
  color: #FFFFFF;
}

.scan-btn.active .scan-icon {
  color: #FFFFFF;
}

.scan-icon {
  width: 16px;
  height: 16px;
}

.menu-container {
  position: relative;
  z-index: inherit;
}

.menu-btn {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  padding: 0;
}

.menu-btn:hover img {
  filter: brightness(0.9);
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background-color: white;
  border: 1px solid #DEE2E6;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 2001;
  min-width: 180px;
}

/* Handle edge cases where dropdown might be cut off */
.integration-card:last-child .menu-dropdown,
.integration-card:nth-last-child(2) .menu-dropdown {
  right: auto;
  left: 0;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  text-align: left;
  padding: 8px 16px;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  color: #495057;
}

.menu-icon {
  color: #51D59C;
}

.menu-item:hover {
  background-color: #F1F4F9;
}

.menu-item.danger {
  color: #D32F2F;
}

.menu-item.danger:hover {
  background-color: rgba(211, 47, 47, 0.04);
}

.connect-btn {
  padding: 8px 16px;
  background-color: #51D59C;
  color: #020A07;
  border: none;
  border-radius: 4px;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
}

.connect-btn:hover {
  background-color: #3DC488;
}

.view-details-btn {
  padding: 8px 16px;
  background-color: #FFFFFF;
  color: #51D59C;
  border: 1px solid #51D59C;
  border-radius: 4px;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.view-details-btn:hover {
  background-color: #F8FDFB;
}

.connection-status {
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #51D59C;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.connection-status::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #51D59C;
  border-radius: 50%;
  margin-right: 8px;
}

/* No Results Styles */
.no-results {
  text-align: center;
  padding: 48px;
  background-color: #F8F9FA;
  border-radius: 8px;
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.no-results h3 {
  font-size: 20px;
  color: #343A40;
  margin-bottom: 8px;
}

.no-results p {
  font-size: 16px;
  color: #6C757D;
}

.clear-filters-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #F1F4F9;
  border: 1px solid #DEE2E6;
  border-radius: 4px;
  color: #495057;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-filters-btn:hover {
  background-color: #E9ECEF;
}

/* Loading and Error States */
.loading-state,
.error-state {
  text-align: center;
  padding: 48px;
  background-color: #F8F9FA;
  border-radius: 8px;
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-state p,
.error-state p {
  font-size: 16px;
  color: #6C757D;
}

.retry-btn {
  padding: 8px 16px;
  background-color: #51D59C;
  color: #020A07;
  border: none;
  border-radius: 4px;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.retry-btn:hover {
  background-color: #3DC488;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .integrations-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .empty-state-illustration {
    width: 100%;
    height: auto;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .new-connection-btn {
    width: 100%;
    justify-content: center;
  }

  .integrations-toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    max-width: none;
  }

  .toolbar-actions {
    justify-content: space-between;
  }

  .integrations-grid {
    grid-template-columns: 1fr;
  }

  .integrations-list .integration-card {
    flex-direction: column;
    align-items: flex-start;
  }

  .integrations-list .integration-logo {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .integrations-list .integration-description {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .integrations-list .integration-actions {
    width: 100%;
    flex-direction: column;
    gap: 8px;
  }

  .page-title {
    font-size: 24px;
  }
}
