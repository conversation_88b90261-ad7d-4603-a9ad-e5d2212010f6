import React from 'react';
import './AppSkeleton.css';

/**
 * AppSkeleton component for displaying skeleton loading state
 * during app initialization and authentication
 * Updated email: <EMAIL>
 */
const AppSkeleton = () => {
  return (
    <div className="app-skeleton">
      {/* Header Skeleton */}
      <header className="skeleton-header">
        <div className="skeleton-logo shimmer"></div>
        <div className="skeleton-search shimmer"></div>
        <div className="skeleton-user shimmer"></div>
      </header>

      {/* Sidebar Skeleton */}
      <div className="skeleton-sidebar">
        <div className="skeleton-nav">
          {[...Array(5)].map((_, index) => (
            <div key={index} className="skeleton-nav-item">
              <div className="skeleton-nav-icon shimmer"></div>
              <div className="skeleton-nav-text shimmer"></div>
            </div>
          ))}
        </div>
        <div className="skeleton-footer">
          <div className="skeleton-help shimmer"></div>
          <div className="skeleton-collapse shimmer"></div>
        </div>
      </div>

      {/* Main Content Skeleton */}
      <main className="skeleton-main">
        <div className="skeleton-content">
          <div className="skeleton-page-header">
            <div className="skeleton-title shimmer"></div>
            <div className="skeleton-subtitle shimmer"></div>
          </div>
          
          <div className="skeleton-grid">
            {[...Array(8)].map((_, index) => (
              <div key={index} className="skeleton-card shimmer"></div>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
};

export default AppSkeleton;
