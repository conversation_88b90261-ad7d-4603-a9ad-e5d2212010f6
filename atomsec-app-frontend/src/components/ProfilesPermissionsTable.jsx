import React, { useState, useImperativeHandle, forwardRef } from 'react';
import { AgGridReact } from 'ag-grid-react';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';

// Custom Checkbox Renderer for boolean columns - matches Figma exactly
const CheckboxRenderer = (props) => {
  const val = props.value;
  const norm = val !== undefined && val !== null ? val.toString().toLowerCase() : '';
  if (norm === 'true') {
    // Green checkmark - matches Figma exactly
    return (
      <span style={{ 
        display: 'inline-flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        width: 20, 
        height: 20,
        background: '#51D59C',
        border: '1.5px solid #51D59C',
        borderRadius: '0px'
      }}>
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4 8L7 11L12 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </span>
    );
  } else if (norm === 'false') {
    // Empty box - matches Figma exactly
    return (
      <span style={{ 
        display: 'inline-flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        width: 20, 
        height: 20,
        background: '#F5F7FA',
        border: '1.5px solid #B7C2C7',
        borderRadius: '0px'
      }}>
      </span>
    );
  } else if (val !== undefined && val !== null && val !== '') {
    // Show value as text
    return <span>{val}</span>;
  } else {
    return null;
  }
};

// Custom Tooltip Renderer
const CustomTooltip = (props) => (
  <div style={{ padding: 10 }}>
    <strong>{props.colDef.headerName}</strong>
    <div>{props.value}</div>
  </div>
);

// Normalize field names for columns and row keys
function normalizeFieldName(name) {
  return (name || '').replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
}

const ProfilesPermissionsTable = forwardRef(({ rowData, settings, profileAssignments, permissionSetAssignments, bubbleFilter, handleResetFilters }, ref) => {
  const [gridApi, setGridApi] = useState(null);
  const [columnApi, setColumnApi] = useState(null);
  const [filterMode, setFilterMode] = useState('and'); // 'and' or 'or'

  useImperativeHandle(ref, () => ({
    resetFilters: () => {
      if (gridApi) gridApi.setFilterModel(null);
    }
  }), [gridApi]);

  // Helper to get type label
  function getTypeLabel(type) {
    // Normalize all profile-related types to 'Profile'
    if (type === 'ProfilePermissions' || 
        type === 'MFAProfilePermissions' || 
        type === 'APIWhitelistingProfilePermissions' || 
        type === 'LoginHoursProfilePermissions' ||
        type === 'DeviceActivationProfilePermissions' ||
        type === 'SessionTimeoutProfilePermissions') {
      return 'Profile';
    }
    // Normalize all permission set-related types to 'Permission Set'
    if (type === 'PermissionSetPermissions' || 
        type === 'ProfilePermissionSetAssignment' ||
        type === 'MFAPermissionSetPermissions' ||
        type === 'APIWhitelistingPermissionSetPermissions' ||
        type === 'LoginHoursPermissionSetPermissions' ||
        type === 'DeviceActivationPermissionSetPermissions' ||
        type === 'SessionTimeoutPermissionSetPermissions') {
      return 'Permission Set';
    }
    return type || '';
  }

  // Group and transform data for table
  function transformAndGroupRows(rowData, profileAssignments, permissionSetAssignments, settings) {
    // First, parse OrgValue and map permissions to normalized fields for each row
    const parsedRows = (rowData || []).map(row => {
      let newRow = { ...row };
      if (row.OrgValue) {
        let orgSettings = [];
        try {
          orgSettings = JSON.parse(row.OrgValue);
        } catch (e) {
          orgSettings = [];
        }
        const type = row.Type || row.type || row.rowType || row.row_type || row.Type__c || '';
        (orgSettings || []).forEach(setting => {
          const colKey = normalizeFieldName(setting.SalesforceSetting);
          // For Permission Set, use PermissionSetValue-UserPermissions if present
          let value;
          if (type === 'ProfilePermissionSetAssignment' || type === 'PermissionSetPermissions' || type === 'Permission Set') {
            if (setting['PermissionSetValue-UserPermissions'] !== undefined) {
              value = setting['PermissionSetValue-UserPermissions'];
            } else if (setting.ProfileValue !== undefined) {
              value = setting.ProfileValue;
            } else if (setting.profileValue !== undefined) {
              value = setting.profileValue;
            } else {
              value = setting.Match;
            }
          } else {
            // For Profile, use ProfileValue, then PermissionSetValue-UserPermissions, then profileValue, then Match
            value = setting.ProfileValue;
            if (value === undefined && setting['PermissionSetValue-UserPermissions'] !== undefined) {
              value = setting['PermissionSetValue-UserPermissions'];
            }
            if (value === undefined && setting.profileValue !== undefined) {
              value = setting.profileValue;
            }
            if (value === undefined) value = setting.Match;
          }
          // Set as 'true' or 'false' string
          newRow[colKey] = (value === true || value === 'true') ? 'true' : 'false';
          newRow[`${colKey}_desc`] = setting.Description;
        });
      }
      return newRow;
    });
    console.log('DEBUG parsedRows:', parsedRows);
    // Map for summing assignments by API Name + Type
    const groupMap = {};
    (parsedRows || []).forEach(row => {
      const type = row.Type || row.type || row.rowType || row.row_type || row.Type__c || '';
      const typeLabel = getTypeLabel(type);
      // For permission sets, use PermissionSetName as API Name, else ProfileName
      const apiName = typeLabel === 'Permission Set' ? (row.PermissionSetName || row.permission_set_name || row.ProfileName || row.profile_name || row.APIName) : (row.ProfileName || row.profile_name || row.APIName);
      if (!apiName) return; // skip if no name
      // Group by API Name and base type (Profile or Permission Set), not specific task type
      const groupKey = `${apiName}__${typeLabel}`;
      if (!groupMap[groupKey]) {
        groupMap[groupKey] = {
          APIName: apiName,
          Type: typeLabel,
          AssignmentCount: 0,
          rawRows: [],
        };
      }
      // Sum assignments
      let assignCount = 0;
      if (row.AssignmentCount !== undefined) assignCount = Number(row.AssignmentCount) || 0;
      if (row.assignment_count !== undefined) assignCount = Number(row.assignment_count) || assignCount;
      // For profileAssignments, sum if present
      if (profileAssignments && typeLabel === 'Profile') {
        const pa = profileAssignments.find(a => a.profile_name === apiName);
        if (pa && pa.assignment_count !== undefined) assignCount = Number(pa.assignment_count) || assignCount;
      }
      // For permissionSetAssignments, sum if present
      if (permissionSetAssignments && typeLabel === 'Permission Set') {
        const psa = permissionSetAssignments.find(a => a.permission_set_name === apiName);
        if (psa && psa.assignment_count !== undefined) assignCount = Number(psa.assignment_count) || assignCount;
      }
      groupMap[groupKey].AssignmentCount += assignCount;
      groupMap[groupKey].rawRows.push(row);
    });
    // Merge settings columns (show true if any row in group is true)
    const result = Object.values(groupMap).map(group => {
      const row = {
        APIName: group.APIName,
        Type: group.Type,
        AssignmentCount: group.AssignmentCount,
      };
      (settings || []).forEach(setting => {
        const colKey = setting.field;
        // If any row in group has this permission true, set 'true' as string, else 'false'
        const hasPermission = group.rawRows.some(r => r[colKey] === 'true');
        row[colKey] = hasPermission ? 'true' : 'false';
        // Use first non-empty description
        const desc = group.rawRows.map(r => r[`${colKey}_desc`]).find(d => d);
        if (desc) row[`${colKey}_desc`] = desc;
      });
      return row;
    });
    return result;
  }

  // Toolbar for column show/hide, CSV export, and pagination controls
  const onBtnExport = () => {
    if (gridApi) gridApi.exportDataAsCsv();
  };
  const onShowColumns = () => {
    console.log('gridApi:', gridApi);
    if (gridApi) {
      gridApi.setSideBarVisible(true);
      gridApi.openToolPanel('columns');
    }
  };

  const baseCols = [
    { headerName: 'API Name', field: 'APIName', pinned: 'left', filter: 'agTextColumnFilter', sortable: true, editable: false, resizable: true, cellStyle: { textAlign: 'left', fontWeight: 600 }, checkboxSelection: true, headerCheckboxSelection: true, rowDrag: true, floatingFilter: true },
    { headerName: 'Type', field: 'Type', filter: 'agSetColumnFilter', sortable: true, editable: false, resizable: true, cellStyle: { textAlign: 'center', fontWeight: 500 }, floatingFilter: true },
    { headerName: 'No of Assignments', field: 'AssignmentCount', filter: 'agNumberColumnFilter', sortable: true, editable: false, resizable: true, cellStyle: { textAlign: 'center' }, floatingFilter: true }
  ];
  const dynamicCols = (settings || []).map(setting => ({
    headerName: setting.label,
    field: setting.field,
    cellRenderer: 'CheckboxRenderer',
    tooltipField: `${setting.field}_desc`,
    filter: 'agSetColumnFilter',
    filterParams: { values: null },
    sortable: true,
    editable: false,
    resizable: true,
    minWidth: 120,
    cellStyle: { textAlign: 'center', verticalAlign: 'middle' },
    headerClass: 'ag-center-cols-header',
    floatingFilter: true,
    comparator: (a, b) => {
      // Sort true (checkmark) > false (empty) > other values
      const norm = v => {
        if (v === true || v === 'true' || (typeof v === 'string' && v.toLowerCase() === 'true')) return 2;
        if (v === false || v === 'false' || (typeof v === 'string' && v.toLowerCase() === 'false')) return 1;
        return 0;
      };
      return norm(b) - norm(a); // descending: true > false > other
    },
  }));
  const columnDefs = [...baseCols, ...dynamicCols];

  const gridRows = transformAndGroupRows(rowData, profileAssignments, permissionSetAssignments, settings);

  // Apply bubbleFilter if present (supports both AND and OR filtering)
  let filteredGridRows = gridRows;
  if (Array.isArray(bubbleFilter) && bubbleFilter.length > 0) {
    console.log(`Filtering rows with bubbleFilter (${filterMode.toUpperCase()} mode):`, bubbleFilter);
    
    if (filterMode === 'and') {
      // Use AND logic - row must have ALL selected permissions set to true
      filteredGridRows = gridRows.filter(row => {
        // Check if row has ALL of the selected permissions
        for (const permission of bubbleFilter) {
          // Normalize the permission key to match row property names
          const normKey = permission.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
          
          const val = row[normKey];
          
          // If any permission is false, exclude this row (AND logic)
          if (!(val === true || val === 'true' || (val && val.toString().toLowerCase() === 'true'))) {
            return false;
          }
        }
        
        // If we get here, all permissions matched
        return true;
      });
      
      console.log(`Filtered from ${gridRows.length} to ${filteredGridRows.length} rows using AND logic`);
    } else {
      // Use OR logic - row must have ANY selected permissions set to true
      filteredGridRows = gridRows.filter(row => {
        // Check if row has ANY of the selected permissions
        for (const permission of bubbleFilter) {
          // Normalize the permission key to match row property names
          const normKey = permission.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
          
          const val = row[normKey];
          
          if (val === true || val === 'true' || (val && val.toString().toLowerCase() === 'true')) {
            return true;
          }
        }
        return false;
      });
      
      console.log(`Filtered from ${gridRows.length} to ${filteredGridRows.length} rows using OR logic`);
    }
  }

  // Debug: log settings and first row of gridRows
  console.log('DEBUG settings:', settings);
  if (filteredGridRows && filteredGridRows.length > 0) {
    console.log('DEBUG first gridRow:', filteredGridRows[0]);
  }

  if (!filteredGridRows || filteredGridRows.length === 0) {
    return <div>No profile permissions data available.</div>;
  }

  // Debug logs
  console.log('DEBUG columnDefs:', columnDefs);
  console.log('DEBUG gridRows[0]:', filteredGridRows[0]);

  return (
    <>
      {/* Table Header - matches Figma exactly */}
      <div className="figma-table-header-section">
        <h3 className="figma-table-title">Roles and Permissions</h3>
      </div>
      
      {/* Control Buttons - matches Figma exactly */}
      <div className="figma-table-controls">
        {/* Export CSV Button */}
        <button
          onClick={onBtnExport}
          className="figma-export-btn"
        >
          Export CSV
        </button>
        
        {/* Filter Mode Dropdown */}
        {Array.isArray(bubbleFilter) && bubbleFilter.length > 1 && (
          <>
            <div className="figma-filter-mode-group">
              <span className="figma-filter-mode-label">Filter Mode:</span>
              <select
                value={filterMode}
                onChange={(e) => setFilterMode(e.target.value)}
                className="figma-filter-mode-select"
              >
                <option value="and">Match ALL (AND)</option>
                <option value="or">Match ANY (OR)</option>
              </select>
            </div>
            <button
              onClick={handleResetFilters}
              className="figma-reset-filters-btn"
            >
              Reset Filters
            </button>
          </>
        )}
      </div>
      
      {/* AG Grid Table with Figma styling */}
      <div className="figma-ag-grid-container">
        <style>{`
          /* AG Grid Theme Overrides for Figma look */
          .figma-ag-grid-container .ag-theme-quartz {
            --ag-background-color: #fff;
            --ag-header-background-color: #F5F7FA;
            --ag-header-foreground-color: #1D2433;
            --ag-row-hover-color: #f8fdfb;
            --ag-font-family: 'Lato', sans-serif;
            --ag-border-radius: 4px;
            --ag-card-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
            --ag-row-border-color: rgba(0, 0, 0, 0.08);
            --ag-cell-horizontal-border: solid rgba(0, 0, 0, 0.08);
            --ag-cell-horizontal-border-width: 1px;
            --ag-header-column-separator-color: rgba(0, 0, 0, 0.08);
            --ag-header-column-separator-width: 1px;
            font-size: 14px;
            border: none;
            border-radius: 4px;
            overflow: visible;
            box-sizing: border-box;
          }
          
          /* Header styling to match Figma exactly */
          .figma-ag-grid-container .ag-theme-quartz .ag-header-cell,
          .figma-ag-grid-container .ag-theme-quartz .ag-header-group-cell { 
            background: rgba(0, 0, 0, 0.04) !important;
            font-weight: 600 !important;
            color: #1D2433 !important;
            border: 1px solid rgba(0, 0, 0, 0.08) !important;
            border-bottom: none !important;
            padding: 14px 8px 14px 16px !important;
            font-size: 14px !important;
            font-family: 'Lato', sans-serif !important;
          }
          
          /* Cell styling to match Figma exactly */
          .figma-ag-grid-container .ag-theme-quartz .ag-cell { 
            vertical-align: middle !important;
            border: 1px solid rgba(0, 0, 0, 0.08) !important;
            border-bottom: none !important;
            border-right: none !important;
            padding: 14px 8px 14px 16px !important;
            font-size: 14px !important;
            color: #020A07 !important;
            font-family: 'Lato', sans-serif !important;
          }
          
          /* Row hover effect */
          .figma-ag-grid-container .ag-theme-quartz .ag-row-hover { 
            background: #f8fdfb !important; 
          }
          
          /* Pagination panel styling */
          .figma-ag-grid-container .ag-theme-quartz .ag-paging-panel { 
            height: 60px !important;
            padding: 12px !important;
            border-top: 1px solid #E0E0E0 !important;
            background: #fff !important;
            font-family: 'Lato', sans-serif !important;
          }
          
          /* Column width specifications to match Figma */
          .figma-ag-grid-container .ag-theme-quartz .ag-header-cell:first-child,
          .figma-ag-grid-container .ag-theme-quartz .ag-cell:first-child {
            width: 320px !important;
            min-width: 320px !important;
          }
          .figma-ag-grid-container .ag-theme-quartz .ag-header-cell:nth-child(2),
          .figma-ag-grid-container .ag-theme-quartz .ag-cell:nth-child(2) {
            width: 280px !important;
            min-width: 280px !important;
          }
          .figma-ag-grid-container .ag-theme-quartz .ag-header-cell:nth-child(3),
          .figma-ag-grid-container .ag-theme-quartz .ag-cell:nth-child(3) {
            width: 240px !important;
            min-width: 240px !important;
          }
          .figma-ag-grid-container .ag-theme-quartz .ag-header-cell:nth-child(4),
          .figma-ag-grid-container .ag-theme-quartz .ag-cell:nth-child(4) {
            width: 160px !important;
            min-width: 160px !important;
          }
          .figma-ag-grid-container .ag-theme-quartz .ag-header-cell:nth-child(5),
          .figma-ag-grid-container .ag-theme-quartz .ag-cell:nth-child(5) {
            width: 40px !important;
            min-width: 40px !important;
          }
          
          /* Remove default AG Grid borders and shadows */
          .figma-ag-grid-container .ag-theme-quartz .ag-root-wrapper {
            border: none !important;
            border-radius: 4px !important;
            overflow: visible !important;
            box-shadow: none !important;
          }
          
          .figma-ag-grid-container .ag-theme-quartz .ag-root {
            border: none !important;
            border-radius: 4px !important;
          }
          
          /* Header label styling */
          .figma-ag-grid-container .ag-theme-quartz .ag-header-cell-label {
            font-weight: 600 !important;
            font-size: 14px !important;
            color: #1D2433 !important;
            font-family: 'Lato', sans-serif !important;
          }
          
          /* Remove default AG Grid focus styles */
          .figma-ag-grid-container .ag-theme-quartz .ag-cell:focus {
            outline: none !important;
            border: none !important;
          }
          
          .figma-ag-grid-container .ag-theme-quartz .ag-header-cell:focus {
            outline: none !important;
            border: 1px solid rgba(0, 0, 0, 0.08) !important;
          }
        `}</style>
        <AgGridReact
          rowData={filteredGridRows}
          columnDefs={columnDefs}
          domLayout="normal"
          defaultColDef={{
            sortable: true,
            resizable: true,
            editable: false,
            tooltipComponent: 'CustomTooltip',
            enableRowGroup: true,
            enablePivot: false,
            enableValue: true,
            floatingFilter: true,
            suppressMenu: false,
          }}
          rowSelection="multiple"
          rowDragManaged={true}
          animateRows={true}
          pagination={true}
          paginationPageSize={10}
          paginationPageSizeSelector={[10, 20, 50, 100]}
          suppressRowClickSelection={false}
          clipboard={true}
          enableCellTextSelection={true}
          suppressAggFuncInHeader={true}
          suppressDragLeaveHidesColumns={true}
          suppressRowGroupHidesColumns={true}
          suppressColumnVirtualisation={false}
          suppressScrollOnNewData={false}
          enableRangeSelection={true}
          enableBrowserTooltips={true}
          onGridReady={params => {
            setGridApi(params.api);
            setColumnApi(params.columnApi);
          }}
          components={{
            CheckboxRenderer,
            CustomTooltip,
          }}
        />
      </div>
    </>
  );
});

export default ProfilesPermissionsTable;
