/* Skeleton Card Base Styles */
.skeleton-card {
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  padding: 24px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.skeleton-card:hover {
  transform: translateY(-2px);
  box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.1);
}

/* Shimmer Animation */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Metric Card Skeleton */
.skeleton-metric {
  height: 140px; /* Match DataCard height */
}

.skeleton-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.skeleton-title-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.skeleton-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.skeleton-title {
  width: 120px;
  height: 18px;
}

.skeleton-navigate {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.skeleton-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skeleton-value-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.skeleton-value {
  width: 80px;
  height: 32px;
}

.skeleton-trend {
  width: 60px;
  height: 20px;
}

.skeleton-actions {
  display: flex;
  gap: 8px;
}

.skeleton-refresh {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

/* Chart Skeleton */
.skeleton-chart {
  height: 400px; /* Match SeverityChart height */
}

.skeleton-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.skeleton-chart-title {
  width: 200px;
  height: 24px;
}

.skeleton-chart-actions {
  display: flex;
  gap: 8px;
}

.skeleton-action-btn {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.skeleton-chart-tabs {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.skeleton-tab {
  width: 100px;
  height: 32px;
}

.skeleton-chart-content {
  flex: 1;
}

.skeleton-chart-visualization {
  width: 100%;
  height: 300px;
}

/* Table Skeleton */
.skeleton-table {
  height: 400px; /* Match AllScansTable height */
}

.skeleton-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.skeleton-table-title {
  width: 250px;
  height: 24px;
}

.skeleton-table-actions {
  display: flex;
  gap: 8px;
}

.skeleton-table-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skeleton-table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 16px;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-table-cell {
  height: 16px;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .skeleton-metric {
    height: 120px;
  }
  
  .skeleton-chart {
    height: 350px;
  }
  
  .skeleton-table {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .skeleton-card {
    padding: 16px;
  }
  
  .skeleton-metric {
    height: 100px;
  }
  
  .skeleton-chart {
    height: 300px;
  }
  
  .skeleton-table {
    height: 300px;
  }
  
  .skeleton-table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}
