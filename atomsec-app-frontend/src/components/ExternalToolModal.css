.external-tool-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-out;
}

.external-tool-modal {
  background: white;
  border-radius: 4px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  background: #51D59C;
  color: white;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-title h2 {
  margin: 0;
  font-family: 'Poppins', sans-serif;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-icon {
  font-size: 1.5rem !important;
}

.close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.close-button svg {
  font-size: 1.25rem;
}

.modal-content {
  padding: 1.5rem;
}

.tool-info h3 {
  margin: 0 0 0.75rem 0;
  font-family: 'Poppins', sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: #020A07;
}

.tool-info p {
  margin: 0 0 1.5rem 0;
  font-family: 'Lato', sans-serif;
  color: #393E3C;
  line-height: 1.6;
}

/* Removed .user-info styling that was causing light green button appearance */

.user-info p {
  margin: 0;
  font-family: 'Lato', sans-serif;
  color: #393E3C;
  font-size: 0.9rem;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-family: 'Inter', sans-serif;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background: #51D59C;
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(81, 213, 156, 0.3);
}

.btn-secondary {
  background: #51D59C;
  color: white;
}

.btn-secondary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(81, 213, 156, 0.3);
}

.btn-outline {
  background: transparent;
  color: #020A07;
  border: 2px solid #51D59C;
}

.btn-outline:hover {
  background: #F1FCF7;
  border-color: #51D59C;
}

.btn svg {
  font-size: 1rem;
}

.auto-close-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.auto-close-info p {
  margin: 0;
  font-family: 'Lato', sans-serif;
  color: #856404;
  font-size: 0.85rem;
}

.auto-close-info strong {
  color: #d63031;
  font-weight: 700;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  background: #F1FCF7;
  display: flex;
  justify-content: center;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .external-tool-modal {
    width: 95%;
    margin: 1rem;
  }

  .modal-header {
    padding: 1rem;
  }

  .modal-content {
    padding: 1rem;
  }

  .modal-footer {
    padding: 1rem;
  }

  .action-buttons {
    gap: 0.5rem;
  }

  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .modal-title h2 {
    font-size: 1.1rem;
  }

  .tool-info h3 {
    font-size: 1.25rem;
  }

  .action-buttons {
    flex-direction: column;
  }
}

/* Focus states for accessibility */
.btn:focus,
.close-button:focus {
  outline: 2px solid #51D59C;
  outline-offset: 2px;
}

/* Loading state */
.external-tool-modal.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Success state */
.external-tool-modal.success .modal-header {
  background: linear-gradient(135deg, #00C853 0%, #4CAF50 100%);
} 