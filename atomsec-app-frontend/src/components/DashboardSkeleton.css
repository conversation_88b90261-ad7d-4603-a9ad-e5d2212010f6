/* Dashboard Skeleton Container */
.dashboard-skeleton {
  animation: fadeIn var(--transition-normal);
  padding: 32px;
  font-family: 'Inter', sans-serif;
}

/* Page Header Skeleton */
.skeleton-page-header {
  margin-bottom: 24px;
}

.skeleton-page-title {
  width: 200px;
  height: 32px;
  margin-bottom: 12px;
}

.skeleton-page-description {
  width: 350px;
  height: 20px;
}

/* Stats Container Skeleton */
.skeleton-stats-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 24px;
}

/* Dashboard Content Skeleton */
.skeleton-dashboard-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.skeleton-dashboard-left-column,
.skeleton-dashboard-right-column {
  min-height: 400px;
}

/* Table Section Skeleton */
.skeleton-table-section {
  margin-top: 24px;
}

/* Shimmer Animation */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Fade In Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .skeleton-stats-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .skeleton-dashboard-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-skeleton {
    padding: 16px;
  }
  
  .skeleton-stats-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .skeleton-dashboard-content {
    gap: 16px;
  }
  
  .skeleton-page-title {
    width: 150px;
    height: 28px;
  }
  
  .skeleton-page-description {
    width: 250px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .dashboard-skeleton {
    padding: 12px;
  }
  
  .skeleton-stats-container {
    gap: 12px;
  }
  
  .skeleton-dashboard-content {
    gap: 12px;
  }
}
