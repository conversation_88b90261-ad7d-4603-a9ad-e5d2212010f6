import React from 'react';
import SkeletonCard from './SkeletonCard';
import './DashboardSkeleton.css';

/**
 * DashboardSkeleton component for displaying skeleton loading state
 * Mimics the complete dashboard structure with skeleton components
 */
const DashboardSkeleton = () => {
  return (
    <div className="dashboard-skeleton">
      {/* Page Header Skeleton */}
      <div className="skeleton-page-header">
        <div className="skeleton-page-title shimmer"></div>
        <div className="skeleton-page-description shimmer"></div>
      </div>

      {/* Stats Container Skeleton */}
      <div className="skeleton-stats-container">
        {[...Array(4)].map((_, index) => (
          <SkeletonCard key={index} type="metric" />
        ))}
      </div>

      {/* Dashboard Content Skeleton */}
      <div className="skeleton-dashboard-content">
        <div className="skeleton-dashboard-left-column">
          <SkeletonCard type="chart" />
        </div>
        
        <div className="skeleton-dashboard-right-column">
          <SkeletonCard type="chart" />
        </div>
      </div>

      {/* Table Section Skeleton */}
      <div className="skeleton-table-section">
        <SkeletonCard type="table" />
      </div>
    </div>
  );
};

export default DashboardSkeleton;
