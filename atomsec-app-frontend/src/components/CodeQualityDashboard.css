/* Code Quality Dashboard - Figma Theme */

/* Remove wrapper styles - components are now direct children */
/* .code-quality-dashboard {
  background: #F8FDFB;
  min-height: 100vh;
  font-family: 'Lato', sans-serif;
  padding: 32px;
} */

/* Header Section - Figma Theme */
.figma-dashboard-header {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin: 32px 32px 24px 32px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: calc(100vw - 64px);
  box-sizing: border-box;
  overflow: hidden;
}

.figma-header-left h1.figma-dashboard-title {
  margin: 0 0 16px 0;
  font-family: 'Poppins', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #020A07;
  line-height: 1.4em;
}

.figma-scan-info {
  display: flex;
  gap: 24px;
  align-items: center;
}

.figma-connection-info,
.figma-scan-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #393E3C;
}

.figma-icon-link::before {
  content: "🔗";
  font-size: 16px;
}

.figma-icon-check::before {
  content: "✓";
  color: #51D59C;
  font-weight: bold;
  font-size: 16px;
}

.figma-export-button {
  background: #51D59C;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.figma-export-button:hover {
  background: #45c492;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.figma-icon-download::before {
  content: "⬇";
  font-size: 14px;
}

/* Filter Section - Figma Theme */
.figma-filter-section {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin: 0 32px 24px 32px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: visible;
  width: calc(100vw - 64px);
  max-width: calc(100vw - 64px);
  box-sizing: border-box;
  min-height: 120px;
}

.figma-filters {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  flex-wrap: wrap;
  width: 100%;
  max-width: 100%;
}

.figma-filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 140px;
  flex: 1;
  max-width: 200px;
}

.figma-search-group {
  flex: 2;
  max-width: 300px;
}

.figma-filter-label {
  font-family: 'Lato', sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: #020A07;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  display: block;
}

.figma-filter-select,
.figma-search-input {
  padding: 10px 12px;
  border: 1px solid #ADB5BD;
  border-radius: 6px;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #020A07;
  background: #FFFFFF;
  transition: all 0.2s ease;
  width: 100%;
  height: 40px;
  box-sizing: border-box;
  min-width: 0;
  display: block;
}

.figma-filter-select:focus,
.figma-search-input:focus {
  outline: none;
  border-color: #51D59C;
  box-shadow: 0 0 0 3px rgba(81, 213, 156, 0.1);
}

.figma-search-input {
  flex: 2;
  max-width: 300px;
}

.figma-filter-actions {
  display: flex;
  align-items: center;
  margin-top: 8px;
  align-self: flex-start;
}

.figma-reset-button {
  background: #6C757D;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 40px;
  box-sizing: border-box;
  white-space: nowrap;
}

.figma-reset-button:hover {
  background: #5a6268;
}

/* Main Content - Figma Theme */
.figma-dashboard-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin: 0 32px 24px 32px;
  max-width: calc(100vw - 64px);
  box-sizing: border-box;
  overflow: hidden;
}

.figma-content-left,
.figma-content-right {
  display: flex;
  flex-direction: column;
  gap: 24px;
  min-width: 0;
}

.figma-chart-container {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.12);
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.figma-chart-title {
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 700;
  color: #020A07;
  margin: 0 0 20px 0;
  line-height: 1.4em;
}

/* Chart styling */
.figma-chart-container canvas {
  max-height: 300px;
  max-width: 100%;
}

/* Empty chart states */
.figma-empty-chart-state {
  text-align: center;
  padding: 40px 20px;
  color: #6C757D;
}

.figma-empty-chart-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.figma-empty-chart-subtitle {
  font-size: 14px;
  margin-top: 8px;
  color: #ADB5BD;
}

/* API Versions Chart */
.figma-api-versions-chart {
  margin-top: 20px;
}

.figma-api-bar {
  display: flex;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 16px;
}

.figma-api-segment {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  position: relative;
}

.figma-obsolete {
  background: #FF6B6B;
}

.figma-deprecated {
  background: #FFA500;
}

.figma-current {
  background: #51D59C;
}

.figma-count {
  position: absolute;
  color: white;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.figma-api-legend {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.figma-legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #393E3C;
}

.figma-legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

/* Rules List */
.figma-rules-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
}

.figma-rule-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: #F8F9FA;
  border-radius: 6px;
  border: 1px solid #E9ECEF;
  min-width: 0;
}

.figma-rule-name {
  flex: 1;
  font-size: 14px;
  color: #020A07;
  font-weight: 500;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.figma-rule-bar {
  flex: 2;
  height: 8px;
  background: #E9ECEF;
  border-radius: 4px;
  overflow: hidden;
  min-width: 100px;
  flex-shrink: 0;
}

.figma-rule-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.figma-rule-count {
  font-size: 14px;
  font-weight: 600;
  color: #020A07;
  min-width: 40px;
  text-align: right;
  flex-shrink: 0;
}

/* Metrics Section - Figma Theme */
.figma-metrics-section {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin: 0 32px 24px 32px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.12);
  max-width: calc(100vw - 64px);
  box-sizing: border-box;
  overflow: hidden;
}

.figma-metrics-title {
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 700;
  color: #020A07;
  margin: 0 0 20px 0;
  line-height: 1.4em;
}

.figma-metrics-table-container {
  overflow-x: auto;
  border-radius: 6px;
  border: 1px solid #E9ECEF;
  max-width: 100%;
}

.figma-metrics-table {
  width: 100%;
  border-collapse: collapse;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  min-width: 800px;
}

.figma-metrics-table th {
  background: #F8F9FA;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #020A07;
  border-bottom: 1px solid #E9ECEF;
  white-space: nowrap;
}

.figma-metrics-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #E9ECEF;
  color: #393E3C;
}

.figma-metrics-table tr:hover {
  background: #F8FDFB;
}

.figma-grand-total {
  background: #F8F9FA;
  font-weight: 600;
}

.figma-grand-total td {
  color: #020A07;
}

/* Issues Section - Figma Theme */
.figma-issues-section {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 24px;
  margin: 0 32px 24px 32px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.12);
  max-width: calc(100vw - 64px);
  box-sizing: border-box;
  overflow: hidden;
}

.figma-issues-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.figma-issues-title {
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 700;
  color: #020A07;
  margin: 0;
  line-height: 1.4em;
}

.figma-issues-info {
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #6C757D;
  font-weight: 500;
}

.figma-issues-table-container {
  overflow-x: auto;
  border-radius: 6px;
  border: 1px solid #E9ECEF;
}

.figma-issues-table {
  width: 100%;
  border-collapse: collapse;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
}

.figma-issues-table th {
  background: #F8F9FA;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #020A07;
  border-bottom: 1px solid #E9ECEF;
  white-space: nowrap;
}

.figma-issues-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #E9ECEF;
  color: #393E3C;
}

.figma-issues-table tr:hover {
  background: #F8FDFB;
}

/* Issue Rows and Details - Figma Theme */
.figma-issue-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.figma-issue-row:hover {
  background: #F8FDFB;
}

.figma-issue-row.figma-expanded {
  background: #E8F5E8;
  border-left: 3px solid #51D59C;
}

.figma-file-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #020A07;
}

.figma-icon-arrow {
  width: 0;
  height: 0;
  border-left: 6px solid #6C757D;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  transition: transform 0.2s ease;
}

.figma-icon-arrow.figma-expanded {
  transform: rotate(90deg);
}

.figma-file-type {
  font-size: 12px;
  color: #6C757D;
  text-transform: uppercase;
  font-weight: 500;
}

.figma-issue-count {
  font-weight: 600;
  text-align: center;
  color: #020A07;
}

/* Issue Details */
.figma-issue-details {
  background: #F8F9FA;
}

.figma-issue-details-content {
  padding: 16px;
}

.figma-issue-details-table {
  width: 100%;
  border-collapse: collapse;
  background: #FFFFFF;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.figma-issue-details-table th {
  background: #F1F3F4;
  padding: 12px 16px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: #020A07;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Lato', sans-serif;
}

.figma-issue-details-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #F1F3F4;
  font-size: 14px;
  color: #393E3C;
  font-family: 'Lato', sans-serif;
}

.figma-issue-detail-row:hover {
  background: #F8F9FA;
}

.figma-line-number {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
  color: #51D59C;
  text-align: center;
}

.figma-issue-category {
  text-align: center;
}

.figma-category-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Lato', sans-serif;
}

.figma-category-badge.security {
  background: #FF6B6B;
  color: white;
}

.figma-category-badge.performance {
  background: #FFEAA7;
  color: #020A07;
}

.figma-category-badge.best-practices {
  background: #DDA0DD;
  color: white;
}

.figma-category-badge.code-style {
  background: #96CEB4;
  color: white;
}

.figma-category-badge.design {
  background: #4ECDC4;
  color: white;
}

.figma-category-badge.documentation {
  background: #FF9800;
  color: white;
}

.figma-category-badge.error-prone {
  background: #45B7D1;
  color: white;
}

.figma-category-badge.code-quality {
  background: #98D8C8;
  color: #020A07;
}

.figma-issue-description {
  max-width: 300px;
  line-height: 1.4;
}

.figma-issue-action {
  width: 80px;
  text-align: center;
}

.figma-view-code-button {
  background: #51D59C;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Lato', sans-serif;
}

.figma-view-code-button:hover {
  background: #45B7D1;
  transform: translateY(-1px);
}

.figma-loading-spinner {
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: figma-spin 1s ease-in-out infinite;
}

@keyframes figma-spin {
  to { transform: rotate(360deg); }
}

/* Code Editor */
.figma-code-editor-row {
  background: #F8F9FA;
}

.figma-code-editor-container {
  margin: 10px;
  border: 1px solid #E9ECEF;
  border-radius: 8px;
  overflow: hidden;
  background: #FFFFFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.figma-code-editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #2C3E50;
  color: white;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.figma-code-file-name {
  font-weight: 600;
  color: #51D59C;
}

.figma-code-line-number {
  color: #FF6B6B;
  font-weight: 500;
}

.figma-close-code-button {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.figma-close-code-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.figma-code-editor-content {
  max-height: 400px;
  overflow-y: auto;
  background: #1E1E1E;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.figma-code-line {
  display: flex;
  padding: 2px 16px;
  border-bottom: 1px solid #2D2D2D;
  transition: background-color 0.2s ease;
}

.figma-code-line:hover {
  background: #2D2D2D;
}

.figma-code-line.figma-issue-line {
  background: rgba(255, 107, 107, 0.1);
  border-left: 4px solid #FF6B6B;
  padding-left: 12px;
}

.figma-code-line.figma-issue-line:hover {
  background: rgba(255, 107, 107, 0.15);
}

.figma-code-line .figma-line-number {
  min-width: 50px;
  color: #6C757D;
  font-size: 12px;
  text-align: right;
  margin-right: 16px;
  user-select: none;
}

.figma-code-line.figma-issue-line .figma-line-number {
  color: #FF6B6B;
  font-weight: 600;
}

.figma-code-line .figma-line-content {
  color: #D4D4D4;
  flex: 1;
  white-space: pre-wrap;
  word-break: break-word;
}

.figma-code-line.figma-issue-line .figma-line-content {
  color: #FFFFFF;
  font-weight: 500;
}

/* Loading and Error States - Figma Theme */
.figma-loading-state,
.figma-error-state,
.figma-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 40px 20px;
  margin: 32px;
  background: #FFFFFF;
  border-radius: 8px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
}

.figma-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #F3F3F3;
  border-top: 4px solid #51D59C;
  border-radius: 50%;
  animation: figma-spin 1s linear infinite;
  margin-bottom: 16px;
}

.figma-error-icon,
.figma-empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.code-quality-dashboard.empty h3 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #020A07;
  font-family: 'Poppins', sans-serif;
}

.code-quality-dashboard.empty p {
  margin: 8px 0;
  font-size: 14px;
  color: #6C757D;
  max-width: 600px;
  line-height: 1.5;
  font-family: 'Lato', sans-serif;
}

.code-quality-dashboard.empty p:last-of-type {
  background: #F8F9FA;
  border: 1px solid #E9ECEF;
  border-radius: 6px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  text-align: left;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.figma-retry-button {
  background: #51D59C;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  margin-top: 16px;
  transition: background-color 0.2s;
  font-family: 'Lato', sans-serif;
  font-weight: 600;
}

.figma-retry-button:hover {
  background: #45C492;
}

/* Responsive adjustments for code editor */
@media (max-width: 768px) {
  .figma-code-editor-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .figma-code-editor-content {
    font-size: 12px;
  }
  
  .figma-code-line {
    padding: 2px 12px;
  }
  
  .figma-code-line .figma-line-number {
    min-width: 40px;
    margin-right: 12px;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .figma-dashboard-content {
    grid-template-columns: 1fr;
  }
  
  .figma-dashboard-header,
  .figma-filter-section,
  .figma-dashboard-content,
  .figma-metrics-section,
  .figma-issues-section {
    margin: 0 24px 24px 24px;
    max-width: calc(100vw - 48px);
  }
  
  .figma-filters {
    gap: 12px;
  }
  
  .figma-filter-group {
    min-width: 120px;
    max-width: 180px;
  }
}

@media (max-width: 768px) {
  .figma-dashboard-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
  
  .figma-scan-info {
    flex-direction: column;
    gap: 12px;
  }
  
  .figma-filter-section {
    margin: 0 16px 24px 16px;
    max-width: calc(100vw - 32px);
    padding: 16px;
  }
  
  .figma-filters {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .figma-filter-group {
    width: 100%;
    max-width: none;
    min-width: 0;
  }
  
  .figma-filter-select,
  .figma-search-input {
    width: 100%;
    max-width: none;
  }
  
  .figma-filter-actions {
    align-self: flex-start;
    margin-top: 0;
  }
  
  .figma-dashboard-content {
    margin: 0 16px 24px 16px;
    max-width: calc(100vw - 32px);
  }
  
  .figma-metrics-section {
    margin: 0 16px 24px 16px;
    max-width: calc(100vw - 32px);
  }
  
  .figma-issues-section {
    margin: 0 16px 24px 16px;
    max-width: calc(100vw - 32px);
  }
  
  .figma-loading-state,
  .figma-error-state,
  .figma-empty-state {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
}

@media (max-width: 480px) {
  .figma-dashboard-header,
  .figma-filter-section,
  .figma-dashboard-content,
  .figma-metrics-section,
  .figma-issues-section {
    margin: 0 8px 16px 8px;
    max-width: calc(100vw - 16px);
  }
  
  .figma-filter-section {
    padding: 12px;
  }
  
  .figma-filters {
    gap: 12px;
  }
  
  .figma-filter-group {
    gap: 6px;
  }
  
  .figma-filter-select,
  .figma-search-input {
    height: 36px;
    padding: 8px 10px;
  }
  
  .figma-reset-button {
    height: 36px;
    padding: 8px 12px;
  }
  
  .figma-loading-state,
  .figma-error-state,
  .figma-empty-state {
    margin: 8px;
    max-width: calc(100vw - 16px);
  }
} 