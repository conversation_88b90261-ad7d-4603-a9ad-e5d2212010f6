.scan-wizard-container {
  max-width: 800px;
  margin: 0 auto;
  background: #FFFFFF;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.wizard-header {
  padding: 20px;
  border-bottom: 1px solid #E5E7EB;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-text {
  flex: 1;
}

.header-description {
  margin: 8px 0 0 0;
  color: #6B7280;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 400;
}

.page-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
}

.wizard-content {
  padding: 24px;
}

.integration-options {
  display: grid;
  gap: 16px;
}

.integration-card {
  padding: 16px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #FFFFFF;
}

.integration-card-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.integration-card:hover {
  border-color: #51D59C;
  background-color: #F1FCF7;
}

.integration-card:hover .arrow-icon img {
  filter: invert(72%) sepia(40%) saturate(463%) hue-rotate(116deg) brightness(95%) contrast(85%);
}

.integration-card.selected {
  border-color: #51D59C;
  background-color: #F1FCF7;
}

.integration-card.selected .arrow-icon img {
  filter: invert(72%) sepia(40%) saturate(463%) hue-rotate(116deg) brightness(95%) contrast(85%);
}

.integration-name {
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #020A07;
  flex: 1;
  line-height: 1.6em;
}

.integration-icon {
  width: 58px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.integration-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.arrow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.integration-form {
  max-width: 600px;
  margin: 0 auto;
}

.integration-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.integration-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
}

.integration-description {
  color: #6B7280;
  margin-bottom: 32px;
  line-height: 1.5;
}

.form-group {
  /* margin-bottom: 24px; */
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #D1D5DB;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #51D59C;
  box-shadow: 0 0 0 3px rgba(81, 213, 156, 0.1);
}

.form-group input.error,
.form-group textarea.error {
  border-color: #EF4444;
}

.error-message {
  display: block;
  margin-top: 8px;
  color: #EF4444;
  font-size: 14px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sandbox-checkbox {
  width: 16px;
  height: 16px;
  margin: 0;
}

.button-group {
  display: flex;
  gap: 16px;
  margin-top: 24px;
}

.test-connection-button,
.save-button {
  padding: 12px;
  background-color: #51D59C;
  color: #020A07;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-connection-button:hover,
.save-button:hover {
  background-color: #3DC488;
}

.test-connection-button:disabled,
.save-button:disabled {
  background-color: #A8E9CE;
  cursor: not-allowed;
}

.save-button.saving {
  background-color: #A8E9CE;
  cursor: progress;
}

.save-button.completed {
  background-color: #3DC488;
  cursor: default;
}

.scan-summary {
  max-width: 600px;
  margin: 0 auto;
}

.scan-summary h3 {
  margin-bottom: 24px;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
}

.summary-content {
  background-color: #F9FAFB;
  padding: 24px;
  border-radius: 8px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #E5E7EB;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item .label {
  color: #6B7280;
  font-weight: 500;
}

.summary-item .value {
  color: #111827;
  font-weight: 500;
}

.wizard-footer {
  padding: 20px;
  border-top: 1px solid #E5E7EB;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-button,
.next-button,
.start-scan-button {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.back-button {
  background-color: #F3F4F6;
  color: #374151;
  border: none;
}

.back-button:hover {
  background-color: #E5E7EB;
}

.next-button,
.start-scan-button {
  background-color: #51D59C;
  color: #020A07;
  border: none;
}

.next-button:hover,
.start-scan-button:hover {
  background-color: #3DC488;
}

.next-button:disabled,
.start-scan-button:disabled {
  background-color: #A8E9CE;
  cursor: not-allowed;
}

.integration-description-step {
  max-width: 600px;
  margin: 0 auto;
}

.description-content {
  background-color: #F9FAFB;
  padding: 24px;
  border-radius: 8px;
  margin-top: 24px;
}

.description-content p {
  color: #6B7280;
  line-height: 1.6;
  margin: 0;
}

.environment-toggle,
.auth-method-toggle {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.toggle-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.toggle-option input[type="radio"] {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #D1D5DB;
  border-radius: 50%;
  margin: 0;
  cursor: pointer;
  position: relative;
  transition: all 0.2s;
}

.toggle-option input[type="radio"]:checked {
  border-color: #51D59C;
  background-color: #51D59C;
}

.toggle-option input[type="radio"]:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
}

.toggle-option:hover input[type="radio"] {
  border-color: #51D59C;
}

.toggle-label {
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

.help-text {
  display: block;
  margin-top: 8px;
  color: #6B7280;
  font-size: 12px;
  font-style: italic;
}

/* Custom Checkbox Styles */
.toggle-option input[type="checkbox"] {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #D1D5DB;
  border-radius: 4px; /* Squarish checkbox */
  margin: 0; /* Gap is handled by parent .toggle-option if it has display:flex and gap */
  cursor: pointer;
  position: relative;
  transition: all 0.2s;
  flex-shrink: 0; /* Prevent shrinking if in a flex container */
}

.toggle-option input[type="checkbox"]:checked {
  border-color: #51D59C;
  background-color: #51D59C;
}

.toggle-option input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  display: block;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.toggle-option:hover input[type="checkbox"] {
  border-color: #51D59C;
}

/* Button Re-styling */

/* Base common styles for test and save buttons */
.test-connection-button,
.save-button {
  padding: 12px;
  background-color: #51D59C;
  color: #020A07;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  /* Removed width: 100%; and margin-top: 24px; from here */
}

.test-connection-button:hover,
.save-button:hover {
  background-color: #3DC488;
}

.test-connection-button:disabled,
.save-button:disabled {
  background-color: #A8E9CE;
  cursor: not-allowed;
}

/* Specific styles for the Test Connection button (in the form) */
.test-connection-button {
  display: block; /* Allows margin auto for centering */
  width: auto;    /* Content-based width */
  min-width: 220px; /* Decent minimum size */
  margin: 24px auto 0 auto; /* 24px top margin, centered horizontally */
  padding-left: 25px; /* Adjust padding for a better look with auto width */
  padding-right: 25px;
}

/* Specific styles for the Save button in the wizard footer */
.wizard-footer .save-button {
  padding: 10px 20px; /* Match Next/Previous button padding */
}

.save-button.saving {
  background-color: #A8E9CE;
  cursor: progress;
}

.save-button.completed {
  background-color: #3DC488;
  cursor: default;
}

.scan-wizard-container .wizard-header .close-button {
  background: transparent !important;
  border: none !important;
  cursor: pointer;
  padding: 8px;
  border-radius: 50% !important;
  transition: background-color 0.2s;
  outline: none !important;
  box-shadow: none !important;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  min-height: 40px;
}

.scan-wizard-container .wizard-header .close-button:hover {
  background-color: #F3F4F6 !important;
}

.scan-wizard-container .wizard-header .close-button:focus {
  background-color: #F3F4F6 !important;
  outline: none !important;
  box-shadow: none !important;
}

.scan-wizard-container .wizard-header .close-button .close-icon {
  font-size: 24px;
  color: #6B7280;
  display: block;
  line-height: 1;
}