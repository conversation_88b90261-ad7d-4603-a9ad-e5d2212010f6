import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, Routes, Route, useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';
import './IntegrationTabs.css';
import {
  fetchIntegrationOverviewById,
  fetchIntegrationHealthCheckById,
  fetchIntegrationProfilesById,
  fetchIntegrationPMDById,
  fetchIntegrations,
  fetchPoliciesResultByIntegrationId
} from '../api';
import DataStatusMessage from './DataStatusMessage';
import ProfilesAndPermissionsTab from './ProfilesAndPermissionsTab';
import PMDIssuesTab from './PMDIssuesTab';
import CodeQualityDashboard from './CodeQualityDashboard';
import GuestUserSecurityScan from './GuestUserSecurityScan';
import { Pie, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
} from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';

// Register ChartJS components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
);

// Register datalabels plugin separately
ChartJS.register(ChartDataLabels);

const IntegrationTabs = () => {
  // Get integration ID from URL
  const { integrationId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { taskType, executionLogId } = useParams();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [integration, setIntegration] = useState(null);

  // Tab data states
  const [overviewData, setOverviewData] = useState(null);
  const [overviewStatus, setOverviewStatus] = useState('loading');
  const [healthCheckData, setHealthCheckData] = useState(null);
  const [healthCheckStatus, setHealthCheckStatus] = useState('loading');
  const [profilesData, setProfilesData] = useState(null);
  const [profilesStatus, setProfilesStatus] = useState('loading');
  const [pmdData, setPmdData] = useState(null);
  const [pmdStatus, setPmdStatus] = useState('loading');
  const [riskFilter, setRiskFilter] = useState('all');

  // Pie chart filter state
  const [pieFilter, setPieFilter] = React.useState(null);

  // Bar chart filter state
  const [barFilter, setBarFilter] = React.useState(null); // { group: string, risk: string } or null

  // Sorting state for table columns
  const [sortConfig, setSortConfig] = React.useState({ key: null, direction: 'asc' });

  // Add state for new filters
  const [settingGroupFilter, setSettingGroupFilter] = React.useState('');
  const [riskTypeFilter, setRiskTypeFilter] = React.useState('');

  // Policies result state for health check
  const [policiesResult, setPoliciesResult] = useState([]);
  const [policiesResultStatus, setPoliciesResultStatus] = useState('loading');

  // Pagination state for details table
  const [detailsPage, setDetailsPage] = React.useState(1);
  const detailsPerPage = 10;

  // Refresh flags
  const [isRefreshing, setIsRefreshing] = useState({
    overview: false,
    healthCheck: false,
    profiles: false,
    pmd: false,
  });

  // Global rescan state
  const [isRescanning, setIsRescanning] = useState(false);

  // Ref to store the integration name once loaded - this will never change
  const integrationNameRef = useRef('');

  // Fetch integration details using the integration ID
  useEffect(() => {
    const fetchIntegrationDetails = async () => {
      if (!integrationId) {
        setError('No integration ID provided');
        toast.error('No integration ID provided');
        navigate('/integrations');
        return;
      }

      try {
        setLoading(true);
        // Fetch all integrations to find the one with matching ID
        const response = await fetchIntegrations({ include_inactive: true }); // Include inactive integrations

        // Handle different response structures
        let integrations = [];
        if (response && response.data && response.data.integrations) {
          integrations = response.data.integrations;
        } else if (response && response.integrations) {
          integrations = response.integrations;
        } else if (Array.isArray(response)) {
          integrations = response;
        }

        if (integrations && integrations.length > 0) {
          // Find the integration with the matching ID
          const foundIntegration = integrations.find(
            int => (int.id === integrationId || int.Id === integrationId || int.RowKey === integrationId)
          );

          if (foundIntegration) {
            setIntegration(foundIntegration);
            // Store integration name in ref for faster access - only set once
            if (!integrationNameRef.current) {
              integrationNameRef.current = foundIntegration.name || foundIntegration.Name || 'Unnamed Integration';
            }
            console.log(`Found integration with ID ${integrationId}`);
            console.log('Integration object properties:', Object.keys(foundIntegration));
            console.log('Integration object:', foundIntegration);
          } else {
            setError(`No integration found with ID: ${integrationId}`);
            toast.error(`No integration found with ID: ${integrationId}`);
            navigate('/integrations');
          }
        } else {
          setError('Failed to fetch integrations or no integrations found');
          toast.error('Failed to fetch integrations or no integrations found');
          navigate('/integrations');
        }
      } catch (error) {
        console.error('Error fetching integration details:', error);
        setError(`Error fetching integration details: ${error.message || 'Unknown error'}`);
        toast.error(`Error fetching integration details: ${error.message || 'Unknown error'}`);
        navigate('/integrations');
      } finally {
        setLoading(false);
      }
    };

    fetchIntegrationDetails();
  }, [integrationId, navigate]);

  // Function to fetch data for the active tab
  const fetchTabData = (tab, forceRefresh = false) => {
    // Don't set global loading state when switching tabs
    if (forceRefresh) {
    setLoading(true);
    }
    setError(null);

    switch (tab) {
      case 'overview':
        fetchOverviewData(forceRefresh);
        break;
      case 'healthCheck':
        fetchHealthCheckData(forceRefresh);
        break;
      case 'profilesPermissions':
        fetchProfilesData(forceRefresh);
        break;
      case 'pmdIssues':
        fetchPMDData(forceRefresh);
        break;
      case 'guestSecurityScan':
        // Guest security scan doesn't need data fetching, just set loading to false
        if (forceRefresh) {
        setLoading(false);
        }
        break;
      default:
        handleTabChange('overview');
        fetchOverviewData(forceRefresh);
    }
  };

  // Fetch tab data when integration object is available
  useEffect(() => {
    if (integration && integrationId) {
      fetchTabData(activeTab, false); // Don't force refresh when switching tabs
    }
  }, [integration, integrationId]); // Removed activeTab dependency

  // Fetch overview data
  const fetchOverviewData = async (forceRefresh = false) => {
    try {
      if (!integration) {
        console.error('Integration object is not available yet');
        setOverviewStatus('error');
        setError('Integration details not loaded yet. Please try again in a moment.');
        return;
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, overview: true }));
      }

      setOverviewStatus('loading');
      console.log(`Fetching overview data for integration ID: ${integrationId} with forceRefresh=${forceRefresh}`);

      // Use the integration ID directly instead of tenant URL
      console.log('Using integration ID:', integrationId);

      const response = await fetchIntegrationOverviewById(integrationId, forceRefresh);
      console.log('Overview response:', response);

      // Check data status
      if (response.dataStatus === 'pending') {
        console.log('Overview data status: pending');
        setOverviewStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchOverviewData(), 5000);
      } else if (response.dataStatus === 'empty') {
        console.log('Overview data status: empty');
        setOverviewStatus('empty');
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        console.log('Overview data status: available');
        setOverviewData(response);
        setOverviewStatus('available');
      } else if (response.dataStatus === 'error') {
        console.log('Overview data status: error');
        setOverviewStatus('error');
        setError(response.message || 'Failed to fetch overview data');
      } else {
        console.log(`Unknown overview data status: ${response.dataStatus}`);
        setOverviewStatus('error');
        setError('Unknown data status received from server');
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, overview: false }));
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching overview data:', error);
      setError(`Failed to fetch overview data: ${error.message || 'Unknown error'}`);
      setOverviewStatus('error');
      setLoading(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, overview: false }));
      }

      // Show error toast
      toast.error(`Error fetching overview data: ${error.message || 'Unknown error'}`);
    }
  };

  // Fetch policies result data for health check
  const fetchPoliciesData = async (forceRefresh = false) => {
    setPoliciesResultStatus('loading');
    try {
      // 1. Fetch latest completed health_check task for this integration
      const { getTaskStatus } = await import('../api');
      const taskStatusData = await getTaskStatus(integrationId, 'completed', 1, 'health_check');
      console.log('[DEBUG] taskStatusData:', taskStatusData);
      const latestTask = taskStatusData?.data?.[0];
      console.log('[DEBUG] latestTask:', latestTask);

      if (!latestTask || !latestTask.execution_log_id) {
        console.log('[DEBUG] No latestTask or execution_log_id found');
        setPoliciesResultStatus('empty');
        setPoliciesResult([]);
        return;
      }

      // 2. Fetch policies result for the latest execution_log_id
      console.log('[DEBUG] Calling fetchPoliciesResultByIntegrationId with', integrationId, latestTask.execution_log_id, 'HealthCheck');
      const result = await fetchPoliciesResultByIntegrationId(integrationId, latestTask.execution_log_id, 'HealthCheck');
      console.log('[DEBUG] fetchPoliciesResultByIntegrationId result:', result);
      if (Array.isArray(result)) {
        setPoliciesResult(result);
        setPoliciesResultStatus('available');
      } else if (result && result.data && Array.isArray(result.data)) {
        setPoliciesResult(result.data);
        setPoliciesResultStatus('available');
      } else if (result && result.length === 0) {
        setPoliciesResult([]);
        setPoliciesResultStatus('empty');
      } else {
        setPoliciesResult([]);
        setPoliciesResultStatus('empty');
      }
    } catch (err) {
      console.error('[DEBUG] Error in fetchPoliciesData:', err);
      setPoliciesResultStatus('error');
      setPoliciesResult([]);
    }
  };

  // Reset to page 1 if policiesResult changes
  React.useEffect(() => {
    setDetailsPage(1);
  }, [policiesResult]);

  // Fetch health check data
  const fetchHealthCheckData = async (forceRefresh = false) => {
    try {
      if (!integration) {
        console.error('Integration object is not available yet');
        setHealthCheckStatus('error');
        setError('Integration details not loaded yet. Please try again in a moment.');
        return;
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthCheck: true }));
        // Show a toast notification that we're syncing data
        toast.info('Syncing health check data from Salesforce. This may take a moment...');
      }

      // Use policies data instead of health check data
      await fetchPoliciesData(forceRefresh);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthCheck: false }));
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching health check data:', error);
      setError(`Failed to fetch health check data: ${error.message || 'Unknown error'}`);
      setHealthCheckStatus('error');
      setLoading(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthCheck: false }));
      }

      // Show error toast
      toast.error(`Error fetching health check data: ${error.message || 'Unknown error'}`);
    }
  };

  // Fetch profiles data
  const fetchProfilesData = async (forceRefresh = false) => {
    try {
      if (!integration) {
        console.error('Integration object is not available yet');
        setProfilesStatus('error');
        setError('Integration details not loaded yet. Please try again in a moment.');
        return;
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: true }));
      }

      setProfilesStatus('loading');
      console.log(`Fetching profiles data for integration ID: ${integrationId} with forceRefresh=${forceRefresh}`);

      // Use the integration ID directly instead of tenant URL
      console.log('Using integration ID:', integrationId);

      const response = await fetchIntegrationProfilesById(integrationId, forceRefresh);
      console.log('Profiles response:', response);

      // Check data status
      if (response.dataStatus === 'pending') {
        console.log('Profiles data status: pending');
        setProfilesStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchProfilesData(), 5000);
      } else if (response.dataStatus === 'empty') {
        console.log('Profiles data status: empty');
        setProfilesStatus('empty');
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        console.log('Profiles data status: available');
        console.log('Profiles data:', response);

        // Log detailed information about the profiles and permission sets
        if (response.profiles) {
          console.log(`Profiles count: ${response.profiles.length}`);
          if (response.profiles.length > 0) {
            console.log('Sample profile:', response.profiles[0]);
            console.log('Sample profile system permissions:', response.profiles[0].systemPermissions);
          }
        } else {
          console.warn('No profiles array found in response');
        }

        if (response.permissionSets) {
          console.log(`Permission sets count: ${response.permissionSets.length}`);
          if (response.permissionSets.length > 0) {
            console.log('Sample permission set:', response.permissionSets[0]);
            console.log('Sample permission set system permissions:', response.permissionSets[0].systemPermissions);
          }
        } else {
          console.warn('No permissionSets array found in response');
        }

        setProfilesData(response);
        setProfilesStatus('available');
      } else if (response.dataStatus === 'error') {
        console.log('Profiles data status: error');
        setProfilesStatus('error');
        setError(response.message || 'Failed to fetch profiles data');
      } else {
        console.log(`Unknown profiles data status: ${response.dataStatus}`);
        setProfilesStatus('error');
        setError('Unknown data status received from server');
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: false }));
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching profiles data:', error);
      setError(`Failed to fetch profiles data: ${error.message || 'Unknown error'}`);
      setProfilesStatus('error');
      setLoading(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: false }));
      }

      // Show error toast
      toast.error(`Error fetching profiles data: ${error.message || 'Unknown error'}`);
    }
  };

  // Fetch PMD data
  const fetchPMDData = async (forceRefresh = false) => {
    try {
      if (!integration) {
        console.error('Integration object is not available yet');
        setPmdStatus('error');
        setError('Integration details not loaded yet. Please try again in a moment.');
        return;
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, pmd: true }));
      }

      setPmdStatus('loading');
      console.log(`Fetching PMD data for integration ID: ${integrationId} with forceRefresh=${forceRefresh}`);

      const response = await fetchIntegrationPMDById(integrationId, forceRefresh);
      console.log('PMD response:', response);

      // Check data status
      if (response.dataStatus === 'pending') {
        console.log('PMD data status: pending');
        setPmdStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchPMDData(), 5000);
      } else if (response.dataStatus === 'empty') {
        console.log('PMD data status: empty');
        setPmdStatus('empty');
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        console.log('PMD data status: available');
        console.log('PMD data:', response);

        setPmdData(response);
        setPmdStatus('available');
      } else {
        console.warn('Unknown PMD data status:', response.dataStatus);
        setPmdStatus('error');
        setError('Unknown data status received from server');
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, pmd: false }));
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching PMD data:', error);
      setError(`Failed to fetch PMD data: ${error.message || 'Unknown error'}`);
      setPmdStatus('error');
      setLoading(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, pmd: false }));
      }

      // Show error toast
      toast.error(`Error fetching PMD data: ${error.message || 'Unknown error'}`);
    }
  };

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    // Fetch data for the new tab without affecting global loading state
    if (integration && integrationId) {
      fetchTabData(tab, false);
    }
  };

  // This function is used in the tab components
  const handleRefresh = () => {
    fetchTabData(activeTab, true);
  };

  // Handle global rescan button click
  const handleRescan = async () => {
    try {
      if (!integration) {
        console.error('Integration object is not available yet');
        toast.error('Integration details not loaded yet. Please try again in a moment.');
        return;
      }

      setIsRescanning(true);
      const loadingToast = toast.info('Rescanning Salesforce org. This may take a few moments...', {
        autoClose: false
      });

      // Rescan the org using the integration ID
      console.log('Attempting to rescan org with integration ID:', integrationId);

      let response;

      try {
        console.log('Using scanIntegration with the integration ID');
        const { scanIntegration } = await import('../api');

        console.log('Using integration ID for scan:', integrationId);

        // Update toast to show progress
        toast.update(loadingToast, {
          render: 'Scanning integration... This may take up to 2 minutes.',
          type: 'info',
          autoClose: false
        });

        response = await scanIntegration(integrationId);
        console.log('Scan response:', response);
      } catch (scanError) {
        console.error('Scan failed:', scanError);
        response = {
          success: false,
          error: scanError.message || 'Unknown error during scan'
        };
      }

      console.log('Final rescan response:', response);

      if (response && response.success === false) {
        // Handle different types of errors
        if (response.isTimeout) {
          // Special handling for timeout errors
          toast.update(loadingToast, {
            render: 'Scan is taking longer than expected. The scan is still running in the background. Please check back in a few minutes.',
            type: 'warning',
            autoClose: 8000
          });

          // Set all tabs to pending status since scan might still be running
          setOverviewStatus('pending');
          setHealthCheckStatus('pending');
          setProfilesStatus('pending');
          setPmdStatus('pending');

          // Schedule refreshes to check for completed data
          setTimeout(() => fetchTabData(activeTab), 30000);  // 30 seconds
          setTimeout(() => fetchTabData(activeTab), 60000);  // 1 minute
          setTimeout(() => fetchTabData(activeTab), 120000); // 2 minutes
        } else {
          // Handle other error responses
          toast.update(loadingToast, {
            render: `Failed to initiate rescan: ${response.error || 'Unknown error'}`,
            type: 'error',
            autoClose: 5000
          });
        }
      } else if (response && (response.success || response.data)) {
        toast.update(loadingToast, {
          render: 'Rescan completed successfully. Data will be updated shortly.',
          type: 'success',
          autoClose: 5000
        });

        // Set all tabs to pending status
        setOverviewStatus('pending');
        setHealthCheckStatus('pending');
        setProfilesStatus('pending');
        setPmdStatus('pending');

        // Schedule a refresh of the current tab after a delay
        setTimeout(() => {
          fetchTabData(activeTab);
        }, 5000);

        // Schedule additional refreshes to catch delayed data
        setTimeout(() => {
          fetchTabData(activeTab);
        }, 15000);

        setTimeout(() => {
          fetchTabData(activeTab);
        }, 30000);
      } else {
        toast.update(loadingToast, {
          render: 'Failed to initiate rescan. Please try again.',
          type: 'error',
          autoClose: 5000
        });
      }
    } catch (error) {
      console.error('Error rescanning org:', error);
      toast.error(`Failed to rescan: ${error.message || 'Unknown error'}`);
    } finally {
      setIsRescanning(false);
    }
  };

  // Render overview tab content
  const renderOverviewTab = () => {
    console.log(`Rendering overview tab with status: ${overviewStatus}`);

    if (overviewStatus === 'loading') {
      return (
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Loading overview data...</p>
        </div>
      );
    }

    if (overviewStatus === 'error') {
      return (
        <div className="error-state">
          <div className="error-icon">⚠️</div>
          <p>{error || 'Failed to load overview data'}</p>
          <button className="refresh-button" onClick={() => fetchOverviewData(true)}>
            Try Again
          </button>
        </div>
      );
    }

    if (overviewStatus === 'pending') {
      return (
        <DataStatusMessage
          status="pending"
          message="Overview data is being fetched in the background. This may take a few moments."
          onRefresh={() => fetchOverviewData()}
        />
      );
    }

    if (overviewStatus === 'empty') {
      return (
        <div className="empty-state">
          <div className="empty-icon">📊</div>
          <h3>No Overview Data Available</h3>
          <p>Overview data is not available. Please perform a full rescan from the integrations page if needed, or check back later.</p>
        </div>
      );
    }

    if (overviewStatus === 'available' && overviewData) {
      // Calculate percentages safely to avoid NaN or division by zero
      const totalRisks = overviewData.totalRisks || 0;
      const highRiskPercent = totalRisks > 0 ? (overviewData.highRisks / totalRisks) * 100 : 0;
      const mediumRiskPercent = totalRisks > 0 ? (overviewData.mediumRisks / totalRisks) * 100 : 0;
      const lowRiskPercent = totalRisks > 0 ? (overviewData.lowRisks / totalRisks) * 100 : 0;

      return (
        <div className="overview-content">
          <div className="overview-header">
            <h3>Security Overview</h3>
          </div>

          <div className="overview-stats">
            <div className="stat-card">
              <h4>Health Score</h4>
              <div className={`score-pill ${
                overviewData.healthScore >= 80 ? 'high' :
                overviewData.healthScore >= 60 ? 'medium' : 'low'
              }`}>
                {overviewData.healthScore}%
              </div>
            </div>

            <div className="stat-card">
              <h4>Profiles</h4>
              <div className="stat-value">{overviewData.totalProfiles || 0}</div>
            </div>

            <div className="stat-card">
              <h4>Permission Sets</h4>
              <div className="stat-value">{overviewData.totalPermissionSets || 0}</div>
            </div>

            <div className="stat-card">
              <h4>Total Risks</h4>
              <div className="stat-value">{overviewData.totalRisks || 0}</div>
            </div>
          </div>

          <div className="risk-summary">
            <h4>Risk Summary</h4>
            <div className="risk-bars">
              <div className="risk-bar">
                <span className="risk-label">High</span>
                <div className="bar-container">
                  <div
                    className="bar high"
                    style={{ width: `${highRiskPercent}%` }}
                  ></div>
                </div>
                <span className="risk-count">{overviewData.highRisks || 0}</span>
              </div>

              <div className="risk-bar">
                <span className="risk-label">Medium</span>
                <div className="bar-container">
                  <div
                    className="bar medium"
                    style={{ width: `${mediumRiskPercent}%` }}
                  ></div>
                </div>
                <span className="risk-count">{overviewData.mediumRisks || 0}</span>
              </div>

              <div className="risk-bar">
                <span className="risk-label">Low</span>
                <div className="bar-container">
                  <div
                    className="bar low"
                    style={{ width: `${lowRiskPercent}%` }}
                  ></div>
                </div>
                <span className="risk-count">{overviewData.lowRisks || 0}</span>
              </div>
            </div>
          </div>

          <div className="last-updated">
            Last updated: {new Date(overviewData.lastUpdated).toLocaleString()}
          </div>
        </div>
      );
    }

    // Fallback for any other state
    return (
      <div className="no-data-state">
        <p>No overview data available. Status: {overviewStatus}</p>
        <p>No data was found in the database. Click the <strong>Rescan</strong> button at the top of the page to fetch data from Salesforce.</p>
      </div>
    );
  };

  // Process policies result data for filtering and charts
  const processedPoliciesResult = React.useMemo(() => {
    if (!policiesResult || !Array.isArray(policiesResult)) return [];
    return policiesResult.map(risk => ({
      Weakness: risk.Weakness || risk.riskType || risk.RiskType || 'UNKNOWN_RISK',
      Setting: risk.Setting || risk.setting || 'Unknown Setting',
      SettingGroup: risk.SettingGroup || risk.settingGroup || 'Unknown Group',
      OrgValue: risk.OrgValue || risk.orgValue || 'Unknown',
      StandardValue: risk.StandardValue || risk.standardValue || 'Unknown',
      OWASPCategory: risk.OWASPCategory || risk.owaspCategory || 'Unknown'
    }));
  }, [policiesResult]);

  // Color mapping for risk types
  const riskColorMap = React.useMemo(() => ({
    'HIGH_RISK': '#E57373',
    'MEDIUM_RISK': '#FFD54F',
    'LOW_RISK': '#51D59C',
    'INFORMATIONAL': '#64B5F6',
    'MEETS_STANDARD': '#81C784',
  }), []);

  // Get unique SettingGroup and Weakness values for picklists
  const settingGroupOptions = React.useMemo(() => {
    const set = new Set();
    processedPoliciesResult.forEach(risk => {
      if (risk.SettingGroup) set.add(risk.SettingGroup);
    });
    return Array.from(set);
  }, [processedPoliciesResult]);

  const riskTypeOptions = React.useMemo(() => {
    const set = new Set();
    processedPoliciesResult.forEach(risk => {
      if (risk.Weakness) set.add(risk.Weakness);
    });
    return Array.from(set);
  }, [processedPoliciesResult]);

  // Filtered policies for all charts/tables based on picklist filters
  const filteredPoliciesAll = React.useMemo(() => {
    return processedPoliciesResult.filter(risk => {
      const groupMatch = settingGroupFilter ? (risk.SettingGroup === settingGroupFilter) : true;
      const riskMatch = riskTypeFilter ? (risk.Weakness === riskTypeFilter) : true;
      return groupMatch && riskMatch;
    });
  }, [processedPoliciesResult, settingGroupFilter, riskTypeFilter]);

  // Pie chart data - now filtered by bar chart selection
  const pieData = React.useMemo(() => {
    let filteredData = filteredPoliciesAll;
    
    // Apply bar chart filter to pie chart data
    if (barFilter && barFilter.group) {
      filteredData = filteredData.filter(risk => risk.SettingGroup === barFilter.group);
    }
    
    const riskCounts = {};
    filteredData.forEach(risk => {
      if (!riskCounts[risk.Weakness]) riskCounts[risk.Weakness] = 0;
      riskCounts[risk.Weakness]++;
    });
    const labels = Object.keys(riskCounts);
    const data = labels.map(label => riskCounts[label]);
    const backgroundColor = labels.map(label => riskColorMap[label] || '#BDBDBD');
    
    console.log('Pie Chart Data Debug:', {
      filteredDataLength: filteredData.length,
      riskCounts,
      labels,
      data,
      backgroundColor
    });
    
    return {
      labels,
      datasets: [
        {
          data,
          backgroundColor,
          borderColor: backgroundColor,
          borderWidth: 2,
        },
      ],
    };
  }, [filteredPoliciesAll, barFilter, riskColorMap]);

  // Pie chart ref for potential future use
  const pieChartRef = React.useRef();

  // Bar chart ref for potential future use
  const barChartRef = React.useRef();

  const pieOptions = React.useMemo(() => ({
    plugins: {
      legend: { display: false },
      datalabels: {
        display: true,
        color: '#020A07',
        font: {
          family: 'Lato',
          size: 14,
          weight: '600'
        },
        formatter: function(value, context) {
          console.log('Pie datalabels formatter:', value, context);
          return value;
        },
        anchor: 'center',
        align: 'center',
        offset: 0
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.label}: ${context.parsed}`;
          }
        },
        titleFont: {
          family: 'Lato',
          size: 12,
          weight: '400'
        },
        bodyFont: {
          family: 'Lato',
          size: 12,
          weight: '400'
        }
      }
    },
    responsive: true,
    maintainAspectRatio: false,
    events: ['click', 'mousemove'],
    onClick: (event, elements) => {
      if (elements && elements.length > 0) {
        const element = elements[0];
        const idx = element.index;
        const label = pieData.labels[idx];
        console.log('Pie chart clicked:', label);
        
        // Set the filter
        setPieFilter(label);
        
        // Clear other filters
        setBarFilter(null);
        setSettingGroupFilter('');
        setRiskTypeFilter('');
        
        // Reset to first page when applying new filter
        setDetailsPage(1);
      }
    },
    onHover: (event, elements) => {
      event.native.target.style.cursor = elements.length ? 'pointer' : 'default';
    }
  }), [pieData.labels]);

  // Dynamic risk types (excluding MEETS_STANDARD)
  const riskTypes = React.useMemo(() => {
    const set = new Set();
    filteredPoliciesAll.forEach(risk => {
      if (risk.Weakness && risk.Weakness !== 'MEETS_STANDARD') set.add(risk.Weakness);
    });
    return Array.from(set);
  }, [filteredPoliciesAll]);

  // Dynamic bar chart data
  const barData = React.useMemo(() => {
    // Group by SettingGroup and RiskType (excluding MEETS_STANDARD)
    const groupMap = {};
    filteredPoliciesAll.forEach(risk => {
      if (risk.Weakness === 'MEETS_STANDARD') return;
      const group = risk.SettingGroup || 'Other';
      const riskType = risk.Weakness;
      if (!groupMap[group]) groupMap[group] = {};
      if (!groupMap[group][riskType]) groupMap[group][riskType] = 0;
      groupMap[group][riskType]++;
    });
    
    const labels = Object.keys(groupMap);
    
    // For each riskType, build a dataset with correct stacking
    const datasets = riskTypes.map(riskType => ({
      label: riskType,
      data: labels.map(group => groupMap[group][riskType] || 0),
      backgroundColor: riskColorMap[riskType] || '#BDBDBD',
      borderColor: riskColorMap[riskType] || '#BDBDBD',
      borderWidth: 1,
      stack: 'Stack 0',
    }));
    
    // Calculate total counts for each group for tooltip display
    const totalCounts = labels.map(group => {
      let total = 0;
      riskTypes.forEach(riskType => {
        total += groupMap[group][riskType] || 0;
      });
      return total;
    });
    
    // Debug logging
    console.log('Bar Chart Data Debug:', {
      groupMap,
      labels,
      datasets: datasets.map(ds => ({ label: ds.label, data: ds.data })),
      totalCounts,
      riskTypes
    });
    
    // Log individual dataset values for debugging
    datasets.forEach((dataset, idx) => {
      console.log(`Dataset ${idx} (${dataset.label}):`, dataset.data);
    });
    
    return { 
      labels, 
      datasets,
      totalCounts, // Add total counts for tooltip
      groupMap // Return groupMap for use in barOptions
    };
  }, [filteredPoliciesAll, riskTypes, riskColorMap]);

  const barOptions = React.useMemo(() => ({
    plugins: {
      legend: { display: false },
      tooltip: {
        callbacks: {
          title: function(context) {
            if (context && context.length > 0) {
              return context[0].label;
            }
            return '';
          },
          label: function(context) {
            if (context && context.dataset && context.dataset.label) {
              const riskType = context.dataset.label;
              const count = context.parsed.x;
              return `${riskType}: ${count}`;
            }
            return '';
          },
          afterLabel: function(context) {
            if (context && context.length > 0 && context[0] && 
                typeof context[0].dataIndex !== 'undefined' && 
                barData && barData.totalCounts) {
              const groupIndex = context[0].dataIndex;
              const total = barData.totalCounts[groupIndex];
              if (total !== undefined) {
                return `Total: ${total}`;
              }
            }
            return '';
          }
        },
        titleFont: {
          family: 'Lato',
          size: 12,
          weight: '400'
        },
        bodyFont: {
          family: 'Lato',
          size: 12,
          weight: '400'
        }
      },
      datalabels: {
        display: function(context) {
          // Only show labels for non-zero values
          const value = context.dataset.data[context.dataIndex];
          return value > 0;
        },
        color: '#020A07',
        font: {
          family: 'Lato',
          size: 12,
          weight: '600'
        },
        formatter: function(value, context) {
          console.log('Bar datalabels formatter:', value, context);
          // Return the actual data value
          return value;
        },
        anchor: 'center',
        align: 'center'
      }
    },
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y',
    scales: {
      x: {
        stacked: true,
        beginAtZero: true,
        max: Math.max(...riskTypes.map(riskType => {
          const maxValue = Math.max(...barData.labels.map(group => barData.groupMap[group]?.[riskType] || 0));
          return maxValue;
        }), 1),
        ticks: {
          stepSize: 1,
          precision: 0,
          font: {
            family: 'Lato',
            size: 12,
            weight: '400'
          }
        }
      },
      y: {
        stacked: true,
        ticks: {
          font: {
            family: 'Lato',
            size: 12,
            weight: '400'
          }
        }
      }
    },
    events: ['click', 'mousemove'],
    onClick: (event, elements) => {
      if (elements && elements.length > 0) {
        const element = elements[0];
        const groupIndex = element.index;
        const datasetIndex = element.datasetIndex;
        const group = barData.labels[groupIndex];
        const riskType = barData.datasets[datasetIndex].label;
        console.log('Bar chart clicked:', { group, riskType });
        
        // Set the filter for both group and risk type
        setBarFilter({ group, risk: riskType });
        
        // Clear pie chart filter when bar chart is filtered
        setPieFilter(null);
        // Clear other filters
        setSettingGroupFilter('');
        setRiskTypeFilter('');
        
        // Reset to first page when applying new filter
        setDetailsPage(1);
      }
    },
    onHover: (event, elements) => {
      event.native.target.style.cursor = elements.length ? 'pointer' : 'default';
    }
  }), [barData, riskTypes]);

  // Helper function to format Risk Type for display (removes underscores, keeps filtering logic intact)
  const formatRiskTypeForDisplay = (riskType) => {
    if (!riskType) return '';
    return riskType.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
  };

  // Filtered policies for the table below (combine with pie filter if needed)
  const filteredPoliciesForTable = React.useMemo(() => {
    let filtered = filteredPoliciesAll;
    
    // Apply pie chart filter (risk type)
    if (pieFilter) {
      filtered = filtered.filter(risk => risk.Weakness === pieFilter);
    }
    
    // Apply bar chart filter (setting group and risk type)
    if (barFilter) {
      if (barFilter.group) {
        filtered = filtered.filter(risk => risk.SettingGroup === barFilter.group);
      }
      if (barFilter.risk) {
        filtered = filtered.filter(risk => risk.Weakness === barFilter.risk);
      }
    }
    
    // Apply dropdown filters
    if (settingGroupFilter) {
      filtered = filtered.filter(risk => risk.SettingGroup === settingGroupFilter);
    }
    if (riskTypeFilter) {
      filtered = filtered.filter(risk => risk.Weakness === riskTypeFilter);
    }
    
    console.log('Filtering debug:', {
      originalCount: filteredPoliciesAll.length,
      pieFilter,
      barFilter,
      settingGroupFilter,
      riskTypeFilter,
      filteredCount: filtered.length
    });
    
    return filtered;
  }, [filteredPoliciesAll, pieFilter, barFilter, settingGroupFilter, riskTypeFilter]);

  // Sort handler
  const handleSort = (key) => {
    setSortConfig((prev) => {
      if (prev.key === key) {
        // Toggle direction
        return { key, direction: prev.direction === 'asc' ? 'desc' : 'asc' };
      }
      return { key, direction: 'asc' };
    });
    // Reset to first page when sorting changes
    setDetailsPage(1);
  };

  // Sort the filtered policies
  const sortedPolicies = React.useMemo(() => {
    if (!sortConfig.key) return filteredPoliciesForTable;
    
    return [...filteredPoliciesForTable].sort((a, b) => {
      let aValue, bValue;
      
      switch (sortConfig.key) {
        case 'RiskType':
          aValue = (a.Severity || a.severity || a.Weakness || '').toLowerCase();
          bValue = (b.Severity || b.severity || b.Weakness || '').toLowerCase();
          break;
        case 'Setting':
          aValue = (a.Setting || a.setting || '').toLowerCase();
          bValue = (b.Setting || b.setting || '').toLowerCase();
          break;
        case 'SettingGroup':
          aValue = (a.SettingGroup || a.settingGroup || '').toLowerCase();
          bValue = (b.SettingGroup || b.settingGroup || '').toLowerCase();
          break;
        case 'OrgValue':
          aValue = (a.OrgValue || a.orgValue || '').toLowerCase();
          bValue = (b.OrgValue || b.orgValue || '').toLowerCase();
          break;
        case 'StandardValue':
          aValue = (a.StandardValue || a.standardValue || '').toLowerCase();
          bValue = (b.StandardValue || b.standardValue || '').toLowerCase();
          break;
        default:
          return 0;
      }
      
      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
  }, [filteredPoliciesForTable, sortConfig]);

  // Pagination for sorted policies
  const totalDetailsPages = Math.ceil(sortedPolicies.length / detailsPerPage);
  
  // Ensure current page is valid when filters change
  React.useEffect(() => {
    if (detailsPage > totalDetailsPages && totalDetailsPages > 0) {
      setDetailsPage(1);
    }
  }, [totalDetailsPages, detailsPage]);
  
  const paginatedPolicies = React.useMemo(() => {
    const start = (detailsPage - 1) * detailsPerPage;
    return sortedPolicies.slice(start, start + detailsPerPage);
  }, [sortedPolicies, detailsPage, detailsPerPage]);

  // Reset handler
  const handleResetFilters = () => {
    setSettingGroupFilter('');
    setRiskTypeFilter('');
    setPieFilter(null);
    setBarFilter(null);
    setDetailsPage(1); // Reset to first page
    console.log('All filters reset');
  };

  // Render health check tab content
  const renderHealthCheckTab = () => {
    console.log(`Rendering health check tab with policiesResultStatus: ${policiesResultStatus}`);

    if (policiesResultStatus === 'loading') {
      return (
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Loading health check data...</p>
        </div>
      );
    }

    if (policiesResultStatus === 'error') {
      return (
        <div className="error-state">
          <div className="error-icon">⚠️</div>
          <p>{error || 'Failed to load health check data'}</p>
          <button className="refresh-button" onClick={() => fetchHealthCheckData(true)}>
            Try Again
          </button>
        </div>
      );
    }

    if (policiesResultStatus === 'pending') {
      return (
        <DataStatusMessage
          status="pending"
          message="Health check data is being fetched in the background. This may take a few moments."
          onRefresh={() => fetchHealthCheckData()}
        />
      );
    }

    if (policiesResultStatus === 'empty' || !policiesResult || policiesResult.length === 0) {
      return (
        <div className="empty-state">
          <div className="empty-icon">🔍</div>
          <h3>No Health Check Data Available</h3>
          <p>No data was found in the database. Click the <strong>Rescan</strong> button at the top of the page to fetch security health check data from Salesforce.</p>
          <button className="refresh-button" onClick={() => fetchHealthCheckData(true)}>
            Rescan
          </button>
        </div>
      );
    }

    // Available state: show health check results from policiesResult
    if (policiesResultStatus === 'available' && policiesResult && policiesResult.length > 0) {
      return (
        <div className="health-check-content">
          {/* Filter Section */}
          <div className="filter-section">
            <div className="filter-header">
              <h2 className="filter-title">Filter By:</h2>
              <div className="filter-controls">
                <div className="filter-input-container">
            <select
              className="filter-select"
              value={settingGroupFilter}
              onChange={e => setSettingGroupFilter(e.target.value)}
            >
                    <option value="">Setting Group</option>
              {settingGroupOptions.map(group => (
                <option key={group} value={group}>{group}</option>
              ))}
            </select>
                  <svg className="filter-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M7 10l5 5 5-5z" fill="#51D59C"/>
                  </svg>
                </div>
                
                <div className="filter-input-container">
            <select
              className="filter-select"
              value={riskTypeFilter}
              onChange={e => setRiskTypeFilter(e.target.value)}
            >
                    <option value="">Risk Type</option>
              {riskTypeOptions.map(risk => (
                <option key={risk} value={risk}>{risk}</option>
              ))}
            </select>
                  <svg className="filter-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M7 10l5 5 5-5z" fill="#51D59C"/>
                  </svg>
                </div>
                
            <button className="filter-reset-btn" onClick={handleResetFilters}>
                  <svg className="reset-icon" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M12 5V2L7 7l5 5V8c3.31 0 6 2.69 6 6 0 1.3-.42 2.5-1.13 3.47l1.46 1.46C19.07 17.07 20 15.13 20 13c0-4.42-3.58-8-8-8z" fill="#020A07"/>
                  </svg>
              Reset
            </button>
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="charts-section">
            <div className="chart-card pie-chart-card">
              <h3 className="chart-title">
                Distribution of Risk Types
                {barFilter && barFilter.group && (
                  <span style={{ 
                    fontSize: '14px', 
                    fontWeight: '400', 
                    color: '#666666',
                    marginLeft: '8px'
                  }}>
                    (Filtered by {barFilter.group})
                  </span>
                )}
              </h3>
              <div className="chart-content">
                <div className="pie-chart-container">
                  <Pie 
                    ref={pieChartRef} 
                    data={pieData} 
                    options={pieOptions}
                    plugins={[ChartDataLabels]}
                  />
              </div>
                <div className="pie-chart-legend">
                {pieData.labels.map((label, idx) => (
                    <div key={label} className="legend-item" onClick={() => setPieFilter(label)}>
                      <span 
                        className={`legend-color ${label.toLowerCase().replace(/\s+/g, '-')}`}
                        style={{ 
                          backgroundColor: pieData.datasets[0].backgroundColor[idx],
                          border: pieFilter === label ? '2px solid #393E3C' : 'none'
                        }}
                      ></span>
                      <span className="legend-text" style={{ fontWeight: pieFilter === label ? 700 : 400 }}>
                        {formatRiskTypeForDisplay(label)}
                      </span>
                  </div>
                ))}
                {pieFilter && (
                    <button 
                      className="clear-filter-btn"
                      onClick={() => setPieFilter(null)}
                    >
                    Clear Filter
                  </button>
                )}
              </div>
            </div>
              </div>
            
            <div className="chart-card bar-chart-card">
              <h3 className="chart-title">Misaligned Settings</h3>
              <div className="chart-content">
                <div className="bar-chart-container">
                  <Bar 
                    ref={barChartRef} 
                    data={barData} 
                    options={barOptions}
                    plugins={[ChartDataLabels]}
                  />
                </div>
              </div>
              <div className="bar-chart-legend">
                {riskTypes.map((riskType, idx) => (
                  <div
                    key={riskType}
                    className="legend-item"
                    onClick={() => {
                      setRiskTypeFilter(riskType);
                      setBarFilter({ risk: riskType });
                    }}
                  >
                    <span 
                      className="legend-color"
                      style={{ 
                        backgroundColor: riskColorMap[riskType] || '#BDBDBD',
                        border: barFilter && barFilter.risk === riskType ? '2px solid #393E3C' : 'none'
                      }}
                    ></span>
                    <span className="legend-text" style={{ fontWeight: barFilter && barFilter.risk === riskType ? 700 : 400 }}>
                      {formatRiskTypeForDisplay(riskType)}
                    </span>
                  </div>
                ))}
                {barFilter && (
                  <button 
                    className="clear-filter-btn"
                    onClick={() => { 
                      setBarFilter(null); 
                      setRiskTypeFilter(''); 
                    }}
                  >
                    Clear Filter
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Details Section */}
          <div className="details-section">
            <div className="details-header">
              <h4 className="details-title">Details</h4>
              <p className="details-description">
                Measures how closely your org's security settings align with Salesforce best practices.
              </p>
            </div>
            
            <div className="details-table">
              <table>
              <thead>
                  <tr>
                    <th className="sortable-header" onClick={() => handleSort('RiskType')}>
                      <div className="header-content">
                        <span>Risk Type</span>
                        <div className="sort-icon">
                          {sortConfig.key === 'RiskType' ? (
                            sortConfig.direction === 'asc' ? (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M7 14l5-5 5 5z" fill="#51D59C"/>
                              </svg>
                            ) : (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M7 10l5 5 5-5z" fill="#51D59C"/>
                              </svg>
                            )
                          ) : (
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                              <path d="M7 10l5 5 5-5z" fill="#BDBDBD"/>
                            </svg>
                          )}
                        </div>
                      </div>
                  </th>
                    <th className="sortable-header" onClick={() => handleSort('Setting')}>
                      <div className="header-content">
                        <span>Setting</span>
                        <div className="sort-icon">
                          {sortConfig.key === 'Setting' ? (
                            sortConfig.direction === 'asc' ? (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M7 14l5-5 5 5z" fill="#51D59C"/>
                              </svg>
                            ) : (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M7 10l5 5 5-5z" fill="#51D59C"/>
                              </svg>
                            )
                          ) : (
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                              <path d="M7 10l5 5 5-5z" fill="#BDBDBD"/>
                            </svg>
                          )}
                        </div>
                      </div>
                  </th>
                    <th className="sortable-header" onClick={() => handleSort('SettingGroup')}>
                      <div className="header-content">
                        <span>Setting Group</span>
                        <div className="sort-icon">
                          {sortConfig.key === 'SettingGroup' ? (
                            sortConfig.direction === 'asc' ? (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M7 14l5-5 5 5z" fill="#51D59C"/>
                              </svg>
                            ) : (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M7 10l5 5 5-5z" fill="#51D59C"/>
                              </svg>
                            )
                          ) : (
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                              <path d="M7 10l5 5 5-5z" fill="#BDBDBD"/>
                            </svg>
                          )}
                        </div>
                      </div>
                  </th>
                    <th className="sortable-header" onClick={() => handleSort('OrgValue')}>
                      <div className="header-content">
                        <span>Org Value</span>
                        <div className="sort-icon">
                          {sortConfig.key === 'OrgValue' ? (
                            sortConfig.direction === 'asc' ? (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M7 14l5-5 5 5z" fill="#51D59C"/>
                              </svg>
                            ) : (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M7 10l5 5 5-5z" fill="#51D59C"/>
                              </svg>
                            )
                          ) : (
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                              <path d="M7 10l5 5 5-5z" fill="#BDBDBD"/>
                            </svg>
                          )}
                        </div>
                      </div>
                  </th>
                    <th className="sortable-header" onClick={() => handleSort('StandardValue')}>
                      <div className="header-content">
                        <span>Standard Value</span>
                        <div className="sort-icon">
                          {sortConfig.key === 'StandardValue' ? (
                            sortConfig.direction === 'asc' ? (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M7 14l5-5 5 5z" fill="#51D59C"/>
                              </svg>
                            ) : (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M7 10l5 5 5-5z" fill="#51D59C"/>
                              </svg>
                            )
                          ) : (
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                              <path d="M7 10l5 5 5-5z" fill="#BDBDBD"/>
                            </svg>
                          )}
                        </div>
                      </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                  {paginatedPolicies.map((row, idx) => {
                    const severity = row.Severity || row.severity || row.Weakness || '';
                    const severityClass = severity.toLowerCase().includes('high') ? 'high-severity' :
                                         severity.toLowerCase().includes('medium') ? 'medium-severity' :
                                         'low-severity';
                    
                    return (
                      <tr key={idx}>
                        <td>
                          <span className={`severity-badge ${severityClass}`}>
                            {formatRiskTypeForDisplay(severity)}
                          </span>
                        </td>
                        <td>{row.Setting || row.setting}</td>
                        <td>{row.SettingGroup || row.settingGroup || 'N/A'}</td>
                        <td>{row.OrgValue || row.orgValue}</td>
                        <td>{row.StandardValue || row.standardValue}</td>
                  </tr>
                    );
                  })}
              </tbody>
            </table>
              
              {/* Pagination Controls */}
          {totalDetailsPages > 1 && (
                <div className="pagination-controls">
                  <button 
                    className="pagination-btn" 
                    onClick={() => setDetailsPage(p => Math.max(1, p - 1))} 
                    disabled={detailsPage === 1}
                  >
                    Previous
                  </button>
                  <span className="pagination-info">
                    Page {detailsPage} of {totalDetailsPages}
                  </span>
                  <button 
                    className="pagination-btn" 
                    onClick={() => setDetailsPage(p => Math.min(totalDetailsPages, p + 1))} 
                    disabled={detailsPage === totalDetailsPages}
                  >
                    Next
                  </button>
            </div>
          )}
            </div>
          </div>
        </div>
      );
    }

    // Fallback for any other state
    return (
      <div className="no-data-state">
        <p>No health check data available. Status: {healthCheckStatus}</p>
        <p>No data was found in the database. Click the <strong>Rescan</strong> button at the top of the page to fetch data from Salesforce.</p>
      </div>
    );
  };

  // Render profiles tab content
  const renderProfilesTab = () => {
    console.log(`Rendering profiles tab with status: ${profilesStatus}`);

    return <ProfilesAndPermissionsTab orgId={integrationId} />;
  };

  // Render PMD tab content
  const renderPMDTab = () => {
    console.log(`Rendering Code Quality tab with status: ${pmdStatus}`);

    return <CodeQualityDashboard />;
  };

  // Render Guest User Security Scan tab content
  const renderGuestSecurityScanTab = () => {
    console.log('Rendering Guest User Security Scan tab');
    return <GuestUserSecurityScan />;
  };

  return (
    <div className="integration-tabs">
      {/* Header Card */}
      <div className="header-card">
        <div className="header-content">
          <div className="header-text">
        {loading ? (
              <h2 className="org-name">Loading integration details...</h2>
        ) : error ? (
              <h2 className="org-name">Error: {error}</h2>
            ) : (
              <h2 className="org-name">{integrationNameRef.current || 'Integration Details'}</h2>
            )}
            {integration && (
              <h3 className="last-synced">Last Synced on: {new Date(integration.lastScan || integration.LastScan || Date.now()).toLocaleString()}</h3>
            )}
          </div>
          <div className="header-actions">
            <button
              className="rescan-button"
              onClick={handleRescan}
              disabled={isRescanning}
            >
              <svg 
                width="16" 
                height="16" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
                className="refresh-icon"
              >
                <path d="M23 4v6h-6"/>
                <path d="M1 20v-6h6"/>
                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
              </svg>
              {isRescanning ? 'Rescanning...' : 'Re-Scan'}
            </button>
            <button className="more-options-button">
              <span className="more-options-icon">⋮</span>
            </button>
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="tabs-container">
        <div className="tabs">
          <button
            className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => handleTabChange('overview')}
          >
            Overview
          </button>
          <button
            className={`tab-button ${activeTab === 'healthCheck' ? 'active' : ''}`}
            onClick={() => handleTabChange('healthCheck')}
          >
            Health Check
          </button>
          <button
            className={`tab-button ${activeTab === 'profilesPermissions' ? 'active' : ''}`}
            onClick={() => handleTabChange('profilesPermissions')}
          >
            Profiles and Permission Sets
          </button>
          <button
            className={`tab-button ${activeTab === 'pmdIssues' ? 'active' : ''}`}
            onClick={() => handleTabChange('pmdIssues')}
          >
            Code Quality
          </button>
          <button
            className={`tab-button ${activeTab === 'guestSecurityScan' ? 'active' : ''}`}
            onClick={() => handleTabChange('guestSecurityScan')}
          >
            Guest User Live Security Scan
          </button>
        </div>
      </div>

      <div className="tab-content">
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'healthCheck' && renderHealthCheckTab()}
        {activeTab === 'profilesPermissions' && renderProfilesTab()}
        {activeTab === 'pmdIssues' && renderPMDTab()}
        {activeTab === 'guestSecurityScan' && renderGuestSecurityScanTab()}
      </div>
    </div>
  );
};

export default IntegrationTabs;