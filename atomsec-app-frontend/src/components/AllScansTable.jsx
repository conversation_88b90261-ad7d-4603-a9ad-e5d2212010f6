import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import './Dashboard.css';

// Import SVG assets
import searchIcon from '../assets/dashboard/search.svg';
import addCircleIcon from '../assets/dashboard/add_circle_outline.svg';
import arrowUpIcon from '../assets/dashboard/arrow_upward.svg';
import arrowDownIcon from '../assets/dashboard/arrow_downward.svg';

/**
 * AllScansTable component for displaying a list of all scans
 * Designed to match the Figma design with enhanced functionality
 *
 * @param {Object} props - Component props
 * @param {Array} props.integrations - Array of integration objects
 * @param {Function} props.onRescan - Callback function when rescan button is clicked
 */
const AllScansTable = ({ integrations, onRescan }) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredIntegrations, setFilteredIntegrations] = useState([]);
  const [sortConfig, setSortConfig] = useState({
    key: null,
    direction: 'asc'
  });

  // Debug logging
  console.log('AllScansTable received integrations:', integrations);
  console.log('AllScansTable integrations type:', typeof integrations);
  console.log('AllScansTable integrations length:', integrations?.length);

  // Filter and sort integrations based on search term and sort config
  useEffect(() => {
    console.log('AllScansTable useEffect - integrations changed:', integrations);
    let filtered = integrations;
    
    if (searchTerm.trim()) {
      filtered = integrations.filter(integration => {
        const name = integration.name || integration.Name || '';
        const environment = integration.environment || integration.Environment || '';

        return (
          name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          environment.toLowerCase().includes(searchTerm.toLowerCase())
        );
      });
    }

    // Apply sorting
    if (sortConfig.key) {
      filtered = [...filtered].sort((a, b) => {
        let aValue, bValue;

        switch (sortConfig.key) {
          case 'score':
            aValue = parseInt(a.healthScore || a.HealthScore || '0', 10);
            bValue = parseInt(b.healthScore || b.HealthScore || '0', 10);
            break;
          case 'orgName':
            aValue = (a.name || a.Name || '').toLowerCase();
            bValue = (b.name || b.Name || '').toLowerCase();
            break;
          case 'lastScanned':
            aValue = new Date(a.lastScan || a.LastScan || 0);
            bValue = new Date(b.lastScan || b.LastScan || 0);
            break;
          default:
            return 0;
        }

        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    setFilteredIntegrations(filtered);
  }, [searchTerm, integrations, sortConfig]);

  // Handle sorting
  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Get sort icon for a column
  const getSortIcon = (key) => {
    if (sortConfig.key !== key) {
      return null;
    }
    return sortConfig.direction === 'asc' ? 
      <img src={arrowUpIcon} alt="Sort ascending" className="sort-icon" /> :
      <img src={arrowDownIcon} alt="Sort descending" className="sort-icon" />;
  };

  // Function to format date in relative time format
  const formatRelativeDate = (dateString) => {
    if (!dateString) return 'Not scanned yet';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid date';

      // For dates less than 7 days ago, use relative time
      const now = new Date();
      const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

      if (diffInDays < 7) {
        // Format without "about" prefix
        const formatted = formatDistanceToNow(date, { addSuffix: true });
        return formatted.replace('about ', '');
      } else {
        // For older dates, use the date format
        return date.toLocaleDateString('en-US', {
          day: 'numeric',
          month: 'short',
          year: 'numeric',
          hour: 'numeric',
          minute: 'numeric'
        });
      }
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  return (
    <div className="all-scans-section">
      <div className="all-scans-header">
        <div className="all-scans-title">
          <h3>All Scans ({filteredIntegrations.length.toString().padStart(2, '0')})</h3>
        </div>
        <div className="all-scans-actions">
          <div className="search-input-container">
            <input
              type="text"
              className="search-input"
              placeholder="Search Projects"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <img
              src={searchIcon}
              alt="Search"
              className="search-icon"
            />
          </div>
          <button
            className="add-integration-btn"
            onClick={() => navigate('/integrations')}
          >
            <img
              src={addCircleIcon}
              alt="Add"
              className="icon"
            />
            <span>New Integration</span>
          </button>
        </div>
      </div>

      <div className="all-scans-table-container">
        {filteredIntegrations.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">🔍</div>
            <h3>{searchTerm ? 'No matching integrations found' : 'No integrations available'}</h3>
            <p>
              {searchTerm
                ? 'Try adjusting your search criteria or clear the search to see all integrations.'
                : 'Connect your first Salesforce org to see its health score and security metrics.'}
            </p>
            {!searchTerm && (
              <button
                className="btn btn-primary"
                onClick={() => navigate('/integrations')}
              >
                Connect Org Now
              </button>
            )}
            {searchTerm && (
              <button
                className="btn btn-secondary"
                onClick={() => setSearchTerm('')}
              >
                Clear Search
              </button>
            )}
          </div>
        ) : (
          <table className="integrations-table">
            <thead>
              <tr>
                <th 
                  className="sortable-header"
                  onClick={() => handleSort('score')}
                >
                  Score {getSortIcon('score')}
                </th>
                <th 
                  className="sortable-header"
                  onClick={() => handleSort('orgName')}
                >
                  Org Name {getSortIcon('orgName')}
                </th>
                <th>Environment</th>
                <th 
                  className="sortable-header"
                  onClick={() => handleSort('lastScanned')}
                >
                  Last Scanned {getSortIcon('lastScanned')}
                </th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredIntegrations.map((integration, index) => {
              // Get normalized properties to handle different response formats
              const name = integration.name || integration.Name || '';
              const lastScan = integration.lastScan || integration.LastScan || '';
              const healthScore = integration.healthScore || integration.HealthScore || '0';
              const isActive = integration.isActive || integration.IsActive || false;
              const environment = integration.environment || integration.Environment || 'Production';

              // Check if organization has been scanned
              const hasBeenScanned = lastScan && lastScan !== 'Not scanned yet';

              return (
                <tr
                  key={index}
                >
                  <td>
                    {hasBeenScanned ? (
                      <div className="table-score-container">
                        <div className="score-ring">
                          <div className="score-ring-base"></div>
                          <div
                            className="score-ring-progress"
                            style={{
                              transform: `rotate(${parseInt(healthScore, 10) <= 50 ? 0 : (parseInt(healthScore, 10) - 50) * 3.6}deg)`,
                              clip: parseInt(healthScore, 10) <= 50
                                ? 'rect(0, 16px, 32px, 0)'
                                : 'rect(0, 32px, 32px, 16px)',
                              borderColor: parseInt(healthScore, 10) >= 80 ? '#4BCA81' : parseInt(healthScore, 10) >= 60 ? '#FFB400' : '#C23934'
                            }}
                          ></div>
                          <div className="score-text">{healthScore}%</div>
                        </div>
                      </div>
                    ) : (
                      <span className="na-text">N/A</span>
                    )}
                  </td>
                  <td className="org-name">{name}</td>
                  <td>{environment}</td>
                  <td>
                    {hasBeenScanned ? formatRelativeDate(lastScan) : 'Pending first scan'}
                  </td>
                  <td>
                    {hasBeenScanned ? (
                      <span className={`status-badge ${isActive ? 'active' : 'inactive'}`}>
                        {isActive ? 'Active' : 'Inactive'}
                      </span>
                    ) : (
                      <span className="status-badge pending">Pending</span>
                    )}
                  </td>
                  <td>
                    <div className="actions-cell">
                      {hasBeenScanned ? (
                        <button
                          className="rescan-button"
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent row click
                            onRescan(integration);
                          }}
                        >
                          <span>Re-Scan</span>
                        </button>
                      ) : (
                        <button
                          className="scan-now-button"
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent row click
                            onRescan(integration);
                          }}
                        >
                          <span>Scan Now</span>
                        </button>
                      )}
                      <button
                        className="view-details-button"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent row click
                          // Get the integration ID
                          const integrationId = integration.id || integration.Id || integration.RowKey;
                          if (integrationId) {
                            // Navigate to the integration details page using the integration ID
                            navigate(`/integration/${integrationId}`);
                          }
                        }}
                      >
                        <span>View Details</span>
                      </button>
                    </div>
                  </td>
                </tr>
              );
              })}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};

export default AllScansTable;
