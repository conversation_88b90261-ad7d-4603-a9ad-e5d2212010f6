.security-tools-selector {
  min-height: 100vh;
  background: #F1FCF7;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', sans-serif;
}

.tools-header {
  text-align: center;
  margin-bottom: 3rem;
}

.welcome-section {
  max-width: 800px;
  margin: 0 auto;
}

.welcome-title {
  font-family: 'Poppins', sans-serif;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #020A07;
  text-align: center;
  line-height: 1.2;
}

.welcome-subtitle {
  font-family: 'Lato', sans-serif;
  font-size: 1.25rem;
  color: #020A07;
  margin-bottom: 1.5rem;
  line-height: 1.6;
  text-align: center;
}

.user-greeting {
  font-family: 'Lato', sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
  color: #020A07;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto 3rem auto;
  flex: 1;
  align-items: stretch;
}

.tool-card {
  background: #FFFFFF;
  border-radius: 0.25rem;
  padding: 2.5rem;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  opacity: 0.95;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--tool-color);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.tool-card:hover {
  transform: translateY(-2px);
  box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--tool-color);
}

.tool-card:hover::before {
  transform: scaleX(1);
}

.tool-card.loading {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.tool-header {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.tool-icon-container {
  width: 60px;
  height: 60px;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.tool-icon {
  font-size: 2rem !important;
  color: white !important;
}

.tool-info {
  flex: 1;
}

.tool-name {
  font-family: 'Poppins', sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: #020A07;
  margin-bottom: 0.5rem;
}

.tool-description {
  font-family: 'Lato', sans-serif;
  font-size: 1rem;
  color: #393E3C;
  line-height: 1.6;
  margin: 0;
}

.tool-features {
  margin-bottom: 2rem;
  flex: 1;
}

.features-title {
  font-family: 'Inter', sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
  color: #020A07;
  margin-bottom: 1rem;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.feature-item {
  font-family: 'Lato', sans-serif;
  font-size: 0.9rem;
  color: #393E3C;
  padding: 0.5rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.feature-item::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--tool-color);
  font-weight: bold;
  font-size: 1rem;
}

.tool-action {
  display: flex;
  justify-content: center;
  margin-top: auto;
}

.tool-button {
  background: #51D59C;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.25rem;
  font-family: 'Inter', sans-serif;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 200px;
  justify-content: center;
}

.tool-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.tool-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-icon {
  font-size: 1.2rem !important;
}

.internal-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #51D59C;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.tools-footer {
  text-align: center;
  margin-top: auto;
}

.help-section {
  background: #FFFFFF;
  padding: 3rem;
  border-radius: 0.25rem;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
  opacity: 0.95;
}

.help-section h4 {
  font-family: 'Poppins', sans-serif;
  font-size: 1.25rem;
  font-weight: 600;
  color: #020A07;
  margin-bottom: 0.5rem;
}

.help-section p {
  font-family: 'Lato', sans-serif;
  color: #393E3C;
  margin: 0;
}

.support-link {
  color: #51D59C;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.support-link:hover {
  color: #020A07;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .security-tools-selector {
    padding: 1rem;
  }

  .welcome-title {
    font-size: 2rem;
  }

  .welcome-subtitle {
    font-size: 1.1rem;
  }

  .tools-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .tool-card {
    padding: 1.5rem;
  }

  .tool-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .tool-icon-container {
    align-self: center;
  }

  .features-list {
    grid-template-columns: 1fr;
  }

  .tool-button {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .welcome-title {
    font-size: 1.75rem;
  }

  .tool-card {
    padding: 1rem;
  }

  .tool-name {
    font-size: 1.25rem;
  }

  .help-section {
    padding: 1.5rem;
  }
}

/* Loading Animation */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.tool-card.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Focus states for accessibility */
.tool-card:focus-within {
  outline: 2px solid var(--tool-color);
  outline-offset: 2px;
}

.tool-button:focus {
  outline: 2px solid white;
  outline-offset: 2px;
} 