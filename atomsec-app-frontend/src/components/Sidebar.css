.sidebar {
  width: 240px;
  height: calc(100vh - 60px);
  background-color: #FFFFFF;
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 60px;
  z-index: 1000;
  transition: width 0.3s ease;
}

.sidebar.collapsed {
  width: 64px;
}

/* Sidebar Container */
.sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 24px;
}

/* Navigation Menu */
.navigation-menu {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0px 8px 32px;
  text-decoration: none;
  color: #393E3C;
  border-radius: 4px;
  transition: all 0.2s ease;
  position: relative;
}

.nav-item:hover {
  background-color: rgba(81, 213, 156, 0.1);
}

.nav-item.active {
  background-color: #F1FCF7;
  border-right: 2px solid #51D59C;
  color: #020A07;
}

.nav-icon-container {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #51D59C;
}

.nav-text {
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.6em;
  color: inherit;
  flex: 1;
}

.nav-item .arrow-icon {
  width: 24px;
  height: 24px;
  color: #51D59C;
}

/* Sidebar Toggle Button */
.sidebar-toggle {
  position: absolute;
  bottom: 24px;
  right: 24px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  color: #6B7280;
  transition: color 0.2s;
}

.sidebar-toggle:hover {
  color: #374151;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
}

/* Collapsed State */
.sidebar.collapsed .nav-text,
.sidebar.collapsed .arrow-icon {
  display: none;
}

.sidebar.collapsed .nav-item {
  padding: 8px;
  justify-content: center;
}

.sidebar.collapsed .nav-icon-container {
  margin: 0;
}

.sidebar.collapsed .sidebar-container {
  padding: 16px 8px;
}

