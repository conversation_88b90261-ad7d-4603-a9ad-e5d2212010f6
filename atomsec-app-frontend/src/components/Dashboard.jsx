import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { formatDistanceToNow } from 'date-fns';
import AuthPopup from './AuthPopup';
import {
  fetchIntegrations,
  fetchHealthScore,
  rescanOrg
} from '../api';
import DataCard from './DataCard';
import SeverityChart from './SeverityChart';
import AllScansTable from './AllScansTable';
import DashboardSkeleton from './DashboardSkeleton';
import './Dashboard.css';

// Import SVG assets
import '../assets/dashboard/navigate_next.svg';
import '../assets/dashboard/arrow_upward.svg';
import '../assets/dashboard/arrow_downward.svg';
import '../assets/dashboard/refresh.svg';
import '../assets/dashboard/shield-check.svg';
import moreVertSvg from '../assets/dashboard/more_vert.svg';

// Helper function to get initials from a name
const getInitials = (name) => {
  if (!name) return 'U';

  const parts = name.split(' ');
  if (parts.length === 1) return parts[0].charAt(0).toUpperCase();

  return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
};

// Helper function to generate a random color based on a string
const getRandomColor = (str) => {
  if (!str) return '#1890ff';

  // Simple hash function to generate a consistent color for the same string
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }

  // Convert hash to RGB values
  const r = (hash & 0xFF) % 200;
  const g = ((hash >> 8) & 0xFF) % 200;
  const b = ((hash >> 16) & 0xFF) % 200;

  return `rgb(${r}, ${g}, ${b})`;
};

const Dashboard = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalIssues: 0,
    criticalIssues: 0,
    totalScannedOrgs: 0,
    inactiveOrgs: 0
  });

  const [issueStats, setIssueStats] = useState({
    high: 0,
    medium: 0,
    low: 0
  });

  const [userOrgs, setUserOrgs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [quickViewOrg, setQuickViewOrg] = useState(null);
  const [showQuickView, setShowQuickView] = useState(false);
  const [showAuthPopup, setShowAuthPopup] = useState(false);
  const [currentOrg, setCurrentOrg] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(new Date());

  // Track which components are refreshing
  const [refreshing, setRefreshing] = useState({
    totalIssues: false,
    criticalIssues: false,
    totalScannedOrgs: false,
    inactiveOrgs: false,
    issueDistribution: false
  });

  // Check if any component is currently refreshing
  const isAnyRefreshing = Object.values(refreshing).some(Boolean);

  // Global refresh function
  const handleGlobalRefresh = async () => {
    try {
      setRefreshing(prev => ({
        totalIssues: true,
        criticalIssues: true,
        totalScannedOrgs: true,
        inactiveOrgs: true,
        issueDistribution: true
      }));

      // Refresh all data
      await Promise.all([
        handleRefreshTotalIssues(),
        handleRefreshCriticalIssues(),
        handleRefreshTotalScannedOrgs(),
        handleRefreshInactiveOrgs(),
        handleRefreshIssueDistribution()
      ]);

      setLastUpdated(new Date());
      toast.success('Dashboard refreshed successfully');
    } catch (error) {
      console.error('Error during global refresh:', error);
      toast.error('Some data failed to refresh');
    } finally {
      setRefreshing(prev => ({
        totalIssues: false,
        criticalIssues: false,
        totalScannedOrgs: false,
        inactiveOrgs: false,
        issueDistribution: false
      }));
    }
  };

  const fetchUserOrgs = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch integrations from the server using our API module
      const response = await fetchIntegrations({ include_inactive: false });
      console.log('Fetched integrations:', response);
      console.log('Response type:', typeof response);
      console.log('Response keys:', Object.keys(response || {}));

      // Extract the integrations array from the response
      let integrationsArray = [];
      if (response && response.data && response.data.integrations && Array.isArray(response.data.integrations)) {
        console.log('Found integrations in response.data.integrations:', response.data.integrations);
        integrationsArray = response.data.integrations;
      } else if (response && response.integrations && Array.isArray(response.integrations)) {
        console.log('Found integrations in response.integrations:', response.integrations);
        integrationsArray = response.integrations;
      } else if (Array.isArray(response)) {
        console.log('Response is directly an array:', response);
        integrationsArray = response;
      } else {
        console.warn('Unexpected integrations response format:', response);
        console.warn('Response structure:', JSON.stringify(response, null, 2));
      }

      console.log('Final integrationsArray:', integrationsArray);

      // Filter to only include active integrations
      const activeIntegrations = integrationsArray.filter(integration => {
        console.log('Checking integration:', integration);
        console.log('isActive:', integration.isActive);
        console.log('IsActive:', integration.IsActive);
        console.log('is_active:', integration.is_active);
        return integration.isActive || integration.IsActive || integration.is_active;
      });

      console.log('Active integrations after filtering:', activeIntegrations);

      setUserOrgs(activeIntegrations);

      // Set stats based on actual data or 0 if no data
      setStats({
        totalIssues: 0, // Will be populated from DB later
        criticalIssues: 0, // Will be populated from DB later
        totalScannedOrgs: activeIntegrations.length || 0,
        inactiveOrgs: 0 // Will be populated from DB later
      });

      // Set issue stats based on actual data or 0 if no data
      setIssueStats({
        high: 0, // Will be populated from DB later
        medium: 0, // Will be populated from DB later
        low: 0 // Will be populated from DB later
      });

      setLoading(false);
    } catch (err) {
      console.error('Error fetching integrations:', err);
      setError('Failed to load connected integrations');
      setLoading(false);

      // Fallback to empty data if API fails
      setStats({
        totalIssues: 0,
        criticalIssues: 0,
        totalScannedOrgs: 0,
        inactiveOrgs: 0
      });
    }
  };



  const handleCloseQuickView = () => {
    setShowQuickView(false);
    setQuickViewOrg(null);
  };

  const handleRescan = async (integration) => {
    try {
      setLoading(true);

      // Show a loading toast
      const loadingToast = toast.loading('Scanning integration...');

      try {
        // Get the integration ID
        const integrationId = integration.id || integration.Id;

        if (!integrationId) {
          toast.update(loadingToast, {
            render: 'Error: Integration ID not found',
            type: 'error',
            isLoading: false,
            autoClose: 5000
          });
          setLoading(false);
          return;
        }

        // Update toast to show progress
        toast.update(loadingToast, {
          render: 'Scanning integration... This may take up to 2 minutes.',
          type: 'info',
          isLoading: true,
          autoClose: false
        });

        // Call the rescanOrg API which now uses scanIntegration internally
        const response = await rescanOrg(integrationId);
        console.log('Scan integration response:', response);

        // Check if we got a successful response
        if (response && response.success) {
          // Check if we have a health score in the response
          if (response.healthScore !== undefined) {
            toast.update(loadingToast, {
              render: `Integration scanned successfully. Health Score: ${response.healthScore}%`,
              type: 'success',
              isLoading: false,
              autoClose: 3000
            });
          } else {
            toast.update(loadingToast, {
              render: 'Integration scanned successfully!',
              type: 'success',
              isLoading: false,
              autoClose: 3000
            });
          }

          // Refresh the list to get updated data
          fetchUserOrgs();
        } else if (response && response.isTimeout) {
          // Handle timeout specifically
          toast.update(loadingToast, {
            render: 'Scan is taking longer than expected. The scan is still running in the background. Please check back in a few minutes.',
            type: 'warning',
            isLoading: false,
            autoClose: 8000
          });

          // Still refresh the list in case some data was updated
          setTimeout(() => fetchUserOrgs(), 30000); // Refresh after 30 seconds
        } else {
          // If the scan failed, show an error message
          const errorMsg = response?.error || response?.message || 'Failed to scan integration';
          toast.update(loadingToast, {
            render: `Scan failed: ${errorMsg}`,
            type: 'error',
            isLoading: false,
            autoClose: 5000
          });
        }
      } catch (error) {
        // For all errors, show error toast
        toast.update(loadingToast, {
          render: 'Error scanning integration: ' + (error.response?.data?.error || error.message),
          type: 'error',
          isLoading: false,
          autoClose: 5000
        });

        // Log the error for debugging
        console.error('Error in handleRescan:', error);
      }

      setLoading(false);
    } catch (err) {
      console.error('Error scanning integration:', err);
      toast.error('Failed to scan integration: ' + (err.response?.data?.error || err.message));
      setLoading(false);
    }
  };



  // Function to format date in relative time format
  const formatRelativeDate = (dateString) => {
    if (!dateString) return 'Not scanned yet';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid date';

      // For dates less than 7 days ago, use relative time
      const now = new Date();
      const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

      if (diffInDays < 7) {
        // Format without "about" prefix
        const formatted = formatDistanceToNow(date, { addSuffix: true });
        return formatted.replace('about ', '');
      } else {
        // For older dates, use the date format
        return date.toLocaleDateString();
      }
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // Function to handle successful re-authentication
  const handleAuthSuccess = () => {
    // If we have a current org, retry the rescan
    if (currentOrg) {
      handleRescan(currentOrg);
      setCurrentOrg(null);
    }
  };



  // Refresh handlers for data cards (now called by global refresh)
  const handleRefreshTotalIssues = async () => {
    // Use mock data for demonstration
    return new Promise((resolve) => {
      setTimeout(() => {
        setStats(prev => ({ ...prev, totalIssues: prev.totalIssues + 120 }));
        resolve();
      }, 1500);
    });
  };

  const handleRefreshCriticalIssues = async () => {
    // Use mock data for demonstration
    return new Promise((resolve) => {
      setTimeout(() => {
        setStats(prev => ({ ...prev, criticalIssues: prev.criticalIssues - 200 }));
        resolve();
      }, 1500);
    });
  };

  const handleRefreshTotalScannedOrgs = async () => {
    // Refresh the list of organizations
    try {
      await fetchUserOrgs();
    } catch (err) {
      console.error('Error refreshing orgs:', err);
      throw err;
    }
  };

  const handleRefreshInactiveOrgs = async () => {
    // Refresh the list of organizations including inactive ones
    try {
      const response = await fetchIntegrations({ include_inactive: true });
      // Extract the integrations array from the response
      let integrationsArray = [];
      if (response && response.integrations && Array.isArray(response.integrations)) {
        // If response has integrations property and it's an array
        integrationsArray = response.integrations;
      } else if (Array.isArray(response)) {
        // If response is directly an array
        integrationsArray = response;
      } else {
        console.warn('Unexpected integrations response format:', response);
      }

      const inactiveIntegrations = integrationsArray.filter(integration =>
        !(integration.isActive || integration.IsActive)
      );
      setStats(prev => ({ ...prev, inactiveOrgs: inactiveIntegrations.length }));
    } catch (err) {
      console.error('Error refreshing inactive orgs:', err);
      throw err;
    }
  };

  const handleRefreshIssueDistribution = async () => {
    // Simulate API call with a timeout
    return new Promise((resolve) => {
      setTimeout(() => {
        try {
          // Generate new random data with realistic distributions
          // Total should be around 100-200 issues
          const total = Math.floor(Math.random() * 100) + 100;

          // Generate random percentages that add up to 100%
          const highPercent = Math.random() * 0.3 + 0.1; // 10-40%
          const mediumPercent = Math.random() * 0.4 + 0.3; // 30-70%
          const lowPercent = 1 - highPercent - mediumPercent; // Remainder

          // Calculate actual counts
          const newData = {
            high: Math.round(total * highPercent),
            medium: Math.round(total * mediumPercent),
            low: Math.round(total * lowPercent)
          };

          // Ensure the total matches by adjusting the low value if needed
          const currentTotal = newData.high + newData.medium + newData.low;
          if (currentTotal !== total) {
            newData.low += (total - currentTotal);
          }

          // Update the state with new data
          setIssueStats(newData);

          // Update total issues count
          setStats(prev => ({
            ...prev,
            totalIssues: newData.high + newData.medium + newData.low,
            criticalIssues: newData.high
          }));

          resolve();
        } catch (error) {
          // Handle error
          console.error('Error refreshing issue distribution:', error);
          throw error;
        }
      }, 1500);
    });
  };



  useEffect(() => {
    fetchUserOrgs();
  }, []);

  if (loading) return <DashboardSkeleton />;
  if (error) return <div className="error-alert">{error}</div>;

  return (
    <div className="dashboard">
      <div className="page-header">
        <div className="page-header-main">
          <h1 className="page-title">Dashboard</h1>
          <div className="global-refresh-control">
            <span className="last-updated">
              Last updated: {formatDistanceToNow(lastUpdated, { addSuffix: true })}
            </span>
            <button
              className="global-refresh-btn"
              onClick={handleGlobalRefresh}
              disabled={isAnyRefreshing}
              aria-label="Refresh all dashboard data"
            >
              <img
                src="/assets/dashboard/refresh.svg"
                alt="Refresh"
                className={`icon ${isAnyRefreshing ? 'rotating' : ''}`}
              />
            </button>
          </div>
        </div>
        <p className="page-description">Overview of your Salesforce orgs and security metrics</p>
      </div>

      <div className="stats-container">
        <DataCard
          title="All Issues"
          value={stats.totalIssues.toLocaleString()}
          trend={2}
          trendUp={true}
          icon="issues"
          onNavigate={() => navigate('/issues')}
        />

        <DataCard
          title="Critical Issues"
          value={stats.criticalIssues.toLocaleString()}
          trend={24}
          trendUp={false}
          icon="critical"
          onNavigate={() => navigate('/issues?severity=high')}
        />

        <DataCard
          title="Total Scanned Orgs"
          value={stats.totalScannedOrgs.toLocaleString()}
          icon="orgs"
          onNavigate={() => navigate('/integrations')}
        />

        <DataCard
          title="Inactive Orgs"
          value={stats.inactiveOrgs.toLocaleString()}
          icon="inactive"
          onNavigate={() => navigate('/integrations?status=inactive')}
        />
      </div>

      <div className="dashboard-content">
        <div className="dashboard-left-column">
          <SeverityChart
            data={issueStats}
            onRefresh={handleRefreshIssueDistribution}
            isRefreshing={refreshing.issueDistribution}
          />
        </div>

        <div className="dashboard-right-column">
          <div className="severity-chart-card">
            <div className="severity-chart-header">
              <h3 className="severity-chart-title">Issues Trend Over Time</h3>
              <div className="severity-chart-actions">
                <button
                  className="severity-chart-menu-btn"
                  aria-label="More options"
                >
                  <img src={moreVertSvg} alt="More options" />
                </button>
              </div>
            </div>

            <div className="severity-chart-tabs">
              <button
                className="severity-chart-tab active"
              >
                Last 7 Days
              </button>
              <button
                className="severity-chart-tab"
              >
                Last 30 Days
              </button>
              <button
                className="severity-chart-tab"
              >
                All Time
              </button>
            </div>

            <div className="severity-chart-content">
              <div className="severity-chart-visualization">
                {/* Placeholder for trend chart - would be implemented with a proper chart library */}
                <div className="trend-chart-placeholder">
                  <div className="trend-chart-empty-icon">
                    <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M3 13H7L9 7L15 17L17 13H21" stroke="#E0E0E0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div className="trend-chart-empty-title">Trend Analysis</div>
                  <div className="trend-chart-empty-description">
                    This chart will track your open security issues over time as you complete more scans. It's a great way to visualize your progress.
                  </div>
                  {/* Visual placeholder: faint gray dashed line */}
                  <svg className="trend-chart-placeholder-line" width="100%" height="100%" viewBox="0 0 300 150" preserveAspectRatio="none">
                    <path
                      d="M20 120 Q75 80 150 100 T280 60"
                      stroke="#E0E0E0"
                      strokeWidth="2"
                      strokeDasharray="5,5"
                      fill="none"
                      opacity="0.3"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* All Scans Section */}
      <AllScansTable
        integrations={userOrgs}
        onRescan={handleRescan}
      />

      {/* Quick View Popup */}
      {showQuickView && quickViewOrg && (
        <div className="quick-view-overlay" onClick={handleCloseQuickView}>
          <div className="quick-view-popup" onClick={(e) => e.stopPropagation()}>
            <div className="quick-view-header">
              <h3>{quickViewOrg.name || quickViewOrg.Name}</h3>
              <button className="close-btn" onClick={handleCloseQuickView}>×</button>
            </div>
            <div className="quick-view-content">
              <div className="detail-item">
                <span className="detail-label">Tenant URL:</span>
                <span className="detail-value">{quickViewOrg.tenantUrl || quickViewOrg.TenantUrl || quickViewOrg.instanceUrl}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Environment:</span>
                <span className="detail-value">{quickViewOrg.environment || quickViewOrg.Environment || 'production'}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Health Score:</span>
                {(() => {
                  const healthScore = quickViewOrg.healthScore || quickViewOrg.HealthScore || '0';
                  const scoreInt = parseInt(healthScore, 10);
                  return (
                    <span className={`health-badge ${scoreInt >= 80 ? 'high' : scoreInt >= 60 ? 'medium' : 'low'}`}>
                      {healthScore}%
                    </span>
                  );
                })()}
              </div>
              <div className="detail-item">
                <span className="detail-label">Last Scan:</span>
                <span className="detail-value">
                  {formatRelativeDate(quickViewOrg.lastScan || quickViewOrg.LastScan || new Date())}
                </span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Status:</span>
                <span className={`status-badge ${(quickViewOrg.isActive || quickViewOrg.IsActive) ? 'active' : 'inactive'}`}>
                  {(quickViewOrg.isActive || quickViewOrg.IsActive) ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
            <div className="quick-view-footer">
              <button
                className="btn btn-secondary"
                onClick={() => {
                  // Get the integration ID
                  const integrationId = quickViewOrg.id || quickViewOrg.Id;
                  if (integrationId) {
                    handleRescan(quickViewOrg);
                    handleCloseQuickView();
                  } else {
                    toast.error('Error: Integration ID not found');
                  }
                }}
              >
                Scan Integration
              </button>
              <button
                className="btn btn-primary"
                onClick={() => {
                  handleCloseQuickView();
                  const integrationId = quickViewOrg.id || quickViewOrg.Id || quickViewOrg.integrationId || quickViewOrg.IntegrationId;
                  if (integrationId) {
                    navigate(`/integration/${integrationId}`);
                  } else {
                    toast.error('Error: Integration ID not found');
                  }
                }}
              >
                View All Details
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Authentication Popup */}
      <AuthPopup
        isOpen={showAuthPopup && !!currentOrg}
        onClose={() => setShowAuthPopup(false)}
        integrationId={currentOrg?.id || currentOrg?.Id || currentOrg?.integrationId || currentOrg?.IntegrationId || ''}
        orgName={currentOrg?.name || currentOrg?.Name || ''}
        onSuccess={handleAuthSuccess}
      />
    </div>
  );
};

export default Dashboard;
