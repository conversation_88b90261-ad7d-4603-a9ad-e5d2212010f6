/* App Skeleton Container */
.app-skeleton {
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* Header Skeleton */
.skeleton-header {
  height: 60px;
  background-color: #FFFFFF;
  border-bottom: 1px solid #DEE2E6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.skeleton-logo {
  width: 120px;
  height: 32px;
}

.skeleton-search {
  width: 300px;
  height: 36px;
  border-radius: 18px;
}

.skeleton-user {
  width: 120px;
  height: 36px;
  border-radius: 18px;
}

/* Sidebar Skeleton */
.skeleton-sidebar {
  width: 250px;
  height: calc(100vh - 60px);
  background-color: #FFFFFF;
  border-right: 1px solid #DEE2E6;
  position: fixed;
  left: 0;
  top: 60px;
  display: flex;
  flex-direction: column;
  z-index: 999;
}

.skeleton-nav {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.skeleton-nav-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.skeleton-nav-text {
  width: 120px;
  height: 16px;
}

.skeleton-footer {
  padding: 16px;
  border-top: 1px solid #DEE2E6;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skeleton-help {
  height: 60px;
  border-radius: 4px;
}

.skeleton-collapse {
  width: 24px;
  height: 24px;
  align-self: flex-end;
}

/* Main Content Skeleton */
.skeleton-main {
  margin-left: 250px;
  margin-top: 60px;
  flex: 1;
  padding: 32px;
}

.skeleton-content {
  max-width: 1200px;
  margin: 0 auto;
}

.skeleton-page-header {
  margin-bottom: 24px;
}

.skeleton-title {
  width: 200px;
  height: 32px;
  margin-bottom: 12px;
}

.skeleton-subtitle {
  width: 350px;
  height: 20px;
}

.skeleton-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.skeleton-card {
  height: 120px;
  border-radius: 4px;
}

/* Shimmer Animation */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .skeleton-sidebar {
    width: 200px;
  }
  
  .skeleton-main {
    margin-left: 200px;
  }
  
  .skeleton-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .skeleton-sidebar {
    width: 64px;
  }
  
  .skeleton-main {
    margin-left: 64px;
    padding: 16px;
  }
  
  .skeleton-nav-text {
    display: none;
  }
  
  .skeleton-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .skeleton-header {
    padding: 0 16px;
  }
  
  .skeleton-search {
    width: 200px;
  }
  
  .skeleton-user {
    width: 80px;
  }
}

@media (max-width: 480px) {
  .skeleton-sidebar {
    display: none;
  }
  
  .skeleton-main {
    margin-left: 0;
    padding: 12px;
  }
  
  .skeleton-header {
    padding: 0 12px;
  }
  
  .skeleton-search {
    width: 150px;
  }
  
  .skeleton-user {
    width: 60px;
  }
}
