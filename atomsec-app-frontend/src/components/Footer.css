.internal-footer {
  padding: 48px 32px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  width: 100%;
  margin-left: 250px;
  position: relative;
  z-index: 10;
  overflow: hidden; /* Prevent horizontal overflow */
}

.sidebar-is-collapsed + .internal-footer {
  margin-left: 64px;
}

@media (max-width: 768px) {
  .internal-footer,
  .sidebar-is-collapsed + .internal-footer {
    margin-left: 0;
  }
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  height: 79px;
  max-width: 100%; /* Ensure content doesn't exceed container width */
  overflow: hidden; /* Prevent content overflow */
  gap: 16px; /* Add gap between left and right sections */
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0; /* Prevent left side from shrinking */
}

.footer-right {
  flex: 1; /* Take remaining space */
  min-width: 0; /* Allow text to wrap */
  overflow: hidden; /* Hide overflow */
  text-overflow: ellipsis; /* Show ellipsis for long text */
  white-space: nowrap; /* Prevent text wrapping */
  max-width: 300px; /* Limit maximum width to prevent overflow */
}

.footer-logo,
.footer-copyright {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.6;
  color: #393E3C;
}

.footer-divider {
  width: 1px;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.12);
}

.footer-links {
  font-family: 'Open Sans', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.3;
  color: #393E3C;
  display: block; /* Ensure proper block display */
  overflow: hidden; /* Hide overflow */
  text-overflow: ellipsis; /* Show ellipsis */
  white-space: nowrap; /* Keep text on one line */
}

/* Responsive Design */
@media (max-width: 1200px) {
  .footer-right {
    max-width: 250px; /* Reduce max width on medium screens */
  }
}

@media (max-width: 768px) {
  .internal-footer,
  .sidebar-is-collapsed + .internal-footer {
    margin-left: 0;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 12px;
    height: auto;
    padding: 20px 0;
  }
  
  .footer-left,
  .footer-right {
    justify-content: center;
    text-align: center;
  }
  
  .footer-right {
    max-width: 100%; /* Allow full width on mobile */
    white-space: normal; /* Allow text to wrap on mobile */
  }
  
  .footer-links {
    white-space: normal; /* Allow text to wrap on mobile */
    text-align: center;
  }
}

@media (max-width: 480px) {
  .footer-content {
    padding: 16px 0;
    gap: 8px;
  }
  
  .footer-left {
    gap: 12px;
  }
  
  .footer-logo,
  .footer-copyright,
  .footer-links {
    font-size: 14px; /* Reduce font size on very small screens */
  }
}