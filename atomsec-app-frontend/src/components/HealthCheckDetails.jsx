import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Pie, Bar } from 'react-chartjs-2';
import { API_CONFIG } from '../config';
import {
  fetchOrgDetails,
  fetchHealthScore as apiFetchHealthScore,
  fetchHealthRisks as apiFetchHealthRisks,
  fetchProfiles as apiFetchProfiles,
  fetchPermissionSets as apiFetchPermissionSets,
  fetchPoliciesResultByIntegrationId
} from '../api';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
} from 'chart.js';
import AuthPopup from './AuthPopup';
import ProfilesPermissionSets from './ProfilesPermissionSets';
import DataStatusMessage from './DataStatusMessage';
import './HealthCheckDetails.css';
import arrowDownIcon from '../assets/icons/arrow_drop_down.svg';
import restartIcon from '../assets/icons/restart.svg';

// Register ChartJS components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
);

const HealthCheckDetails = () => {
  const { instanceUrl } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [orgData, setOrgData] = useState(null);
  const [healthRisks, setHealthRisks] = useState([]);
  const [healthScore, setHealthScore] = useState(null);
  const [profiles, setProfiles] = useState([]);
  const [permissionSets, setPermissionSets] = useState([]);
  const [isRescanning, setIsRescanning] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [filterType, setFilterType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showAuthPopup, setShowAuthPopup] = useState(false);
  const [authError, setAuthError] = useState(false);

  // Data status states
  const [orgDataStatus, setOrgDataStatus] = useState('loading');
  const [healthScoreStatus, setHealthScoreStatus] = useState('loading');
  const [healthRisksStatus, setHealthRisksStatus] = useState('loading');
  const [profilesStatus, setProfilesStatus] = useState('loading');
  const [permissionSetsStatus, setPermissionSetsStatus] = useState('loading');
  const [isRefreshing, setIsRefreshing] = useState({
    orgData: false,
    healthScore: false,
    healthRisks: false,
    profiles: false,
    permissionSets: false
  });

  const [policiesResult, setPoliciesResult] = useState([]);
  const [policiesResultStatus, setPoliciesResultStatus] = useState('loading');

  useEffect(() => {
    fetchOrgData();
    if (activeTab === 'healthCheck') {
      fetchHealthRisks();
    } else if (activeTab === 'profilesPermissions') {
      fetchProfiles();
    }
  }, [activeTab]);

  // Fetch health score when component mounts
  useEffect(() => {
    fetchHealthScore();
  }, []);

  useEffect(() => {
    console.log('DEBUG useEffect orgData:', orgData, 'activeTab:', activeTab);
    if (orgData && activeTab === 'healthCheck') {
      fetchPoliciesResult();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orgData, activeTab]);

  useEffect(() => {
    if (orgData) {
      console.log('DEBUG orgData:', orgData);
    }
  }, [orgData]);

  const fetchOrgData = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setOrgDataStatus('loading');
      setAuthError(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, orgData: true }));
      }

      // Include the email in the request if available
      const response = await fetchOrgDetails(instanceUrl, forceRefresh);

      // Check data status
      if (response.dataStatus === 'pending') {
        setOrgDataStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchOrgData(), 5000);
        setLoading(false);
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        // Handle both new and old response formats
        setOrgData(response);
        console.log('DEBUG setOrgData', response);
        setOrgDataStatus('available');
        setLoading(false);
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, orgData: false }));
      }
    } catch (err) {
      console.error('Error fetching org data:', err);
      setOrgDataStatus('error');

      // Check if it's an authentication error
      if (err.response && err.response.status === 401) {
        setAuthError(true);
        setError('Authentication required. Please authenticate with Salesforce.');
      } else {
        setError('Failed to fetch organization details');
      }

      setLoading(false);

      if (err.response && err.response.status === 401) {
        toast.error('Authentication required. Please click the Authenticate button below.');
      } else {
        toast.error('Failed to fetch organization details: ' + (err.response?.data?.error || err.message));
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, orgData: false }));
      }
    }
  };

  const fetchHealthScore = async (forceRefresh = false) => {
    try {
      setHealthScoreStatus('loading');
      setAuthError(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthScore: true }));
      }

      const response = await apiFetchHealthScore(instanceUrl, forceRefresh);

      // Check data status
      if (response.dataStatus === 'pending') {
        setHealthScoreStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchHealthScore(), 5000);
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        // Handle both new and old response formats
        if (response.score !== undefined && response.score !== null) {
          setHealthScore(response.score);
        } else {
          console.log('Health score is null or undefined, setting to N/A');
          setHealthScore('N/A');
        }
        setHealthScoreStatus('available');
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthScore: false }));
      }
    } catch (err) {
      console.error('Error fetching health score:', err);
      setHealthScore('N/A');
      setHealthScoreStatus('error');

      // Don't show authentication errors anymore
      if (err.response && err.response.status !== 401) {
        toast.error('Failed to fetch health score: ' + (err.response?.data?.error || err.message));
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthScore: false }));
      }
    }
  };

  const fetchHealthRisks = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setHealthRisksStatus('loading');
      setAuthError(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthRisks: true }));
      }

      const response = await apiFetchHealthRisks(instanceUrl, forceRefresh);
      console.log('Health check API response:', response);

      // Check data status
      if (response.dataStatus === 'pending') {
        setHealthRisksStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchHealthRisks(), 5000);
        setLoading(false);
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        // Handle both new and old response formats
        if (Array.isArray(response.risks)) {
          setHealthRisks(response.risks);
        } else if (Array.isArray(response.data?.risks)) {
          setHealthRisks(response.data.risks);
        } else if (Array.isArray(response)) {
          setHealthRisks(response);
        } else {
          // If data is not an array, set empty array
          console.error('Health risks data is not an array or not found in response:', response);
          setHealthRisks([]);
        }

        setHealthRisksStatus('available');
        setLoading(false);

        // Also fetch the health score
        await fetchHealthScore();
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthRisks: false }));
      }
    } catch (err) {
      console.error('Error fetching health risks:', err);
      setHealthRisks([]);
      setHealthRisksStatus('error');
      setLoading(false);

      // Don't show authentication errors anymore
      if (err.response && err.response.status !== 401) {
        setError('Failed to fetch health risk data');
        toast.error('Failed to fetch health risk data: ' + (err.response?.data?.error || err.message));
      }

      // Still fetch the health score even if health risks fail
      try {
        await fetchHealthScore();
      } catch (scoreErr) {
        console.error('Error fetching health score after health risks failure:', scoreErr);
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthRisks: false }));
      }
    }
  };

  const fetchProfiles = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setProfilesStatus('loading');
      setAuthError(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: true }));
      }

      const response = await apiFetchProfiles(instanceUrl, forceRefresh);

      // Check data status
      if (response.dataStatus === 'pending') {
        setProfilesStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchProfiles(), 5000);
        setLoading(false);
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        // Handle both new and old response formats
        if (Array.isArray(response.profiles)) {
          setProfiles(response.profiles);
        } else if (Array.isArray(response)) {
          setProfiles(response);
        } else {
          // If data is not an array, set empty array
          console.log('Profile data is not in expected format, setting to empty array');
          setProfiles([]);
        }

        setProfilesStatus('available');
        setLoading(false);

        // Also fetch permission sets
        await fetchPermissionSets(forceRefresh);
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: false }));
      }
    } catch (err) {
      console.error('Error fetching profiles:', err);
      setProfiles([]);
      setProfilesStatus('error');
      setLoading(false);

      // Check if it's an authentication error
      if (err.response && err.response.status === 401) {
        setAuthError(true);
        setError('Authentication required. Please authenticate with Salesforce.');
      } else {
        setError('Failed to fetch profile data');
      }

      if (err.response && err.response.status === 401) {
        toast.error('Authentication required. Please click the Authenticate button below.');
      } else {
        toast.error('Failed to fetch profile data: ' + (err.response?.data?.error || err.message));
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: false }));
      }
    }
  };

  const fetchPermissionSets = async (forceRefresh = false) => {
    try {
      setPermissionSetsStatus('loading');
      setAuthError(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, permissionSets: true }));
      }

      const response = await apiFetchPermissionSets(instanceUrl, forceRefresh);

      // Check data status
      if (response.dataStatus === 'pending') {
        setPermissionSetsStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchPermissionSets(), 5000);
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        // Handle both new and old response formats
        if (Array.isArray(response.permissionSets)) {
          setPermissionSets(response.permissionSets);
        } else if (Array.isArray(response)) {
          setPermissionSets(response);
        } else {
          // If data is not an array, set empty array
          console.log('Permission set data is not in expected format, setting to empty array');
          setPermissionSets([]);
        }

        setPermissionSetsStatus('available');
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, permissionSets: false }));
      }
    } catch (err) {
      console.error('Error fetching permission sets:', err);
      setPermissionSets([]);
      setPermissionSetsStatus('error');

      // Check if it's an authentication error
      if (err.response && err.response.status === 401) {
        setAuthError(true);
        setError('Authentication required. Please authenticate with Salesforce.');
      } else {
        setError('Failed to fetch permission set data');
      }

      if (err.response && err.response.status === 401) {
        toast.error('Authentication required. Please click the Authenticate button below.');
      } else {
        toast.error('Failed to fetch permission set data: ' + (err.response?.data?.error || err.message));
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, permissionSets: false }));
      }
    }
  };

  const fetchPoliciesResult = async () => {
    try {
      setPoliciesResultStatus('loading');
      let integrationId = orgData?.id || orgData?.RowKey;
      if (!integrationId) {
        setPoliciesResultStatus('error');
        setPoliciesResult([]);
        return;
      }

      // 1. Fetch latest completed health_check task for this integration
              const taskStatusResp = await fetch(`/api/db/task-status?integration_id=${integrationId}&status=completed&task_type=health_check&limit=1`);
      const taskStatusData = await taskStatusResp.json();
      const latestHealthCheckTask = taskStatusData?.data?.[0];

      if (!latestHealthCheckTask || !latestHealthCheckTask.ExecutionLogId) {
        setPoliciesResultStatus('empty');
        setPoliciesResult([]);
        return;
      }

      // 2. Fetch ONLY health check policies result for the latest ExecutionLogId
      console.log('HealthCheckDetails - Calling fetchPoliciesResultByIntegrationId with:', {
        integrationId,
        executionLogId: latestHealthCheckTask.ExecutionLogId,
        type: 'HealthCheck'
      });
      const result = await fetchPoliciesResultByIntegrationId(integrationId, latestHealthCheckTask.ExecutionLogId, 'HealthCheck');

      if (Array.isArray(result)) {
        // Filter to only show HealthCheck records
        const healthCheckRecords = result.filter(record => record.Type === 'HealthCheck');
        setPoliciesResult(healthCheckRecords);
        setPoliciesResultStatus('available');
      } else if (result && result.data) {
        // Filter to only show HealthCheck records
        const healthCheckRecords = result.data.filter(record => record.Type === 'HealthCheck');
        setPoliciesResult(healthCheckRecords);
        setPoliciesResultStatus('available');
      } else if (result && result.dataStatus === 'pending') {
        setPoliciesResultStatus('pending');
        setTimeout(fetchPoliciesResult, 5000);
      } else {
        setPoliciesResult([]);
        setPoliciesResultStatus('empty');
      }
    } catch (err) {
      console.error('Error fetching health check policies result:', err);
      setPoliciesResultStatus('error');
      setPoliciesResult([]);
    }
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  const handleFilterChange = (e) => {
    setFilterType(e.target.value);
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleAuthenticate = () => {
    setShowAuthPopup(true);
  };

  const handleAuthSuccess = () => {
    // Refresh the data after successful authentication
    fetchOrgData();
    if (activeTab === 'healthCheck') {
      fetchHealthRisks();
    }
  };

  const handleRescan = async () => {
    try {
      setIsRescanning(true);
      toast.info('Rescanning Salesforce org...');

      // Fetch fresh data with force refresh
      if (activeTab === 'healthCheck') {
        await fetchHealthRisks(true);
      } else if (activeTab === 'profilesPermissions') {
        await fetchProfiles(true);
      } else {
        // Always fetch the latest org data
        await fetchOrgData(true);
      }

      toast.success('Rescan completed successfully!');
      setIsRescanning(false);
    } catch (error) {
      console.error('Rescan error:', error);
      toast.error('Failed to rescan: ' + (error.response?.data?.error || error.message));
      setIsRescanning(false);
    }
  };

  const filteredPoliciesResult = policiesResult.filter(record => {
    const severity = record.Severity || record.severity || '';
    const setting = record.Setting || record.setting || '';
    const settingGroup = record.SettingGroup || record.settingGroup || '';
    
    const matchesFilter = filterType === 'all' || severity.toUpperCase() === filterType;
    const matchesSearch = searchTerm === '' ||
      setting.toLowerCase().includes(searchTerm.toLowerCase()) ||
      settingGroup.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  // Get health score grade
  const getHealthScoreGrade = (score) => {
    if (!score) return { grade: 'N/A', color: '#6c757d' };

    const numScore = parseInt(score, 10);

    if (numScore >= 90) return { grade: 'Excellent', color: '#28a745' };
    if (numScore >= 80) return { grade: 'Very Good', color: '#5cb85c' };
    if (numScore >= 70) return { grade: 'Good', color: '#5bc0de' };
    if (numScore >= 55) return { grade: 'Poor', color: '#f0ad4e' };
    return { grade: 'Very Poor', color: '#d9534f' };
  };

  // Prepare data for pie chart
  const prepareRiskDistributionData = () => {
    if (!policiesResult || policiesResult.length === 0) {
      // Return default data if no health check data
      return {
        labels: ['No Data'],
        datasets: [
          {
            data: [1],
            backgroundColor: ['#6c757d'],
            borderWidth: 1
          }
        ]
      };
    }

    const riskCounts = policiesResult.reduce((acc, record) => {
      const severity = record.Severity || record.severity || 'UNKNOWN';
      acc[severity] = (acc[severity] || 0) + 1;
      return acc;
    }, {});

    return {
      labels: Object.keys(riskCounts),
      datasets: [
        {
          data: Object.values(riskCounts),
          backgroundColor: [
            '#dc3545', // HIGH - Red
            '#ffc107', // MEDIUM - Yellow
            '#28a745', // LOW - Green
            '#6c757d'  // UNKNOWN - Gray
          ],
          borderWidth: 1
        }
      ]
    };
  };

  // Prepare data for bar chart
  const prepareSettingGroupData = () => {
    if (!policiesResult || policiesResult.length === 0) {
      // Return default data if no health check data
      return {
        labels: ['No Data'],
        datasets: [
          {
            label: 'No Data',
            data: [0],
            backgroundColor: '#6c757d',
          }
        ]
      };
    }

    // Group by SettingGroup (if available) or use a default group
    const groupCounts = policiesResult.reduce((acc, record) => {
      const settingGroup = record.SettingGroup || record.settingGroup || 'Unknown Group';
      const severity = record.Severity || record.severity || 'UNKNOWN';
      
      if (!acc[settingGroup]) {
        acc[settingGroup] = {
          HIGH: 0,
          MEDIUM: 0,
          LOW: 0,
          UNKNOWN: 0
        };
      }
      acc[settingGroup][severity.toUpperCase()] = (acc[settingGroup][severity.toUpperCase()] || 0) + 1;
      return acc;
    }, {});

    return {
      labels: Object.keys(groupCounts),
      datasets: [
        {
          label: 'High Risk',
          data: Object.values(groupCounts).map(group => group.HIGH || 0),
          backgroundColor: '#dc3545',
        },
        {
          label: 'Medium Risk',
          data: Object.values(groupCounts).map(group => group.MEDIUM || 0),
          backgroundColor: '#ffc107',
        },
        {
          label: 'Low Risk',
          data: Object.values(groupCounts).map(group => group.LOW || 0),
          backgroundColor: '#28a745',
        }
      ]
    };
  };

  const renderOverviewTab = () => {
    return (
      <div className="coming-soon">
        <h3>Overview - analysis coming soon</h3>
      </div>
    );
  };

  const renderHealthCheckTab = () => {
    // Loading state
    if (healthRisksStatus === 'loading') {
      return (
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Loading health check data...</p>
        </div>
      );
    }

    // Error state
    if (healthRisksStatus === 'error') {
      return (
        <div className="error-state">
          <div className="error-icon">⚠️</div>
          <p>{error || 'Failed to load health check data'}</p>
          <button className="refresh-button" onClick={() => fetchHealthRisks(true)}>
            Try Again
          </button>
        </div>
      );
    }

    // Pending state
    if (healthRisksStatus === 'pending') {
      return (
        <DataStatusMessage
          status="pending"
          message="Health check data is being fetched in the background. This may take a few moments."
          onRefresh={() => fetchHealthRisks()}
        />
      );
    }

    // Empty state
    if (!healthRisks || healthRisks.length === 0) {
      return (
        <div className="empty-state">
          <div className="empty-icon">🔍</div>
          <h3>No Health Check Data Available</h3>
          <p>No data was found in the database. Click the <strong>Rescan</strong> button at the top of the page to fetch security health check data from Salesforce.</p>
          <button className="refresh-button" onClick={() => fetchHealthRisks(true)}>
            Rescan
          </button>
        </div>
      );
    }

    // Available state: show health score, last updated, charts, and tables
    const healthScoreGrade = getHealthScoreGrade(healthScore);
    const lastUpdated = orgData && orgData.lastScan ? new Date(orgData.lastScan).toLocaleString() : 'Unknown';

    // Prepare chart data
    const pieData = prepareRiskDistributionData();
    const barData = prepareSettingGroupData();

    // Chart options
    const pieOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              return `${context.label}: ${context.parsed}`;
            }
          }
        }
      }
    };

    const barOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              return `${context.dataset.label}: ${context.parsed.y}`;
            }
          }
        }
      },
      scales: {
        x: {
          stacked: true,
          grid: {
            display: false
          }
        },
        y: {
          stacked: true,
          beginAtZero: true,
          grid: {
            color: '#f0f0f0'
          }
        }
      }
    };

    return (
      <div className="health-check-content">
        {/* Filter Section */}
        <div className="filter-section">
          <div className="filter-header">
            <h2 className="filter-title">Filter By:</h2>
            <div className="filter-controls">
              <div className="filter-input-container">
                <select
                  className="filter-select"
                  value={filterType}
                  onChange={handleFilterChange}
                >
                  <option value="all">Setting Group</option>
                  <option value="HIGH">High Risk</option>
                  <option value="MEDIUM">Medium Risk</option>
                  <option value="LOW">Low Risk</option>
                </select>
                <img src={arrowDownIcon} alt="Expand" className="filter-arrow" />
              </div>
              
              <div className="filter-input-container">
                <select
                  className="filter-select"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                >
                  <option value="">Risk Type</option>
                  <option value="HIGH_RISK">High Risk</option>
                  <option value="MEDIUM_RISK">Medium Risk</option>
                  <option value="LOW_RISK">Low Risk</option>
                </select>
                <img src={arrowDownIcon} alt="Expand" className="filter-arrow" />
              </div>
              
              <button 
                className="filter-reset-btn" 
                onClick={() => { setFilterType('all'); setSearchTerm(''); }}
              >
                <img src={restartIcon} alt="Reset" className="reset-icon" />
                Reset
              </button>
            </div>
          </div>
        </div>

        {/* Charts Section */}
        <div className="charts-section">
          <div className="chart-card">
            <h3 className="chart-title">Distribution of Risk Types</h3>
            <div className="chart-content">
              <div className="pie-chart-container">
                <Pie data={pieData} options={pieOptions} />
              </div>
              <div className="chart-legend">
                <div className="legend-item">
                  <span className="legend-color high-risk"></span>
                  <span className="legend-text">High Risk</span>
                </div>
                <div className="legend-item">
                  <span className="legend-color medium-risk"></span>
                  <span className="legend-text">Medium Risk</span>
                </div>
                <div className="legend-item">
                  <span className="legend-color meets-standard"></span>
                  <span className="legend-text">Meets Standard</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="chart-card">
            <h3 className="chart-title">Misaligned Settings</h3>
            <div className="chart-content">
              <div className="bar-chart-container">
                <Bar data={barData} options={barOptions} />
              </div>
              <div className="chart-legend">
                <div className="legend-item">
                  <span className="legend-color high-risk"></span>
                  <span className="legend-text">High Risk</span>
                </div>
                <div className="legend-item">
                  <span className="legend-color medium-risk"></span>
                  <span className="legend-text">Medium Risk</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Details Section */}
        <div className="details-section">
          <div className="details-header">
            <h4 className="details-title">Details</h4>
            <p className="details-description">
              Measures how closely your org's security settings align with Salesforce best practices.
            </p>
          </div>
          
          <div className="details-table">
            <table>
              <thead>
                <tr>
                  <th>Risk Type</th>
                  <th>Setting</th>
                  <th>Setting Group</th>
                  <th>Org Value</th>
                  <th>Standard Value</th>
                </tr>
              </thead>
              <tbody>
                {filteredPoliciesResult.map((row, idx) => {
                  const severity = row.Severity || row.severity || '';
                  const severityClass = severity.toLowerCase().includes('high') ? 'high-severity' :
                                       severity.toLowerCase().includes('medium') ? 'medium-severity' :
                                       'low-severity';
                  
                  return (
                    <tr key={idx}>
                      <td>
                        <span className={`severity-badge ${severityClass}`}>
                          {severity}
                        </span>
                      </td>
                      <td>{row.Setting || row.setting}</td>
                      <td>{row.SettingGroup || row.settingGroup || 'N/A'}</td>
                      <td>{row.OrgValue || row.orgValue}</td>
                      <td>{row.StandardValue || row.standardValue}</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  };

  console.log('DEBUG render HealthCheckDetails', { orgData, activeTab });

  return (
    <div className="health-check-details">
      {/* Authentication Popup */}
      <AuthPopup
        isOpen={showAuthPopup}
        env={orgData?.environment || 'production'}
        instanceUrl={instanceUrl}
        onClose={() => setShowAuthPopup(false)}
        onSuccess={handleAuthSuccess}
      />

      {/* Header Card */}
      <div className="header-card">
        <div className="header-content">
          <div className="header-text">
            {orgData && <h2 className="org-name">{orgData.name}</h2>}
            {orgData && <h3 className="last-synced">Last Synced on: {new Date(orgData.lastScan || orgData.LastScan || Date.now()).toLocaleString()}</h3>}
          </div>
          <div className="header-actions">
            <button className="rescan-button" onClick={handleRescan} disabled={isRescanning}>
              <span className="rescan-icon">{isRescanning ? '⏳' : '🔄'}</span>
              {isRescanning ? 'Rescanning...' : 'Re-Scan'}
            </button>
            <button className="more-options-button">
              <span className="more-options-icon">⋮</span>
            </button>
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="tabs-container">
        <div className="tabs">
          <button
            className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => handleTabChange('overview')}
          >
            Overview
          </button>
          <button
            className={`tab-button ${activeTab === 'healthCheck' ? 'active' : ''}`}
            onClick={() => handleTabChange('healthCheck')}
          >
            Health Check
          </button>
          <button
            className={`tab-button ${activeTab === 'profilesPermissions' ? 'active' : ''}`}
            onClick={() => handleTabChange('profilesPermissions')}
          >
            Profiles and Permission Sets
          </button>
          <button
            className={`tab-button ${activeTab === 'guestUserProfile' ? 'active' : ''}`}
            onClick={() => handleTabChange('guestUserProfile')}
          >
            Guest User Profile Risks
          </button>
        </div>
      </div>

      <div className="tab-content">
        {authError ? (
          <div className="auth-error-container">
            <div className="auth-error-message">
              <h3>Authentication Required</h3>
              <p>You need to authenticate with Salesforce to view this data.</p>
              <button className="btn btn-primary" onClick={handleAuthenticate}>
                Authenticate with Salesforce
              </button>
            </div>
          </div>
        ) : (
          <>
            {activeTab === 'overview' && renderOverviewTab()}
            {activeTab === 'healthCheck' && renderHealthCheckTab()}
            {activeTab === 'profilesPermissions' && <div className="coming-soon"><h3>Profiles and Permission Sets - analysis coming soon</h3></div>}
            {activeTab === 'guestUserProfile' && <div className="coming-soon"><h3>Guest User Profile Risks - analysis coming soon</h3></div>}
          </>
        )}
      </div>
    </div>
  );
};

export default HealthCheckDetails;
