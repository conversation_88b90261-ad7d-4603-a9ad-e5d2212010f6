import React, { useState, useEffect } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  Home,
  Inventory2,
  Settings,
  ArrowDropDown,
  Menu as MenuIcon,
  Security
} from '@mui/icons-material';
import './Sidebar.css';

const Sidebar = () => {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('tools');

  // Initialize active tab from localStorage or default to 'tools'
  useEffect(() => {
    const savedActiveTab = localStorage.getItem('activeTab');
    if (savedActiveTab) {
      setActiveTab(savedActiveTab);
    }
  }, []);

  // Update active tab when location changes
  useEffect(() => {
    const path = location.pathname.split('/')[1] || 'tools';
    setActiveTab(path);
    localStorage.setItem('activeTab', path);
  }, [location]);

  const toggleSidebar = () => {
    const newCollapsedState = !collapsed;
    setCollapsed(newCollapsedState);

    // Add or remove the sidebar-is-collapsed class from the app-container
    const appContainer = document.querySelector('.app-container');
    if (appContainer) {
      if (newCollapsedState) {
        appContainer.classList.add('sidebar-is-collapsed');
      } else {
        appContainer.classList.remove('sidebar-is-collapsed');
      }
    }
  };

  // Set initial collapsed state class on app-container
  useEffect(() => {
    const appContainer = document.querySelector('.app-container');
    if (appContainer && collapsed) {
      appContainer.classList.add('sidebar-is-collapsed');
    }

    // Cleanup function
    return () => {
      if (appContainer) {
        appContainer.classList.remove('sidebar-is-collapsed');
      }
    };
  }, [collapsed]);

  // Navigation items based on actual routes in the application
  const navItems = [
    {
      path: '/tools',
      name: 'Security Tools',
      icon: <Security />,
      hasDropdown: false,
      id: 'tools'
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      icon: <Home />,
      hasDropdown: false,
      id: 'dashboard'
    },
    {
      path: '/integrations',
      name: 'Integrations',
      icon: <Inventory2 />,
      hasDropdown: true,
      id: 'integrations'
    },
    {
      path: '/settings',
      name: 'Settings',
      icon: <Settings />,
      hasDropdown: true,
      id: 'settings'
    }
  ];

  return (
    <div className={`sidebar ${collapsed ? 'collapsed' : ''}`}>
      <div className="sidebar-container">
        {/* Navigation Menu */}
        <div className="navigation-menu">
          {navItems.map((item) => (
            <NavLink
              key={item.id}
              to={item.path}
              className={({ isActive }) => 
                `nav-item ${isActive ? 'active' : ''} ${activeTab === item.id ? 'active' : ''}`
              }
              onClick={() => setActiveTab(item.id)}
            >
              <div className="nav-icon-container">
                {item.icon}
              </div>
              <span className="nav-text">{item.name}</span>
              {item.hasDropdown && <ArrowDropDown className="arrow-icon" />}
            </NavLink>
          ))}
        </div>

        {/* Toggle Button */}
        <button className="sidebar-toggle" onClick={toggleSidebar}>
          <MenuIcon />
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
