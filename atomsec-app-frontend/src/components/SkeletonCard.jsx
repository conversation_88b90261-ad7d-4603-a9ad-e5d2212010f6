import React from 'react';
import './SkeletonCard.css';

/**
 * SkeletonCard component for displaying skeleton loading states
 * Mimics the structure of DataCard with animated shimmer effects
 */
const SkeletonCard = ({ type = 'metric' }) => {
  if (type === 'metric') {
    return (
      <div className="skeleton-card skeleton-metric">
        <div className="skeleton-card-header">
          <div className="skeleton-title-container">
            <div className="skeleton-icon shimmer"></div>
            <div className="skeleton-title shimmer"></div>
          </div>
          <div className="skeleton-navigate shimmer"></div>
        </div>
        <div className="skeleton-card-content">
          <div className="skeleton-value-container">
            <div className="skeleton-value shimmer"></div>
            <div className="skeleton-trend shimmer"></div>
          </div>
          <div className="skeleton-actions">
            <div className="skeleton-refresh shimmer"></div>
          </div>
        </div>
      </div>
    );
  }

  if (type === 'chart') {
    return (
      <div className="skeleton-card skeleton-chart">
        <div className="skeleton-chart-header">
          <div className="skeleton-chart-title shimmer"></div>
          <div className="skeleton-chart-actions">
            <div className="skeleton-action-btn shimmer"></div>
            <div className="skeleton-action-btn shimmer"></div>
          </div>
        </div>
        <div className="skeleton-chart-tabs">
          <div className="skeleton-tab shimmer"></div>
          <div className="skeleton-tab shimmer"></div>
          <div className="skeleton-tab shimmer"></div>
        </div>
        <div className="skeleton-chart-content">
          <div className="skeleton-chart-visualization shimmer"></div>
        </div>
      </div>
    );
  }

  if (type === 'table') {
    return (
      <div className="skeleton-card skeleton-table">
        <div className="skeleton-table-header">
          <div className="skeleton-table-title shimmer"></div>
          <div className="skeleton-table-actions">
            <div className="skeleton-action-btn shimmer"></div>
          </div>
        </div>
        <div className="skeleton-table-content">
          {[...Array(5)].map((_, index) => (
            <div key={index} className="skeleton-table-row">
              <div className="skeleton-table-cell shimmer"></div>
              <div className="skeleton-table-cell shimmer"></div>
              <div className="skeleton-table-cell shimmer"></div>
              <div className="skeleton-table-cell shimmer"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return null;
};

export default SkeletonCard;
