import React, { useState, useEffect, useRef } from 'react';
import './settings-risk-table.css';
import ResetIcon from '../assets/icons/ResetIcon.svg';
import { getTaskStatus, fetchProfilesPermissions } from '../api';

const TASK_TYPES = [
  'device_activation',
  'login_ip_ranges',
  'mfa_enforcement',
  'password_policy',
  'session_timeout',
];

const FILTER_OPTIONS = [
  { label: 'Profiles/Permission Sets', value: 'profiles' },
  { label: 'Settings', value: 'settings' },
];
const RISK_TYPE_OPTIONS = [
  { label: 'All', value: 'all' },
  { label: 'High', value: 'high' },
  { label: 'Medium', value: 'medium' },
  { label: 'Low', value: 'low' },
];

export default function SettingsRiskTable({ orgId, setDetailView }) {
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tooltip, setTooltip] = useState({ show: false, text: '', x: 0, y: 0 });

  // Function to get tooltip text for each setting
  const getSettingTooltip = (settingName) => {
    const tooltips = {
      'Device Activation': 'Requires device verification for new login attempts to prevent unauthorized access.',
      'Login IP Ranges': 'Restricts login access to specific IP addresses for enhanced network security.',
      'MFA Enforcement': 'Enforces multi-factor authentication to add an extra layer of security beyond passwords.',
      'Password Policy': 'Defines password strength requirements including length, complexity, and expiration rules.',
      'Session Timeout': 'Controls session duration before requiring re-authentication to prevent idle session access.'
    };
    return tooltips[settingName] || 'Security setting configuration and compliance information.';
  };
  const scrollTopRef = useRef(null);
  const [settingNameFilter, setSettingNameFilter] = useState('All');
  const [owaspFilter, setOwaspFilter] = useState('All');
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 10;

  const handleSort = (key) => {
    setSortConfig((prev) => {
      if (prev.key === key) {
        // Toggle direction
        return { key, direction: prev.direction === 'asc' ? 'desc' : 'asc' };
      }
      return { key, direction: 'asc' };
    });
  };

  useEffect(() => {
    let isMounted = true;
    setLoading(true);
    setError(null);
    const fetchAll = async () => {
      try {
        const results = await Promise.all(
          TASK_TYPES.map(async (taskType) => {
            // 1. Fetch latest execution_log_id for this task type
            const statusJson = await getTaskStatus(orgId, 'completed', 1, taskType);
            const arr = Array.isArray(statusJson.data) ? statusJson.data : [];
            const latest = arr.length > 0 ? arr[0] : null;
            const executionLogId = latest && (latest.execution_log_id || latest.ExecutionLogId);
            if (!executionLogId) return null;
            // 2. Fetch the summary/settings data for this execution_log_id
            const dataJson = await fetchProfilesPermissions(orgId, executionLogId, taskType);

            // Special handling for MFA Enforcement
            if (taskType === 'mfa_enforcement' && Array.isArray(dataJson.policies)) {
              // 1. Org Value: custom aggregation for issues, unique per profile
              let orgValue = '';
              // Map: { profileName: {missing: bool, mismatch: bool} }
              const profileIssueMap = {};
              // Define allSettings to collect all settings for standardValue aggregation
              const allSettings = [];
              dataJson.policies.forEach(policy => {
                let profileName = policy.ProfileName || policy.profileName || policy.RowKey || '';
                // Try to extract profile name from RowKey if needed
                if (!profileName && policy.RowKey && policy.RowKey.includes('-')) {
                  profileName = policy.RowKey.split('-')[0];
                }
                try {
                  const arr = JSON.parse(policy.OrgValue);
                  allSettings.push(...arr);
                  arr.forEach(s => {
                    if (!profileIssueMap[profileName]) profileIssueMap[profileName] = { missing: false, mismatch: false };
                    if (s.Issue === 'Setting missing from profile') profileIssueMap[profileName].missing = true;
                    if (s.Issue === 'Value does not match best practice') profileIssueMap[profileName].mismatch = true;
                  });
                } catch {}
              });
              const missingCount = Object.values(profileIssueMap).filter(v => v.missing).length;
              const mismatchCount = Object.values(profileIssueMap).filter(v => v.mismatch).length;
              const orgValueParts = [];
              if (missingCount > 0) {
                orgValueParts.push(`${missingCount === 1 ? '1 profile is' : missingCount + ' profiles are'} lacking MFA`);
              }
              if (mismatchCount > 0) {
                orgValueParts.push(`${mismatchCount === 1 ? '1 profile has a discrepancy' : mismatchCount + ' profiles have discrepancies'} in MFA implementation`);
              }
              orgValue = orgValueParts.join('; ');
              // 2. OWASP Category: unique OWASP
              const uniqueOwasp = Array.from(new Set(dataJson.policies.map(p => p.OWASP).filter(Boolean)));
              const owaspCategory = uniqueOwasp.length > 0 ? uniqueOwasp.join(', ') : "A7: Identification & Authentication Failures";
              // 3. Standard Value: unique SalesforceSetting = StandardValue
              const uniqueSettings = [];
              const seen = new Set();
              allSettings.forEach(s => {
                const key = `${s.SalesforceSetting}=${s.StandardValue}`;
                if (!seen.has(key)) {
                  uniqueSettings.push(`${s.SalesforceSetting} = ${s.StandardValue}`);
                  seen.add(key);
                }
              });
              const standardValue = uniqueSettings.join(', ');
              return {
                taskType,
                executionLogId,
                settingName: 'MFA Enforcement',
                orgValue,
                standardValue,
                owaspCategory,
                status: 'Meets Standard',
                raw: dataJson,
              };
            }
            // Special handling for Device Activation
            if (taskType === 'device_activation' && Array.isArray(dataJson.policies)) {
              let orgValue = '';
              let missingCount = 0;
              let misconfiguredCount = 0;
              const allSettings = [];
              const uniqueOwasp = new Set();
              dataJson.policies.forEach(policy => {
                try {
                  const arr = JSON.parse(policy.OrgValue);
                  allSettings.push(...arr);
                  arr.forEach(s => {
                    if (s.OrgValue === null || s.OrgValue === undefined) missingCount++;
                    else if (String(s.OrgValue).toLowerCase() !== String(s.StandardValue).toLowerCase()) misconfiguredCount++;
                    if (s.OWASP) uniqueOwasp.add(s.OWASP);
                  });
                } catch {}
              });
              if (missingCount > 0) {
                orgValue += `Configuration not enforced for ${missingCount === 1 ? '1 profile' : missingCount + ' profiles'}, that might allow attackers to skip device verification during login`;
              }
              if (misconfiguredCount > 0) {
                if (orgValue) orgValue += '; ';
                orgValue += `Device Activation feature is misconfigured on ${misconfiguredCount === 1 ? '1 profile' : misconfiguredCount + ' profiles'}`;
              }
              // Standard Value: unique SalesforceSetting = StandardValue
              const uniqueSettings = [];
              const seen = new Set();
              allSettings.forEach(s => {
                const key = `${s.SalesforceSetting}=${s.StandardValue}`;
                if (!seen.has(key)) {
                  uniqueSettings.push(`${s.SalesforceSetting} = ${s.StandardValue}`);
                  seen.add(key);
                }
              });
              const standardValue = uniqueSettings.join(', ');
              const owaspCategory = Array.from(uniqueOwasp).length > 0 ? Array.from(uniqueOwasp).join(', ') : "A7: Identification & Authentication Failures";
              return {
                taskType,
                executionLogId,
                settingName: 'Device Activation',
                orgValue,
                standardValue,
                owaspCategory,
                status: 'Meets Standard',
                raw: dataJson,
              };
            }
            // Special handling for Login IP Ranges
            if (taskType === 'login_ip_ranges' && Array.isArray(dataJson.policies)) {
              let riskProfiles = 0;
              const uniqueOwasp = new Set();
              dataJson.policies.forEach(policy => {
                try {
                  const arr = JSON.parse(policy.OrgValue);
                  if (Array.isArray(arr) && arr.length > 0) {
                    // If any issue present, count this profile
                    if (arr.some(s => s.Issue)) riskProfiles++;
                  }
                  arr.forEach(s => { if (s.OWASP) uniqueOwasp.add(s.OWASP); });
                } catch {}
              });
              let orgValue = '';
              if (riskProfiles > 0) {
                orgValue = `${riskProfiles} Profiles have been implemented without proper IP restriction that exposes org to unauthorized access`;
              }
              const standardValue = "Profiles should have restricted IP whitelisting";
              const owaspCategory = Array.from(uniqueOwasp).length > 0 ? Array.from(uniqueOwasp).join(', ') : "A5: Security Misconfiguration";
              return {
                taskType,
                executionLogId,
                settingName: 'Login IP Ranges',
                orgValue,
                standardValue,
                owaspCategory,
                status: 'Meets Standard',
                raw: dataJson,
              };
            }
            // Special handling for Password Policy
            if (taskType === 'password_policy' && Array.isArray(dataJson.policies)) {
              const missingProfiles = new Set();
              const misconfiguredProfiles = new Set();
              const uniqueOwasp = new Set();
              const allSettings = [];
              dataJson.policies.forEach(policy => {
                let profileName = policy.ProfileName || policy.profileName || policy.RowKey || '';
                if (!profileName && policy.RowKey && policy.RowKey.includes('-')) {
                  profileName = policy.RowKey.split('-')[0];
                }
                try {
                  const arr = JSON.parse(policy.OrgValue);
                  allSettings.push(...arr);
                  arr.forEach(s => {
                    if (s.Issue === 'Password policy file missing for profile') missingProfiles.add(profileName);
                    if (s.Issue === 'Value does not match best practice') misconfiguredProfiles.add(profileName);
                    if (s.OWASP) uniqueOwasp.add(s.OWASP);
                  });
                } catch {}
              });
              let orgValue = '';
              if (missingProfiles.size > 0) {
                orgValue += `${missingProfiles.size} ${missingProfiles.size === 1 ? 'profile lacks' : 'profiles lack'} best practices for password policies`;
              }
              if (misconfiguredProfiles.size > 0) {
                if (orgValue) orgValue += ': ';
                orgValue += `${misconfiguredProfiles.size} ${misconfiguredProfiles.size === 1 ? 'profile has' : 'profiles have'} misconfigured password policies`;
              }
              // Standard Value: unique SalesforceSetting = StandardValue
              const uniqueSettings = [];
              const seen = new Set();
              allSettings.forEach(s => {
                const key = `${s.SalesforceSetting}=${s.StandardValue}`;
                if (!seen.has(key) && s.SalesforceSetting && s.StandardValue) {
                  uniqueSettings.push(`${s.SalesforceSetting} = ${s.StandardValue}`);
                  seen.add(key);
                }
              });
              const standardValue = uniqueSettings.join(', ');
              const owaspCategory = Array.from(uniqueOwasp).join(', ');
              return {
                taskType,
                executionLogId,
                settingName: 'Password Policy',
                orgValue,
                standardValue,
                owaspCategory,
                status: 'Meets Standard',
                raw: dataJson,
              };
            }
            
            // Special handling for Session Timeout
            if (taskType === 'session_timeout' && Array.isArray(dataJson.policies)) {
              const missingProfiles = new Set();
              const misconfiguredProfiles = new Set();
              const uniqueOwasp = new Set();
              const allSettings = [];
              let totalProfiles = 0;
              
              console.log('Session timeout data:', dataJson); // Debug log
              
              dataJson.policies.forEach(policy => {
                let profileName = policy.ProfileName || policy.profileName || policy.RowKey || '';
                if (!profileName && policy.RowKey && policy.RowKey.includes('-')) {
                  profileName = policy.RowKey.split('-')[0];
                }
                if (profileName) totalProfiles++;
                
                try {
                  const arr = JSON.parse(policy.OrgValue);
                  allSettings.push(...arr);
                  arr.forEach(s => {
                    console.log('Session timeout setting:', s); // Debug log
                    // Check for various possible issue descriptions
                    if (s.Issue && (
                      s.Issue.toLowerCase().includes('missing') || 
                      s.Issue.toLowerCase().includes('not configured') ||
                      s.Issue.toLowerCase().includes('setting missing') ||
                      s.Issue === 'Setting missing from profile' ||
                      s.Issue === 'Session timeout not configured'
                    )) {
                      missingProfiles.add(profileName);
                    }
                    if (s.Issue && (
                      s.Issue.toLowerCase().includes('does not match') ||
                      s.Issue.toLowerCase().includes('misconfigured') ||
                      s.Issue.toLowerCase().includes('discrepancy') ||
                      s.Issue.toLowerCase().includes('mismatch') ||
                      s.Issue === 'Value does not match best practice'
                    )) {
                      misconfiguredProfiles.add(profileName);
                    }
                    // Also check if OrgValue doesn't match StandardValue (common pattern)
                    if (s.OrgValue && s.StandardValue && s.OrgValue !== s.StandardValue) {
                      misconfiguredProfiles.add(profileName);
                    }
                    // Check if there's any issue at all (fallback)
                    if (s.Issue && !missingProfiles.has(profileName)) {
                      misconfiguredProfiles.add(profileName);
                    }
                    if (s.OWASP) uniqueOwasp.add(s.OWASP);
                  });
                } catch (e) {
                  console.log('Session timeout parsing error:', e); // Debug log
                  // If parsing fails, assume the profile has issues
                  if (profileName) missingProfiles.add(profileName);
                }
              });
              
              let orgValue = '';
              if (missingProfiles.size > 0) {
                orgValue += `${missingProfiles.size} ${missingProfiles.size === 1 ? 'profile lacks' : 'profiles lack'} session timeout configuration`;
              }
              if (misconfiguredProfiles.size > 0) {
                if (orgValue) orgValue += ': ';
                orgValue += `${misconfiguredProfiles.size} ${misconfiguredProfiles.size === 1 ? 'profile has' : 'profiles have'} misconfigured session timeout settings`;
              }
              
              // If no specific issues found but we have data, show a generic message
              if (!orgValue && totalProfiles > 0) {
                orgValue = `${totalProfiles} ${totalProfiles === 1 ? 'profile has' : 'profiles have'} session timeout settings that need review`;
              }
              
              // Standard Value: unique SalesforceSetting = StandardValue
              const uniqueSettings = [];
              const seen = new Set();
              allSettings.forEach(s => {
                const key = `${s.SalesforceSetting}=${s.StandardValue}`;
                if (!seen.has(key) && s.SalesforceSetting && s.StandardValue) {
                  uniqueSettings.push(`${s.SalesforceSetting} = ${s.StandardValue}`);
                  seen.add(key);
                }
              });
              const standardValue = uniqueSettings.join(', ');
              
              const owaspCategory = Array.from(uniqueOwasp).join(', ');
              console.log('Session timeout summary:', { orgValue, missingProfiles: missingProfiles.size, misconfiguredProfiles: misconfiguredProfiles.size, standardValue }); // Debug log
              
              return {
                taskType,
                executionLogId,
                settingName: 'Session Timeout',
                orgValue,
                standardValue,
                owaspCategory,
                status: 'Meets Standard',
                raw: dataJson,
              };
            }
            
            // Special handling for Login IP Ranges
            if (taskType === 'login_ip_ranges' && Array.isArray(dataJson.policies)) {
              const riskyProfiles = new Set();
              const uniqueOwasp = new Set();
              dataJson.policies.forEach(policy => {
                let profileName = policy.ProfileName || policy.profileName || policy.RowKey || '';
                if (!profileName && policy.RowKey && policy.RowKey.includes('-')) {
                  profileName = policy.RowKey.split('-')[0];
                }
                try {
                  const arr = JSON.parse(policy.OrgValue);
                  arr.forEach(s => {
                    if (s.Issue && s.Issue.toLowerCase().includes('risky')) riskyProfiles.add(profileName);
                    if (s.OWASP) uniqueOwasp.add(s.OWASP);
                  });
                } catch {}
              });
              let orgValue = '';
              if (riskyProfiles.size > 0) {
                orgValue += `${riskyProfiles.size} ${riskyProfiles.size === 1 ? 'profile has' : 'profiles have'} a risky configuration for whitelisted IP's`;
              }
              const owaspCategory = Array.from(uniqueOwasp).join(', ');
              return {
                taskType,
                executionLogId,
                settingName: 'Login IP Ranges',
                orgValue,
                standardValue: 'IP Ranges should be restricted',
                owaspCategory,
                status: 'Meets Standard',
                raw: dataJson,
              };
            }
            // Default: just return summary row per task
            return {
              taskType,
              executionLogId,
              ...dataJson.summary, // or customize as per your backend response
              raw: dataJson,
            };
          })
        );
        if (isMounted) {
          setTableData(results.filter(Boolean));
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          setError('Failed to fetch settings risk data');
          setLoading(false);
        }
      }
    };
    fetchAll();
    return () => { isMounted = false; };
  }, [orgId]);

  const safeData = Array.isArray(tableData) ? tableData : [];

  // Generate dynamic filter options
  const settingNameOptions = React.useMemo(() => {
    const options = ['All'];
    const uniqueSettings = [...new Set(safeData.map(row => row.settingName || row.taskType))];
    options.push(...uniqueSettings);
    return options;
  }, [safeData]);

  const owaspOptions = React.useMemo(() => {
    const options = ['All'];
    const uniqueOwasp = new Set();
    safeData.forEach(row => {
      if (row.owaspCategory) {
        const categories = row.owaspCategory.split(',').map(cat => cat.trim());
        categories.forEach(cat => uniqueOwasp.add(cat));
      }
    });
    options.push(...Array.from(uniqueOwasp));
    return options;
  }, [safeData]);

  // Filter data based on filters
  const filteredData = React.useMemo(() => {
    return safeData.filter(row => {
      const settingMatch = settingNameFilter === 'All' || row.settingName === settingNameFilter || row.taskType === settingNameFilter;
      const owaspMatch = owaspFilter === 'All' || (row.owaspCategory && row.owaspCategory.includes(owaspFilter));
      return settingMatch && owaspMatch;
    });
  }, [safeData, settingNameFilter, owaspFilter]);

  // Sort filteredData based on sortConfig
  const sortedFilteredData = React.useMemo(() => {
    if (!sortConfig.key) return filteredData;
    const sorted = [...filteredData].sort((a, b) => {
      const aVal = (a[sortConfig.key] || '').toString().toLowerCase();
      const bVal = (b[sortConfig.key] || '').toString().toLowerCase();
      if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
    return sorted;
  }, [filteredData, sortConfig]);

  // Calculate pagination
  const totalPages = Math.ceil(sortedFilteredData.length / rowsPerPage);
  const paginatedData = sortedFilteredData.slice(
    (currentPage - 1) * rowsPerPage,
    currentPage * rowsPerPage
  );

  // Reset handler
  const handleResetFilters = () => {
    setSettingNameFilter('All');
    setOwaspFilter('All');
    setCurrentPage(1);
  };

  if (loading) return <div style={{ padding: 32 }}>Loading...</div>;
  if (error) return <div style={{ padding: 32, color: 'red' }}>{error}</div>;

  // Main table UI only
  return (
    <>
      {/* Custom Tooltip */}
      {tooltip.show && (
        <div 
          className="custom-tooltip"
          style={{
            position: 'fixed',
            left: tooltip.x,
            top: tooltip.y,
            transform: 'translateX(-50%)',
            zIndex: 1000,
            backgroundColor: '#51D59C',
            color: 'white',
            padding: '10px 14px',
            borderRadius: '6px',
            fontSize: '13px',
            fontWeight: '500',
            maxWidth: '300px',
            minWidth: '200px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
            whiteSpace: 'normal',
            lineHeight: '1.4',
            textAlign: 'center',
            pointerEvents: 'none'
          }}
        >
          {tooltip.text}
          <div 
            style={{
              position: 'absolute',
              top: '100%',
              left: '50%',
              transform: 'translateX(-50%)',
              width: 0,
              height: 0,
              borderLeft: '6px solid transparent',
              borderRight: '6px solid transparent',
              borderTop: '6px solid #51D59C',
              pointerEvents: 'none'
            }}
          />
        </div>
      )}
      
      {/* Security Findings Title */}
      <div className="figma-security-findings-title">Security Findings</div>
      
      {/* Filter Section */}
      <div className="figma-filter-section">
        <span className="figma-filter-label">Filter By:</span>
        <div className="figma-filter-dropdown">
          <select 
            value={settingNameFilter} 
            onChange={(e) => setSettingNameFilter(e.target.value)}
            className="figma-filter-select"
          >
            {settingNameOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="m6 9 6 6 6-6"/>
          </svg>
        </div>
        <div className="figma-filter-dropdown">
          <select 
            value={owaspFilter} 
            onChange={(e) => setOwaspFilter(e.target.value)}
            className="figma-filter-select"
          >
            {owaspOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="m6 9 6 6 6-6"/>
          </svg>
        </div>
        <button className="figma-reset-button" onClick={handleResetFilters}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M1 4v6h6"/>
            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>
          </svg>
          Reset
        </button>
      </div>
      
      <div ref={scrollTopRef}></div>
      
      {/* Security Findings Table */}
      <div className="figma-security-table">
        <div className="figma-table-header" style={{ minHeight: '120px' }}>
          <div className="figma-table-header-cell figma-setting-name-col" style={{ minHeight: '120px', alignItems: 'flex-start' }}>Setting Name</div>
          <div className="figma-table-header-cell figma-org-value-col" style={{ minHeight: '120px', alignItems: 'flex-start' }}>Org Value</div>
          <div className="figma-table-header-cell figma-standard-value-col" style={{ minHeight: '120px', alignItems: 'flex-start' }}>Standard Value</div>
          <div className="figma-table-header-cell figma-owasp-category-col" style={{ minHeight: '120px', alignItems: 'flex-start' }}>OWASP Category</div>
        </div>
        
        <div className="figma-table-body">
          {paginatedData.map(row => (
            <div key={row.taskType} className="figma-table-row" style={{ minHeight: '120px' }}>
              <div className="figma-table-cell figma-setting-name-col" style={{ minHeight: '120px' }}>
                <span className="figma-setting-name">
                  {row.settingName || row.taskType}
                </span>
                <span 
                  className="figma-info-icon" 
                  onMouseEnter={(e) => {
                    const rect = e.target.getBoundingClientRect();
                    setTooltip({
                      show: true,
                      text: getSettingTooltip(row.settingName || row.taskType),
                      x: rect.left + rect.width / 2,
                      y: rect.top - 40
                    });
                  }}
                  onMouseLeave={() => {
                    setTimeout(() => setTooltip({ show: false, text: '', x: 0, y: 0 }), 100);
                  }}
                >
                  ℹ
                </span>
              </div>
              
              <div className="figma-table-cell figma-org-value-col" style={{ minHeight: '120px' }}>
                <div className="figma-org-value-content">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="figma-warning-icon">
                    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                    <line x1="12" y1="9" x2="12" y2="13"/>
                    <line x1="12" y1="17" x2="12.01" y2="17"/>
                  </svg>
                  <div className="figma-org-value-text">
                    <div className="figma-org-value-main">
                      {row.orgValue || '-'}
                    </div>
                    {row.executionLogId && setDetailView && (
                      <span
                        className="figma-view-link"
                        onClick={() => {
                          window.scrollTo({ top: 0, behavior: 'smooth' });
                          setDetailView({ taskType: row.taskType, executionLogId: row.executionLogId });
                        }}
                      >
                        view
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="figma-table-cell figma-standard-value-col" style={{ minHeight: '120px' }}>
                <div className="figma-standard-value-text">
                  {row.standardValue || '-'}
                </div>
              </div>
              
              <div className="figma-table-cell figma-owasp-category-col" style={{ minHeight: '120px' }}>
                <div className="figma-owasp-category-text">
                  {row.owaspCategory
                    ? row.owaspCategory.split(',').map((part, idx) => (
                        <div key={idx} className="figma-owasp-item">{part.trim()}</div>
                      ))
                    : '-'}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Pagination */}
      {totalPages > 1 && (
        <div className="figma-pagination">
          <button 
            onClick={() => setCurrentPage(p => Math.max(1, p - 1))} 
            disabled={currentPage === 1}
            className="figma-pagination-btn"
          >
            Previous
          </button>
          <span className="figma-pagination-info">
            Page {currentPage} of {totalPages}
          </span>
          <button 
            onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))} 
            disabled={currentPage === totalPages}
            className="figma-pagination-btn"
          >
            Next
          </button>
        </div>
      )}
    </>
  );
} 