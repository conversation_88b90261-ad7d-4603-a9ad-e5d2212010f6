import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { Pie } from 'react-chartjs-2';
import { fetchIntegrationPMDById, fetchFileContent } from '../api.js';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
} from 'chart.js';
import './CodeQualityDashboard.css';

// Register ChartJS components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
);

const CodeQualityDashboard = () => {
  const { integrationId } = useParams();
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(100); // Increased from 25 to 100
  const [totalCount, setTotalCount] = useState(0);
  
  // Filter states
  const [selectedSeverity, setSelectedSeverity] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedFileType, setSelectedFileType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Expandable state for code issues
  const [expandedFiles, setExpandedFiles] = useState(new Set());
  
  // Code editor state
  const [expandedCodeView, setExpandedCodeView] = useState(null); // { fileName, lineNumber, codeContent }
  const [codeLoading, setCodeLoading] = useState(false);

  console.log('CodeQualityDashboard render - integrationId:', integrationId, 'data:', data, 'loading:', loading, 'error:', error);

  // Fetch PMD findings data
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      console.log('Fetching PMD data for integration:', integrationId);
      
      const params = {
        limit: pageSize.toString(),
        offset: ((currentPage - 1) * pageSize).toString()
      };
      
      if (selectedSeverity !== 'all') params.severity = selectedSeverity;
      if (selectedCategory !== 'all') params.category = selectedCategory;
      if (selectedFileType !== 'all') params.fileType = selectedFileType;
      if (searchTerm) params.search = searchTerm;
      
      console.log('PMD API Parameters:', params);
      
      const response = await fetchIntegrationPMDById(integrationId, params);
      console.log('PMD API Response:', response);
      
      // Check if response has the expected structure
      if (!response) {
        console.error('PMD API returned null/undefined response');
        setError('No response from PMD API');
        return;
      }
      
      if (response.success === false) {
        console.error('PMD API returned error:', response.error);
        setError(response.error || 'PMD API returned an error');
        return;
      }
      
      // Handle different response structures
      let processedData = response;
      if (response.data) {
        processedData = response.data;
      }
      
      console.log('Processed PMD data:', processedData);
      
      setData(processedData);
      setTotalCount(processedData.totalCount || processedData.issues?.length || 0);
      setError(null);
    } catch (err) {
      console.error('Error fetching PMD findings:', err);
      setError(err.message || 'Failed to fetch PMD findings');
    } finally {
      setLoading(false);
    }
  }, [integrationId, currentPage, pageSize, selectedSeverity, selectedCategory, selectedFileType, searchTerm]);

  // Initial data fetch
  useEffect(() => {
    if (integrationId) {
      fetchData();
    }
  }, [integrationId, currentPage, pageSize, selectedSeverity, selectedCategory, selectedFileType, searchTerm, fetchData]);

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / pageSize);
  const startItem = (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(currentPage * pageSize, totalCount);

  // Process data for charts
  const chartData = useMemo(() => {
    if (!data?.issues) return null;

    const issues = data.issues;
    
    // Debug: Log the first few issues to see the actual structure
    console.log('First 3 issues from API:', issues.slice(0, 3));
    
    // Issues by Category (using ruleSet field from backend)
    const categoryCounts = issues.reduce((acc, issue) => {
      const category = issue.ruleSet || issue.pmd_rule_set || issue.issue_category || 'Code Quality';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});

    // Issues by Severity
    const severityCounts = issues.reduce((acc, issue) => {
      const severity = issue.severity || 'Unknown';
      acc[severity] = (acc[severity] || 0) + 1;
      return acc;
    }, {});

    // Top Rules
    const ruleCounts = issues.reduce((acc, issue) => {
      const rule = issue.rule || issue.rule_name || 'Unknown';
      acc[rule] = (acc[rule] || 0) + 1;
      return acc;
    }, {});

    console.log('Chart data processed:', { categoryCounts, severityCounts, ruleCounts });

    return {
      categoryCounts,
      severityCounts,
      ruleCounts
    };
  }, [data]);

  // Helper function to toggle file expansion
  const toggleFileExpansion = (fileName) => {
    const newExpandedFiles = new Set(expandedFiles);
    if (newExpandedFiles.has(fileName)) {
      newExpandedFiles.delete(fileName);
    } else {
      newExpandedFiles.add(fileName);
    }
    setExpandedFiles(newExpandedFiles);
  };

  // Function to fetch code content for a specific file and line
  const fetchCodeContent = async (fileName, lineNumber) => {
    try {
      setCodeLoading(true);
      
      // Fetch real file content from the backend
      const response = await fetchFileContent(integrationId, fileName, lineNumber);
      
      setExpandedCodeView({
        fileName,
        lineNumber,
        codeContent: response.lines
      });
    } catch (error) {
      console.error('Error fetching code content:', error);
      
      // Fallback to mock content if API fails
      const mockCodeContent = generateMockCodeContent(fileName, lineNumber);
      setExpandedCodeView({
        fileName,
        lineNumber,
        codeContent: mockCodeContent
      });
    } finally {
      setCodeLoading(false);
    }
  };

  // Function to toggle code view
  const toggleCodeView = (fileName, lineNumber) => {
    if (expandedCodeView && 
        expandedCodeView.fileName === fileName && 
        expandedCodeView.lineNumber === lineNumber) {
      setExpandedCodeView(null);
    } else {
      fetchCodeContent(fileName, lineNumber);
    }
  };

  // Helper function to generate mock code content (replace with actual API call)
  const generateMockCodeContent = (fileName, lineNumber) => {
    const lineNum = parseInt(lineNumber);
    const lines = [];
    
    // Generate 10 lines before and after the issue line
    for (let i = Math.max(1, lineNum - 10); i <= lineNum + 10; i++) {
      let content = '';
      if (i === lineNum) {
        // Highlight the issue line
        content = `// ISSUE ON THIS LINE: ${fileName} line ${lineNumber}`;
      } else if (i === lineNum - 1) {
        content = `public class ${fileName.replace('.cls', '')} {`;
      } else if (i === lineNum + 1) {
        content = '    // Some method or property';
      } else {
        content = `    // Line ${i} - Sample code content`;
      }
      lines.push({ lineNumber: i, content, isIssueLine: i === lineNum });
    }
    
    return lines;
  };

  // Helper function to group issues by file
  const groupIssuesByFile = (issues) => {
    const grouped = {};
    issues.forEach(issue => {
      const fileName = issue.file || 'Unknown';
      if (!grouped[fileName]) {
        grouped[fileName] = [];
      }
      grouped[fileName].push(issue);
    });
    return grouped;
  };

  // Get grouped issues
  const groupedIssues = useMemo(() => {
    if (!data?.issues) return {};
    return groupIssuesByFile(data.issues);
  }, [data]);

  // Get file list for display (use backend data if available)
  const fileList = useMemo(() => {
    if (data?.filesSummary) {
      // Use backend-provided file summary
      return data.filesSummary;
    } else if (data?.issues) {
      // Fallback to frontend processing
      return Object.keys(groupedIssues).map(fileName => ({
        fileName,
        fileType: getFileType(fileName),
        issueCount: groupedIssues[fileName].length
      }));
    }
    return [];
  }, [data, groupedIssues]);

  if (loading && !data) {
    return (
      <div className="figma-loading-state">
        <div className="figma-spinner"></div>
        <p>Loading code quality data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="figma-error-state">
        <div className="figma-error-icon">⚠️</div>
        <h3>Error Loading Code Quality Data</h3>
        <p>{error}</p>
        <button onClick={() => fetchData()} className="figma-retry-button">
          Retry
        </button>
      </div>
    );
  }

  if (!data?.issues) {
    return (
      <div className="figma-empty-state">
        <div className="figma-empty-icon">🔍</div>
        <h3>No Code Quality Data Available</h3>
        <p>No PMD scan results were found for this integration.</p>
        <p>Integration ID: {integrationId}</p>
        <p>Data received: {JSON.stringify(data, null, 2)}</p>
        <button onClick={() => fetchData()} className="figma-retry-button">
          Retry
        </button>
      </div>
    );
  }

  const resetFilters = () => {
    setSelectedSeverity('all');
    setSelectedCategory('all');
    setSelectedFileType('all');
    setSearchTerm('');
  };

  return (
    <>
      {/* Header Section - Figma Theme */}
      <div className="figma-dashboard-header">
        <div className="figma-header-left">
          <h1 className="figma-dashboard-title">Code Quality Dashboard</h1>
          <div className="figma-scan-info">
            <span className="figma-connection-info">
              <i className="figma-icon-link"></i>
              {data.tenantUrl || 'Salesforce Org'}
            </span>
            <span className="figma-scan-status">
              <i className="figma-icon-check"></i>
              Last scan: {new Date().toLocaleDateString()}
            </span>
          </div>
        </div>
        <div className="figma-header-right">
          <button className="figma-export-button">
            <i className="figma-icon-download"></i>
            Export to CSV
          </button>
        </div>
      </div>

      {/* Filter Section - Figma Theme */}
      <div className="figma-filter-section">
        <div className="figma-filters">
          <div className="figma-filter-group">
            <label className="figma-filter-label">SEVERITY</label>
            <select 
              value={selectedSeverity} 
              onChange={(e) => setSelectedSeverity(e.target.value)}
              className="figma-filter-select"
            >
              <option value="all">All Severities</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
          </div>
          
          <div className="figma-filter-group">
            <label className="figma-filter-label">CATEGORY</label>
            <select 
              value={selectedCategory} 
              onChange={(e) => {
                console.log('Category filter changed from', selectedCategory, 'to', e.target.value);
                setSelectedCategory(e.target.value);
              }}
              className="figma-filter-select"
            >
              <option value="all">All Categories</option>
              <option value="Security">Security</option>
              <option value="Performance">Performance</option>
              <option value="Best Practices">Best Practices</option>
              <option value="Code Style">Code Style</option>
              <option value="Design">Design</option>
              <option value="Documentation">Documentation</option>
              <option value="Error Prone">Error Prone</option>
              <option value="Code Quality">Code Quality</option>
              {/* Dynamic categories from API data */}
              {data?.issues && Object.keys(
                data.issues.reduce((acc, issue) => {
                  const category = issue.ruleSet || issue.pmd_rule_set || issue.issue_category || 'Code Quality';
                  acc[category] = (acc[category] || 0) + 1;
                  return acc;
                }, {})
              ).map(category => {
                // Only add categories that aren't already in the default list
                const defaultCategories = ['Security', 'Performance', 'Best Practices', 'Code Style', 'Design', 'Documentation', 'Error Prone', 'Code Quality'];
                if (!defaultCategories.includes(category)) {
                  return <option key={category} value={category}>{category}</option>;
                }
                return null;
              })}
            </select>
          </div>
          
          <div className="figma-filter-group">
            <label className="figma-filter-label">FILE TYPE</label>
            <select 
              value={selectedFileType} 
              onChange={(e) => setSelectedFileType(e.target.value)}
              className="figma-filter-select"
            >
              <option value="all">All Types</option>
              <option value="cls">Classes</option>
              <option value="trigger">Triggers</option>
              <option value="lwc">LWC</option>
              <option value="aura">Aura</option>
            </select>
          </div>
          
          <div className="figma-filter-group figma-search-group">
            <label className="figma-filter-label">SEARCH</label>
            <input
              type="text"
              placeholder="Search files, rules, or issues..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="figma-search-input"
            />
          </div>
        </div>
        
        <div className="figma-filter-actions">
          <button 
            onClick={resetFilters} 
            className="figma-reset-button"
          >
            Reset All
          </button>
        </div>
        
        {/* Debug info - remove this after fixing */}
        <div style={{ marginTop: '16px', padding: '8px', background: '#f0f0f0', borderRadius: '4px', fontSize: '12px' }}>
          <strong>Debug Info:</strong> Data loaded: {data ? 'Yes' : 'No'}, 
          Issues count: {data?.issues?.length || 0}, 
          Selected filters: Severity={selectedSeverity}, Category={selectedCategory}, FileType={selectedFileType}
        </div>
      </div>

      {/* Main Content - Figma Theme */}
      <div className="figma-dashboard-content">
        {/* Left Column - Charts */}
        <div className="figma-content-left">
          {/* Code Issues by Category Chart */}
          <div className="figma-chart-container">
            <h3 className="figma-chart-title">Code Issues by Category</h3>
            {chartData && Object.keys(chartData.categoryCounts).length > 0 ? (
              <Pie 
                data={{
                  labels: Object.keys(chartData.categoryCounts),
                  datasets: [{
                    data: Object.values(chartData.categoryCounts),
                    backgroundColor: [
                      '#51D59C', // Atom Security Green
                      '#FF6B6B', // Atom Security Red
                      '#4ECDC4', // Atom Security Teal
                      '#45B7D1', // Atom Security Blue
                      '#96CEB4', // Atom Security Light Green
                      '#FFEAA7', // Atom Security Yellow
                      '#DDA0DD', // Atom Security Purple
                      '#98D8C8'  // Atom Security Mint
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                  }]
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                          family: 'Lato, sans-serif',
                          size: 12,
                          weight: '500'
                        },
                        generateLabels: function(chart) {
                          const data = chart.data;
                          if (data.labels.length && data.datasets.length) {
                            return data.labels.map((label, i) => {
                              const dataset = data.datasets[0];
                              const value = dataset.data[i];
                              const total = dataset.data.reduce((a, b) => a + b, 0);
                              const percentage = ((value / total) * 100).toFixed(2);
                              return {
                                text: `${label} (${percentage}%)`,
                                fillStyle: dataset.backgroundColor[i],
                                strokeStyle: dataset.backgroundColor[i],
                                lineWidth: 0,
                                pointStyle: 'circle',
                                hidden: false,
                                index: i
                              };
                            });
                          }
                          return [];
                        }
                      }
                    },
                    tooltip: {
                      callbacks: {
                        label: function(context) {
                          const label = context.label || '';
                          const value = context.parsed;
                          const total = context.dataset.data.reduce((a, b) => a + b, 0);
                          const percentage = ((value / total) * 100).toFixed(2);
                          return `${label}: ${value} (${percentage}%)`;
                        }
                      }
                    }
                  }
                }}
              />
            ) : (
              <div className="figma-empty-chart-state">
                <div className="figma-empty-chart-icon">📊</div>
                <p>No category data available</p>
                <p className="figma-empty-chart-subtitle">Try adjusting your filters or check if data is available</p>
              </div>
            )}
          </div>

          {/* API Versions Chart */}
          <div className="figma-chart-container">
            <h3 className="figma-chart-title">API Versions</h3>
            <div className="figma-api-versions-chart">
              <div className="figma-api-bar">
                <div className="figma-api-segment figma-obsolete" style={{ width: '12%' }}>
                  <span className="figma-count">88</span>
                </div>
                <div className="figma-api-segment figma-deprecated" style={{ width: '10%' }}>
                  <span className="figma-count">124</span>
                </div>
                <div className="figma-api-segment figma-current" style={{ width: '78%' }}>
                  <span className="figma-count">816</span>
                </div>
              </div>
              <div className="figma-api-legend">
                <div className="figma-legend-item">
                  <div className="figma-legend-color figma-obsolete"></div>
                  <span>Obsolete v &lt; 52</span>
                </div>
                <div className="figma-legend-item">
                  <div className="figma-legend-color figma-deprecated"></div>
                  <span>Deprecated v 52-58</span>
                </div>
                <div className="figma-legend-item">
                  <div className="figma-legend-color figma-current"></div>
                  <span>Current v &gt; 58</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Code Issues by Rule */}
        <div className="figma-content-right">
          <div className="figma-chart-container">
            <h3 className="figma-chart-title">Code Issues by Rule</h3>
            {chartData && Object.keys(chartData.ruleCounts).length > 0 ? (
              <div className="figma-rules-list">
                {Object.entries(chartData.ruleCounts)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 20) // Show more rules
                  .map(([rule, count], index) => {
                    // Atom Security color palette
                    const colors = [
                      '#51D59C', // Green
                      '#FF6B6B', // Red
                      '#4ECDC4', // Teal
                      '#45B7D1', // Blue
                      '#DDA0DD', // Purple
                      '#FFEAA7', // Yellow
                      '#96CEB4', // Light Green
                      '#98D8C8'  // Mint
                    ];
                    const color = colors[index % colors.length];
                    
                    return (
                      <div key={rule} className="figma-rule-item">
                        <span className="figma-rule-name">{rule}</span>
                        <div className="figma-rule-bar">
                          <div 
                            className="figma-rule-bar-fill" 
                            style={{ 
                              width: `${Math.min((count / Math.max(...Object.values(chartData.ruleCounts))) * 100, 100)}%`,
                              backgroundColor: color
                            }}
                          ></div>
                        </div>
                        <span className="figma-rule-count">{count}</span>
                      </div>
                    );
                  })}
              </div>
            ) : (
              <div className="figma-empty-chart-state">
                <div className="figma-empty-chart-icon">📋</div>
                <p>No rule data available</p>
                <p className="figma-empty-chart-subtitle">Try adjusting your filters or check if data is available</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Code Metrics Table - Figma Theme */}
      <div className="figma-metrics-section">
        <h3 className="figma-metrics-title">Code Metrics (Line Counts)</h3>
        <div className="figma-metrics-table-container">
          <table className="figma-metrics-table">
            <thead>
              <tr>
                <th>Type</th>
                <th>Source</th>
                <th>Comment</th>
                <th>Comments/100 Lines</th>
                <th>Avg. Source/File</th>
                <th>Files</th>
                <th>Issue Count</th>
                <th>% of Code with Issues</th>
                <th>Total Lines</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Aura</td>
                <td>961</td>
                <td>6</td>
                <td>1</td>
                <td>44</td>
                <td>98</td>
                <td>1,091</td>
                <td>42.7%</td>
                <td>1,022</td>
              </tr>
              <tr>
                <td>Classes</td>
                <td>61,172</td>
                <td>5,126</td>
                <td>8</td>
                <td>131</td>
                <td>516</td>
                <td>8,459</td>
                <td>9.2%</td>
                <td>71,930</td>
              </tr>
              <tr>
                <td>Components</td>
                <td>177</td>
                <td>8</td>
                <td>5</td>
                <td>20</td>
                <td>9</td>
                <td>-</td>
                <td>-</td>
                <td>188</td>
              </tr>
              <tr>
                <td>LWC</td>
                <td>55,294</td>
                <td>3,262</td>
                <td>6</td>
                <td>274</td>
                <td>202</td>
                <td>22,191</td>
                <td>24.6%</td>
                <td>63,681</td>
              </tr>
              <tr>
                <td>Pages</td>
                <td>11,181</td>
                <td>1,794</td>
                <td>16</td>
                <td>228</td>
                <td>49</td>
                <td>62</td>
                <td>0.4%</td>
                <td>13,548</td>
              </tr>
              <tr>
                <td>Triggers</td>
                <td>372</td>
                <td>76</td>
                <td>20</td>
                <td>12</td>
                <td>31</td>
                <td>30</td>
                <td>6.7%</td>
                <td>451</td>
              </tr>
              <tr className="figma-grand-total">
                <td><strong>Grand Total</strong></td>
                <td><strong>129,157</strong></td>
                <td><strong>10,272</strong></td>
                <td><strong>8</strong></td>
                <td><strong>165</strong></td>
                <td><strong>905</strong></td>
                <td><strong>31,833</strong></td>
                <td><strong>15.1%</strong></td>
                <td><strong>150,820</strong></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Code Issues Table - Figma Theme */}
      <div className="figma-issues-section">
        <div className="figma-issues-header">
          <h3 className="figma-issues-title">Code Issues</h3>
          <div className="figma-issues-info">
            Showing {startItem} - {endItem} of {totalCount} files ({data?.summary?.total || 0} total issues)
          </div>
        </div>
        
        <div className="figma-issues-table-container">
          {fileList.length > 0 ? (
            <table className="figma-issues-table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>File Type</th>
                  <th>Issues</th>
                </tr>
              </thead>
              <tbody>
                {fileList.map((file, index) => (
                  <React.Fragment key={file.fileName}>
                    <tr 
                      className={`figma-issue-row ${expandedFiles.has(file.fileName) ? 'figma-expanded' : ''}`}
                      onClick={() => toggleFileExpansion(file.fileName)}
                    >
                      <td className="figma-file-name">
                        <i className={`figma-icon-arrow ${expandedFiles.has(file.fileName) ? 'figma-expanded' : ''}`}></i>
                        {file.fileName}
                      </td>
                      <td className="figma-file-type">{file.fileType}</td>
                      <td className="figma-issue-count">{file.issueCount}</td>
                    </tr>
                    {expandedFiles.has(file.fileName) && (
                      <tr className="figma-issue-details">
                        <td colSpan="3">
                          <div className="figma-issue-details-content">
                            <table className="figma-issue-details-table">
                              <thead>
                                <tr>
                                  <th>Line</th>
                                  <th>Issue Category</th>
                                  <th>Rule</th>
                                  <th>Description</th>
                                  <th>Action</th>
                                </tr>
                              </thead>
                              <tbody>
                                {groupedIssues[file.fileName]?.map((issue, issueIndex) => (
                                  <React.Fragment key={`${file.fileName}-${issueIndex}`}>
                                    <tr className="figma-issue-detail-row">
                                      <td className="figma-line-number">{issue.line || 'N/A'}</td>
                                      <td className="figma-issue-category">
                                        <span className={`figma-category-badge ${(issue.ruleSet || issue.pmd_rule_set || '').toLowerCase().replace(/\s+/g, '-')}`}>
                                          {issue.ruleSet || issue.pmd_rule_set || 'Code Quality'}
                                        </span>
                                      </td>
                                      <td className="figma-rule-name">{issue.rule || issue.rule_name || 'Unknown'}</td>
                                      <td className="figma-issue-description">{issue.message || issue.description || 'No description available'}</td>
                                      <td className="figma-issue-action">
                                        <button 
                                          className="figma-view-code-button"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            toggleCodeView(file.fileName, issue.line);
                                          }}
                                        >
                                          {codeLoading && expandedCodeView?.fileName === file.fileName && expandedCodeView?.lineNumber === issue.line ? (
                                            <span className="figma-loading-spinner"></span>
                                          ) : (
                                            'View'
                                          )}
                                        </button>
                                      </td>
                                    </tr>
                                    {/* Code Editor Section */}
                                    {expandedCodeView && 
                                     expandedCodeView.fileName === file.fileName && 
                                     expandedCodeView.lineNumber === issue.line && (
                                      <tr className="figma-code-editor-row">
                                        <td colSpan="5">
                                          <div className="figma-code-editor-container">
                                            <div className="figma-code-editor-header">
                                              <span className="figma-code-file-name">{file.fileName}</span>
                                              <span className="figma-code-line-number">Line {issue.line}</span>
                                              <button 
                                                className="figma-close-code-button"
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  setExpandedCodeView(null);
                                                }}
                                              >
                                                ×
                                              </button>
                                            </div>
                                            <div className="figma-code-editor-content">
                                              {expandedCodeView.codeContent.map((line, index) => (
                                                <div 
                                                  key={index} 
                                                  className={`figma-code-line ${line.isIssueLine ? 'figma-issue-line' : ''}`}
                                                >
                                                  <span className="figma-line-number">{line.lineNumber}</span>
                                                  <span className="figma-line-content">{line.content}</span>
                                                </div>
                                              ))}
                                            </div>
                                          </div>
                                        </td>
                                      </tr>
                                    )}
                                  </React.Fragment>
                                )) || (
                                  <tr>
                                    <td colSpan="5" style={{ textAlign: 'center', color: '#6c757d', fontStyle: 'italic' }}>
                                      No detailed issues available
                                    </td>
                                  </tr>
                                )}
                              </tbody>
                            </table>
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="empty-issues-state">
              <div className="empty-issues-icon">📄</div>
              <h4>No code issues found</h4>
              <p>No files with issues match your current filters</p>
              <p className="empty-issues-subtitle">Try adjusting your filters or check if data is available</p>
            </div>
          )}
        </div>
        
        {/* Pagination */}
        {fileList.length > 0 && (
          <div className="pagination">
            <div className="pagination-controls">
              <button 
                className="pagination-button"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
              >
                Previous
              </button>
              
              <span className="pagination-info">
                Page {currentPage} of {totalPages} ({startItem} - {endItem} of {totalCount} files)
              </span>
              
              <button 
                className="pagination-button"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                Next
              </button>
            </div>
            
            <div className="page-size-selector">
              <label>Show:</label>
              <select 
                value={pageSize} 
                onChange={(e) => {
                  setPageSize(Number(e.target.value));
                  setCurrentPage(1); // Reset to first page when changing page size
                }}
                className="page-size-select"
              >
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
                <option value={200}>200</option>
                <option value={500}>500</option>
              </select>
              <span>files per page</span>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

// Helper function to determine file type
const getFileType = (fileName) => {
  if (fileName?.endsWith('.cls')) return 'Classes';
  if (fileName?.endsWith('.trigger')) return 'Triggers';
  if (fileName?.endsWith('.js') && fileName?.includes('lwc')) return 'LWC';
  if (fileName?.endsWith('.cmp')) return 'Aura';
  if (fileName?.endsWith('.page')) return 'Pages';
  return 'Other';
};

export default CodeQualityDashboard; 