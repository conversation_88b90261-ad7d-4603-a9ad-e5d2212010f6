import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { toast } from 'react-toastify';
import {
  Security,
  Search,
  BugReport,
  Launch,
  ArrowForward
} from '@mui/icons-material';
import { 
  navigateToExternalTool, 
  validateToolConfig, 
  handleExternalToolError 
} from '../utils/externalToolNavigation';
import { getAllExternalTools } from '../config/externalTools';
import ExternalToolModal from './ExternalToolModal';
import './SecurityToolsSelector.css';

const SecurityToolsSelector = () => {
  const navigate = useNavigate();
  const { user, getAccessToken } = useAuth();
  const [loading, setLoading] = useState(false);
  const [modalState, setModalState] = useState({
    isOpen: false,
    toolName: '',
    toolUrl: ''
  });

  // Security tools configuration
  const securityTools = [
    {
      id: 'attack-surface',
      name: 'Attack Surface',
      description: 'Comprehensive security assessment and vulnerability management platform',
      icon: <Security className="tool-icon" />,
      color: '#51D59C', // AtomSec green theme
      isInternal: true,
      path: '/dashboard', // Navigate to dashboard with layout
      features: ['Vulnerability Scanning', 'Risk Assessment', 'Integration Management', 'Reports & Analytics']
    },
    // External tools loaded from configuration
    ...getAllExternalTools().map(tool => ({
      id: tool.id,
      name: tool.name,
      description: tool.description,
      icon: tool.id === 'open-search' ? <Search className="tool-icon" /> : <BugReport className="tool-icon" />,
      color: '#51D59C', // Use consistent AtomSec green for all tools
      isInternal: false,
      externalUrl: tool.url,
      features: tool.features
    }))
  ];

  const handleToolSelection = async (tool) => {
    setLoading(true);
    
    try {
      if (tool.isInternal) {
        // Navigate to internal tool (Attack Surface)
        navigate(tool.path);
      } else {
        // Validate tool configuration
        if (!validateToolConfig(tool)) {
          throw new Error('Invalid tool configuration');
        }
        
        // Navigate to external tool with SSO token
        await navigateToExternalTool(tool, user, getAccessToken);
        
        // Show modal after successful navigation
        setModalState({
          isOpen: true,
          toolName: tool.name,
          toolUrl: tool.externalUrl
        });
        
        toast.success(`${tool.name} opened in a new tab!`);
      }
    } catch (error) {
      console.error('Error navigating to tool:', error);
      const errorMessage = handleExternalToolError(error, tool.name);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseModal = () => {
    setModalState({
      isOpen: false,
      toolName: '',
      toolUrl: ''
    });
  };

  const handleNavigateToDashboard = () => {
    // This will be handled by the modal component
    console.log('Navigating to dashboard from modal');
  };

  return (
    <div className="security-tools-selector">
      <div className="tools-header">
        <div className="welcome-section">
          <h1 className="welcome-title">
            Welcome to Atom Security Platform
          </h1>
          <p className="welcome-subtitle">
            Choose a security tool to get started with your security operations
          </p>
          {user && (
            <div className="user-info">
              <span className="user-greeting">Hello, {user.name || user.email}</span>
            </div>
          )}
        </div>
      </div>

      <div className="tools-grid">
        {securityTools.map((tool) => (
          <div
            key={tool.id}
            className={`tool-card ${loading ? 'loading' : ''}`}
            onClick={() => !loading && handleToolSelection(tool)}
            style={{ '--tool-color': tool.color }}
          >
            <div className="tool-header">
              <div className="tool-icon-container" style={{ backgroundColor: tool.color }}>
                {tool.icon}
              </div>
              <div className="tool-info">
                <h3 className="tool-name">{tool.name}</h3>
                <p className="tool-description">{tool.description}</p>
              </div>
            </div>

            <div className="tool-features">
              <h4 className="features-title">Key Features:</h4>
              <ul className="features-list">
                {tool.features.map((feature, index) => (
                  <li key={index} className="feature-item">
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            <div className="tool-action">
              <button 
                className="tool-button"
                disabled={loading}
              >
                {tool.isInternal ? (
                  <>
                    <span>Launch {tool.name}</span>
                    <ArrowForward className="button-icon" />
                  </>
                ) : (
                  <>
                    <span>Open {tool.name} in New Tab</span>
                    <Launch className="button-icon" />
                  </>
                )}
              </button>
            </div>

            {tool.isInternal && (
              <div className="internal-badge">
                <span>Built-in</span>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="tools-footer">
        <div className="help-section">
          <h4>Need Help?</h4>
          <p>
            Contact our support team at{' '}
            <a href="mailto:<EMAIL>" className="support-link">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>

      {/* External Tool Modal */}
      <ExternalToolModal
        isOpen={modalState.isOpen}
        toolName={modalState.toolName}
        toolUrl={modalState.toolUrl}
        onClose={handleCloseModal}
        onNavigateToDashboard={handleNavigateToDashboard}
      />
    </div>
  );
};

export default SecurityToolsSelector; 