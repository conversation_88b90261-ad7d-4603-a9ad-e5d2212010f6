// Test script to verify manifest.json accessibility and CORS headers
const https = require('https');

const testUrls = [
  'https://app-atomsec-dev01.azurewebsites.net/manifest.json',
  'https://app-atomsec-dev01.azurewebsites.net/favicon.ico',
  'https://app-atomsec-dev01.azurewebsites.net/static/css/main.css',
  'https://app-atomsec-dev01.azurewebsites.net/assets/logo.svg'
];

function testUrl(url) {
  return new Promise((resolve, reject) => {
    console.log(`\n🔍 Testing: ${url}`);
    
    const options = {
      headers: {
        'Origin': 'https://app-atomsec-dev01.azurewebsites.net',
        'User-Agent': 'Mozilla/5.0 (compatible; CORS-Test/1.0)'
      }
    };

    https.get(url, options, (res) => {
      console.log(`✅ Status: ${res.statusCode}`);
      console.log(`📋 CORS Headers:`);
      console.log(`   Access-Control-Allow-Origin: ${res.headers['access-control-allow-origin']}`);
      console.log(`   Access-Control-Allow-Methods: ${res.headers['access-control-allow-methods']}`);
      console.log(`   Content-Type: ${res.headers['content-type']}`);
      
      if (res.statusCode === 302) {
        console.log(`⚠️  Redirect Location: ${res.headers.location}`);
        if (res.headers.location && res.headers.location.includes('login.windows.net')) {
          console.log(`❌ ERROR: File is being redirected to authentication!`);
        }
      }
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log(`✅ SUCCESS: File accessible without authentication`);
          if (url.includes('manifest.json')) {
            try {
              const json = JSON.parse(data);
              console.log(`📄 Manifest parsed successfully: ${json.name || json.short_name}`);
            } catch (e) {
              console.log(`⚠️  Manifest content is not valid JSON`);
            }
          }
        } else {
          console.log(`❌ FAILED: Status ${res.statusCode}`);
        }
        resolve({ url, status: res.statusCode, headers: res.headers, data });
      });
    }).on('error', (err) => {
      console.error(`❌ Network Error for ${url}:`, err.message);
      reject(err);
    });
  });
}

async function runTests() {
  console.log('🚀 Starting CORS and Authentication Tests...');
  console.log('=====================================');
  
  const results = [];
  
  for (const url of testUrls) {
    try {
      const result = await testUrl(url);
      results.push(result);
    } catch (error) {
      results.push({ url, error: error.message });
    }
  }
  
  console.log('\n📊 Test Summary:');
  console.log('================');
  
  let successCount = 0;
  let failCount = 0;
  
  results.forEach(result => {
    if (result.error) {
      console.log(`❌ ${result.url}: Network Error`);
      failCount++;
    } else if (result.status === 200) {
      console.log(`✅ ${result.url}: Success`);
      successCount++;
    } else {
      console.log(`❌ ${result.url}: Status ${result.status}`);
      failCount++;
    }
  });
  
  console.log(`\n📈 Results: ${successCount} passed, ${failCount} failed`);
  
  if (failCount === 0) {
    console.log('🎉 All tests passed! CORS issue should be resolved.');
  } else {
    console.log('⚠️  Some tests failed. Check Azure App Service configuration.');
  }
}

runTests().catch(console.error); 