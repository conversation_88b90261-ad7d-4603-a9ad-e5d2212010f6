{"routes": [{"route": "/manifest.json", "serve": "/manifest.json", "allowedRoles": ["anonymous"], "headers": {"Content-Type": "application/json", "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "GET, OPTIONS", "Access-Control-Allow-Headers": "Content-Type, Authorization", "Access-Control-Max-Age": "86400"}}, {"route": "/favicon.ico", "serve": "/favicon.ico", "allowedRoles": ["anonymous"], "headers": {"Content-Type": "image/x-icon", "Access-Control-Allow-Origin": "*"}}, {"route": "/static/*", "serve": "/static/*", "allowedRoles": ["anonymous"], "headers": {"Access-Control-Allow-Origin": "*"}}, {"route": "/assets/*", "serve": "/assets/*", "allowedRoles": ["anonymous"], "headers": {"Access-Control-Allow-Origin": "*"}}, {"route": "/*.json", "serve": "/*.json", "allowedRoles": ["anonymous"], "headers": {"Content-Type": "application/json", "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "GET, OPTIONS", "Access-Control-Allow-Headers": "Content-Type, Authorization"}}, {"route": "/*.png", "serve": "/*.png", "headers": {"Access-Control-Allow-Origin": "*"}}, {"route": "/*.jpg", "serve": "/*.jpg", "headers": {"Access-Control-Allow-Origin": "*"}}, {"route": "/*.svg", "serve": "/*.svg", "headers": {"Access-Control-Allow-Origin": "*"}}, {"route": "/*.ico", "serve": "/*.ico", "headers": {"Access-Control-Allow-Origin": "*"}}, {"route": "/*.js", "serve": "/*.js", "headers": {"Access-Control-Allow-Origin": "*"}}, {"route": "/*.css", "serve": "/*.css", "headers": {"Access-Control-Allow-Origin": "*"}}, {"route": "/*.woff", "serve": "/*.woff", "headers": {"Access-Control-Allow-Origin": "*"}}, {"route": "/*.woff2", "serve": "/*.woff2", "headers": {"Access-Control-Allow-Origin": "*"}}, {"route": "/*.ttf", "serve": "/*.ttf", "headers": {"Access-Control-Allow-Origin": "*"}}, {"route": "/*.eot", "serve": "/*.eot", "headers": {"Access-Control-Allow-Origin": "*"}}, {"route": "/login", "serve": "/index.html", "headers": {"Access-Control-Allow-Origin": "*"}, "allowedRoles": ["anonymous", "authenticated"]}, {"route": "/*", "serve": "/index.html", "headers": {"Access-Control-Allow-Origin": "*"}, "allowedRoles": ["authenticated"]}], "navigationFallback": {"rewrite": "/index.html", "exclude": ["/manifest.json", "/favicon.ico", "/static/*", "/assets/*", "/*.json", "/*.png", "/*.jpg", "/*.svg", "/*.ico", "/*.js", "/*.css", "/*.woff", "/*.woff2", "/*.ttf", "/*.eot"]}, "platform": {"enabled": true}, "identityProviders": {"azureActiveDirectory": {"enabled": true, "registration": {"openIdIssuer": "https://login.microsoftonline.com/41b676db-bf6f-46ae-a354-a83a1362533f/v2.0", "clientId": "2d313c1a-d62d-492c-869e-cf8cb9258204", "clientSecretSettingName": "MICROSOFT_PROVIDER_AUTHENTICATION_SECRET"}, "login": {"loginParameters": ["response_type=code id_token", "response_mode=form_post"], "postLoginRedirectUrl": "/platform-auth"}}}, "login": {"tokenStore": {"enabled": true}, "preserveUrlFragmentsForLogins": true, "routes": {"loginSuccessUrl": "/platform-auth", "logoutSuccessUrl": "/login"}, "allowedExternalRedirectUrls": ["https://app-atomsec-dev01.azurewebsites.net", "https://app-atomsec-dev01.azurewebsites.net/", "https://app-atomsec-dev01.azurewebsites.net/login", "https://app-atomsec-dev01.azurewebsites.net/platform-auth", "https://app-atomsec-dev01.azurewebsites.net/dashboard", "https://app-atomsec-dev01.azurewebsites.net/integrations"]}}