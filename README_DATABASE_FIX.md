# AtomSec Database Schema Fix

This directory contains scripts to fix database schema issues in your Azure SQL Database.

## Problem

Your Azure Function App is encountering database errors because:
1. **Missing tables**: Some tables don't exist in the SQL database
2. **Missing columns**: Existing tables are missing required columns
3. **Schema mismatch**: The code expects a different table structure than what exists

## Files

- **`database_schema_fix.sql`** - Main SQL script that fixes all schema issues
- **`run_schema_fix.ps1`** - PowerShell script to help execute the SQL script
- **`README_DATABASE_FIX.md`** - This file

## Quick Fix (Recommended)

### Option 1: Azure Portal Query Editor (Easiest)

1. **Go to Azure Portal**: https://portal.azure.com
2. **Navigate to your database**: 
   - Resource Group: `atomsec-dev-data`
   - SQL Server: `sqldb-atomsec-dev`
   - Database: `sql-atomsec-dev`
3. **Open Query Editor**: Click "Query editor (preview)" in the left menu
4. **Connect**: Use your Azure AD account to connect
5. **Copy the SQL script**: Open `database_schema_fix.sql` and copy all contents
6. **Paste and run**: Paste into the query editor and click "Run"

### Option 2: PowerShell Script

1. **Open PowerShell** in this directory
2. **Run the script**: `.\run_schema_fix.ps1`
3. **Choose option 1** (Azure Portal) when prompted

### Option 3: Manual Execution

1. **Open** `database_schema_fix.sql` in any text editor
2. **Copy** the entire contents
3. **Paste** into your preferred SQL tool (Azure Portal, SSMS, etc.)
4. **Execute** the script

## What the Script Does

### 1. **Schema Validation**
- Checks which tables exist
- Identifies missing columns
- Reports schema mismatches

### 2. **Table Creation**
- Creates missing tables with proper schemas
- Uses appropriate data types and constraints
- Sets default values where needed

### 3. **Column Addition**
- Adds missing columns to existing tables
- Preserves existing data
- Sets appropriate default values

### 4. **Final Validation**
- Confirms all required tables exist
- Verifies critical columns are present
- Reports any remaining issues

## Tables That Will Be Created/Fixed

### Core Tables
- **`Account`** - Organization accounts
- **`Integrations`** - Salesforce integrations
- **`UserAccount`** - User-account relationships
- **`Credentials`** - Integration credentials

### Security Tables
- **`App_HealthCheck`** - Security health checks
- **`App_PoliciesResult`** - Policy evaluation results
- **`App_Overview`** - Security overview data
- **`App_PMDScans`** - PMD scan results
- **`App_ProfilePermissions`** - Profile permission data
- **`App_ProfileAssignmentCount`** - Profile assignment counts

### Management Tables
- **`Policy`** - Security policies
- **`Rule`** - Policy rules
- **`PolicyRuleSetting`** - Policy rule settings
- **`TaskStatus`** - Task execution status
- **`UserLogin`** - User login history
- **`users`** - User management

## Expected Output

The script will show progress like this:
```
=== CHECKING EXISTING TABLES ===
✓ Account table exists
✗ Missing column: Account.IsActive (BIT)
✗ Missing column: Account.CreatedAt (DATETIME2)
✗ Missing column: Account.CreatedBy (NVARCHAR)
✗ Integrations table does not exist

=== CREATING MISSING TABLES AND COLUMNS ===
✓ Added Account.IsActive column
✓ Added Account.CreatedAt column
✓ Added Account.CreatedBy column
✓ Created Integrations table

=== FINAL VALIDATION ===
✓ All required tables exist
✓ All critical columns exist

=== SCHEMA FIX COMPLETE ===
```

## After Running the Script

1. **Check Function App logs** for any remaining database errors
2. **Test API endpoints** to ensure they're working
3. **Verify frontend functionality** - dashboard should load without errors
4. **Monitor for any new issues** that might arise

## Troubleshooting

### Common Issues

1. **Permission Denied**
   - Ensure your Azure AD account has Contributor access to the database
   - Check if you're in the correct subscription

2. **Script Fails Partway**
   - Check the error message
   - The script is designed to be re-runnable safely
   - Missing tables/columns will be created on subsequent runs

3. **Tables Still Missing**
   - Check if the script completed successfully
   - Look for error messages in the output
   - Verify you're connected to the correct database

### Getting Help

If you encounter issues:
1. **Check the script output** for specific error messages
2. **Verify database connection** and permissions
3. **Ensure you're in the correct Azure subscription**
4. **Check function app logs** for specific error details

## Next Steps

Once the schema is fixed:
1. **Deploy the updated frontend** (with the config.js fixes)
2. **Test the complete flow** from login to dashboard
3. **Monitor for any remaining issues**
4. **Consider migrating from Azure Storage** to SQL Database for better performance

## Security Note

This script creates tables with appropriate default values and constraints. However, you may want to:
- **Review the schema** for your specific security requirements
- **Add additional indexes** for performance
- **Set up proper backup and recovery** procedures
- **Configure row-level security** if needed
