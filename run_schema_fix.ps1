# =====================================================
# AtomSec Database Schema Fix Runner
# =====================================================
# This script helps you run the database schema fix

param(
    [string]$ResourceGroup = "atomsec-dev-data",
    [string]$ServerName = "sqldb-atomsec-dev",
    [string]$DatabaseName = "sql-atomsec-dev",
    [string]$SqlScriptPath = "database_schema_fix.sql"
)

Write-Host "=== AtomSec Database Schema Fix Runner ===" -ForegroundColor Green
Write-Host ""

# Check if Azure CLI is installed
try {
    $azVersion = az version --output json | ConvertFrom-Json
    Write-Host "✓ Azure CLI found (version: $($azVersion.'azure-cli'))" -ForegroundColor Green
} catch {
    Write-Host "✗ Azure CLI not found. Please install it first." -ForegroundColor Red
    Write-Host "  Download from: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli" -ForegroundColor Yellow
    exit 1
}

# Check if user is logged in
try {
    $account = az account show --output json | ConvertFrom-Json
    Write-Host "✓ Logged in as: $($account.user.name)" -ForegroundColor Green
} catch {
    Write-Host "✗ Not logged in to Azure. Please run 'az login' first." -ForegroundColor Red
    exit 1
}

# Check if SQL script exists
if (-not (Test-Path $SqlScriptPath)) {
    Write-Host "✗ SQL script not found: $SqlScriptPath" -ForegroundColor Red
    exit 1
}

Write-Host "✓ SQL script found: $SqlScriptPath" -ForegroundColor Green
Write-Host ""

# Display current configuration
Write-Host "Configuration:" -ForegroundColor Cyan
Write-Host "  Resource Group: $ResourceGroup" -ForegroundColor White
Write-Host "  Server: $ServerName" -ForegroundColor White
Write-Host "  Database: $DatabaseName" -ForegroundColor White
Write-Host "  SQL Script: $SqlScriptPath" -ForegroundColor White
Write-Host ""

# Check if resources exist
Write-Host "Checking Azure resources..." -ForegroundColor Cyan

try {
    $server = az sql server show --name $ServerName --resource-group $ResourceGroup --output json | ConvertFrom-Json
    Write-Host "✓ SQL Server found: $($server.name)" -ForegroundColor Green
} catch {
    Write-Host "✗ SQL Server not found: $ServerName" -ForegroundColor Red
    exit 1
}

try {
    $database = az sql db show --name $DatabaseName --server $ServerName --resource-group $ResourceGroup --output json | ConvertFrom-Json
    Write-Host "✓ Database found: $($database.name) (Status: $($database.status))" -ForegroundColor Green
} catch {
    Write-Host "✗ Database not found: $DatabaseName" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Options for running the script
Write-Host "Choose how to run the schema fix:" -ForegroundColor Yellow
Write-Host "1. Azure Portal Query Editor (Recommended)" -ForegroundColor White
Write-Host "2. Azure CLI (if available)" -ForegroundColor White
Write-Host "3. Download script for manual execution" -ForegroundColor White
Write-Host ""

$choice = Read-Host "Enter your choice (1-3)"

switch ($choice) {
    "1" {
        Write-Host ""
        Write-Host "=== Azure Portal Query Editor ===" -ForegroundColor Green
        Write-Host "1. Go to: https://portal.azure.com" -ForegroundColor White
        Write-Host "2. Navigate to: SQL databases > $DatabaseName" -ForegroundColor White
        Write-Host "3. Click 'Query editor (preview)'" -ForegroundColor White
        Write-Host "4. Connect with your Azure AD account" -ForegroundColor White
        Write-Host "5. Copy and paste the contents of: $SqlScriptPath" -ForegroundColor White
        Write-Host "6. Click 'Run' to execute the script" -ForegroundColor White
        Write-Host ""
        Write-Host "The script will:" -ForegroundColor Cyan
        Write-Host "  - Check existing tables and columns" -ForegroundColor White
        Write-Host "  - Create missing tables" -ForegroundColor White
        Write-Host "  - Add missing columns to existing tables" -ForegroundColor White
        Write-Host "  - Validate the final schema" -ForegroundColor White
    }
    "2" {
        Write-Host ""
        Write-Host "=== Azure CLI Execution ===" -ForegroundColor Green
        Write-Host "Note: Azure CLI SQL query support may be limited." -ForegroundColor Yellow
        Write-Host ""
        
        # Try to use Azure CLI if possible
        try {
            Write-Host "Attempting to run script via Azure CLI..." -ForegroundColor Cyan
            $scriptContent = Get-Content $SqlScriptPath -Raw
            
            # Remove GO statements and USE statement for Azure CLI compatibility
            $scriptContent = $scriptContent -replace "USE \[.*?\];", ""
            $scriptContent = $scriptContent -replace "GO", ""
            
            # Split into individual statements
            $statements = $scriptContent -split ";" | Where-Object { $_.Trim().Length -gt 0 }
            
            foreach ($statement in $statements) {
                $trimmed = $statement.Trim()
                if ($trimmed.Length -gt 0) {
                    Write-Host "Executing: $($trimmed.Substring(0, [Math]::Min(50, $trimmed.Length)))..." -ForegroundColor Cyan
                    try {
                        az sql db query --server $ServerName --database $DatabaseName --resource-group $ResourceGroup --querytext $trimmed
                        Write-Host "✓ Statement executed successfully" -ForegroundColor Green
                    } catch {
                        Write-Host "✗ Failed to execute statement" -ForegroundColor Red
                        Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
                    }
                }
            }
        } catch {
            Write-Host "✗ Azure CLI execution failed. Use Option 1 (Azure Portal) instead." -ForegroundColor Red
        }
    }
    "3" {
        Write-Host ""
        Write-Host "=== Manual Execution ===" -ForegroundColor Green
        Write-Host "The SQL script has been saved to: $SqlScriptPath" -ForegroundColor White
        Write-Host ""
        Write-Host "You can:" -ForegroundColor Cyan
        Write-Host "1. Open the file in a text editor" -ForegroundColor White
        Write-Host "2. Copy the contents" -ForegroundColor White
        Write-Host "3. Paste into Azure Portal Query Editor or SQL Server Management Studio" -ForegroundColor White
        Write-Host "4. Execute the script" -ForegroundColor White
        Write-Host ""
        Write-Host "File location: $(Get-Location)\$SqlScriptPath" -ForegroundColor Yellow
    }
    default {
        Write-Host "Invalid choice. Please run the script again and select 1, 2, or 3." -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "=== Next Steps ===" -ForegroundColor Green
Write-Host "After running the schema fix:" -ForegroundColor White
Write-Host "1. Check the function app logs for any remaining database errors" -ForegroundColor White
Write-Host "2. Test the API endpoints to ensure they're working" -ForegroundColor White
Write-Host "3. Verify the frontend can load data without errors" -ForegroundColor White
Write-Host ""

Write-Host "Script completed!" -ForegroundColor Green
