---
inclusion: always
---
# AtomSec SFDC Backend (Azure Functions) Structure and Routing

## Main Entry Point
- The main entry point is [function_app.py](mdc:atomsec-func-sfdc/function_app.py).
- All API endpoints are registered as blueprints and attached to the Azure Function App.
- The app exposes RESTful endpoints for Salesforce integration, security analysis, task management, user management, and more.

## Architectural Notes
- Follows a microservices architecture, with business logic in the SFDC service and proxying via the DB service.
- Uses blueprints for modular endpoint organization (see [api/](mdc:atomsec-func-sfdc/api/)).
- Handles both direct API calls and requests proxied from the DB service.
- CORS and authentication are handled globally.
- Implements enhanced security, performance, monitoring, and deployment best practices.
- Uses service communication utilities for inter-service communication (see [shared/service_communication.py](mdc:atomsec-func-sfdc/shared/service_communication.py)).
- Follows proxy architecture patterns documented in [SFDC_PROXY_ARCHITECTURE.md](mdc:atomsec-func-sfdc/SFDC_PROXY_ARCHITECTURE.md).

## Key API Routes (see [function_app.py](mdc:atomsec-func-sfdc/function_app.py) and [API_SPECIFICATIONS_EXPORT.md](mdc:atomsec-func-sfdc/API_SPECIFICATIONS_EXPORT.md))
- **/api/users**: User CRUD operations
- **/api/accounts**: Account CRUD operations
- **/api/organizations**: Organization CRUD operations
- **/api/integrations**: Integration CRUD and data endpoints (overview, health-check, profiles, pmd-issues)
- **/api/integration/test-connection**: Test Salesforce connection
- **/api/integration/connect**: Connect and store credentials
- **/api/integration/scan/{id}**: Trigger scan for integration
- **/api/security**: Security health checks, profiles, permission sets, overview
- **/api/tasks**: Task management (list, status, schedule, cancel)
- **/api/auth**: Auth endpoints (login, signup, token refresh, Azure AD login/callback)
- **/api/policies**: Policy and rule management
- **/api/user**: User profile management
- **/api/key-vault**: Azure Key Vault operations
- **/api/pmd**: PMD scan and configuration endpoints
- **/api/sfdc-proxy**: Proxy endpoints for SFDC-specific operations
- **/api/health**: Health check endpoint
- **/api/info**: Service info endpoint

## Proxying
- Some endpoints (e.g., /api/sfdc-proxy) are designed to be called only by the DB service, not directly by the frontend.
- The SFDC backend expects all frontend requests to be routed through the DB service for security and CORS consistency.

### Detailed Proxying Explanation

#### Why Proxying?
- **Security:** The SFDC backend contains sensitive Salesforce integration logic and should not be exposed directly to the frontend or external clients.
- **Centralized Access:** All frontend requests go through the DB Service, which acts as a single entry point for authentication, authorization, and CORS handling.
- **Separation of Concerns:** The DB Service handles database and user/account management, while the SFDC backend focuses on Salesforce-specific business logic.

#### How Proxying Works
1. **Frontend → DB Service**
   - The React frontend makes API requests only to the DB Service (e.g., `/api/db/integration/scan/{id}`).
   - The DB Service exposes endpoints for all frontend needs, including those that require Salesforce logic.
2. **DB Service → SFDC Backend**
   - For Salesforce-specific operations (like running a scan, fetching profiles, or getting health scores), the DB Service does not process the request itself.
   - Instead, it forwards (proxies) the request to the SFDC backend using internal HTTP calls.
   - This is implemented in the DB Service’s `api/sfdc_proxy_endpoints.py`.
3. **SFDC Backend**
   - The SFDC backend receives the proxied request from the DB Service.
   - It performs the Salesforce-specific logic (e.g., connecting to Salesforce, running a scan, processing results).
   - It returns the result to the DB Service.
4. **DB Service → Frontend**
   - The DB Service receives the response from the SFDC backend.
   - It may perform additional processing or simply relay the response back to the frontend.

#### Example Flow
- **User triggers a scan from the frontend:**
  1. Frontend calls `POST /api/db/integration/scan/{id}`.
  2. DB Service receives the request and proxies it to the SFDC backend’s scan endpoint.
  3. SFDC backend orchestrates the scan, interacts with Salesforce, and returns the result.
  4. DB Service returns the scan result to the frontend.

#### Key Points
- **Direct Access Blocked:** The frontend cannot call the SFDC backend directly; all requests must go through the DB Service.
- **Internal Trust:** The SFDC backend trusts requests from the DB Service, not from external sources.
- **Consistent CORS & Auth:** The DB Service enforces CORS and authentication for all requests, so the SFDC backend can assume requests are already validated.
- **Proxy Endpoints:** Only certain endpoints (like `/api/sfdc-proxy` or `/integration/scan/{id}`) are meant to be called by the DB Service, not by the frontend.

#### Benefits
- **Security:** Sensitive business logic and credentials are never exposed to the client.
- **Maintainability:** Centralized API surface for the frontend, with clear separation of backend responsibilities.
- **Scalability:** Each service can scale independently and be maintained or updated without affecting the other.

## Adding New Endpoints
- Add new routes as blueprints in [api/](mdc:atomsec-func-sfdc/api/) and register them in [function_app.py](mdc:atomsec-func-sfdc/function_app.py).
- Follow blueprint pattern with proper imports and registrations.
- Ensure all new endpoints follow microservices architecture patterns.
- Use service communication utilities for inter-service calls.
- For consolidated routing, see the main API router pattern in [FUNCTION_CONSOLIDATION.md](mdc:atomsec-func-sfdc/FUNCTION_CONSOLIDATION.md).

## Best Practices Implementation
- **Security**: Enhanced authentication, input validation, and security monitoring
- **Performance**: Connection pooling, caching, and performance optimization
- **Monitoring**: Structured logging, metrics collection, and health checks
- **Configuration**: Environment-aware configuration and feature flags
- **Error Handling**: Circuit breakers, retry policies, and graceful degradation
- **Testing**: Comprehensive unit, integration, and security testing
- **Documentation**: OpenAPI specs, operational runbooks, and architecture decisions

---
description: "AtomSec SFDC Backend (Azure Functions) Structure and Routing"
globs: ["atomsec-func-sfdc/*"]
alwaysApply: false
---
