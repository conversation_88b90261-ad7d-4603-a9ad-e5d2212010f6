#!/usr/bin/env python3
"""
SFDC Service Verification Script

This script verifies that the SFDC service can import all required modules
without errors after the duplicate function removal.
"""

import sys
import os
import traceback

# Add the SFDC service directory to Python path
sfdc_dir = os.path.join(os.path.dirname(__file__), 'atomsec-func-sfdc')
sys.path.insert(0, sfdc_dir)

def test_imports():
    """Test all required imports for the SFDC service"""
    print("🔍 Testing SFDC Service Imports...")
    print("=" * 50)
    
    # Test basic imports
    try:
        import api.sfdc_auth_endpoints
        print("✅ sfdc_auth_endpoints imported successfully")
    except Exception as e:
        print(f"❌ Failed to import sfdc_auth_endpoints: {e}")
        return False
    
    try:
        import api.user_endpoints
        print("✅ user_endpoints imported successfully")
    except Exception as e:
        print(f"❌ Failed to import user_endpoints: {e}")
        return False
    
    try:
        import api.sfdc_proxy_endpoints
        print("✅ sfdc_proxy_endpoints imported successfully")
    except Exception as e:
        print(f"❌ Failed to import sfdc_proxy_endpoints: {e}")
        return False
    
    try:
        import api.cors_handler
        print("✅ cors_handler imported successfully")
    except Exception as e:
        print(f"❌ Failed to import cors_handler: {e}")
        return False
    
    # Test blueprint creation
    try:
        from api.sfdc_auth_endpoints import bp as auth_bp
        print("✅ sfdc_auth_endpoints blueprint created successfully")
    except Exception as e:
        print(f"❌ Failed to create sfdc_auth_endpoints blueprint: {e}")
        return False
    
    try:
        from api.user_endpoints import bp as user_bp
        print("✅ user_endpoints blueprint created successfully")
    except Exception as e:
        print(f"❌ Failed to create user_endpoints blueprint: {e}")
        return False
    
    try:
        from api.sfdc_proxy_endpoints import bp as proxy_bp
        print("✅ sfdc_proxy_endpoints blueprint created successfully")
    except Exception as e:
        print(f"❌ Failed to create sfdc_proxy_endpoints blueprint: {e}")
        return False
    
    try:
        from api.cors_handler import bp as cors_bp
        print("✅ cors_handler blueprint created successfully")
    except Exception as e:
        print(f"❌ Failed to create cors_handler blueprint: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All imports successful! SFDC service is ready.")
    return True

def test_removed_endpoints():
    """Verify that removed endpoints are not accessible"""
    print("\n🔍 Verifying Removed Endpoints...")
    print("=" * 50)
    
    removed_modules = [
        'api.account_endpoints',
        'api.organization_endpoints',
        'api.integration_endpoints',
        'api.security_endpoints',
        'api.task_endpoints',
        'api.policy_endpoints',
        'api.pmd_endpoints',
        'api.key_vault_endpoints',
        'api.user_profile_endpoints',
        'api.auth_endpoints'
    ]
    
    for module_name in removed_modules:
        try:
            __import__(module_name)
            print(f"❌ {module_name} is still importable (should be removed)")
        except ImportError:
            print(f"✅ {module_name} successfully removed")
        except Exception as e:
            print(f"⚠️  {module_name} has unexpected error: {e}")
    
    print("\n" + "=" * 50)

def main():
    """Main verification function"""
    print("🚀 SFDC Service Verification Script")
    print("=" * 50)
    
    # Test current imports
    if not test_imports():
        print("\n❌ Import verification failed!")
        return False
    
    # Test removed endpoints
    test_removed_endpoints()
    
    print("\n📋 Summary:")
    print("✅ SFDC service can import all required modules")
    print("✅ All duplicate endpoints have been removed")
    print("✅ Service is ready for deployment")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
