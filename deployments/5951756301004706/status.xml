﻿<?xml version="1.0" encoding="utf-8"?>
<deployment>
  <id>5951756301004706</id>
  <author><PERSON><PERSON><PERSON></author>
  <deployer>VSTS</deployer>
  <authorEmail />
  <message>{"type":"SlotSwap","commitId":"75079690c16ce608d6f735334a512ab445cfa162","buildId":"595","buildNumber":"20250827.7","repoProvider":"TfsGit","repoName":"atomsec-func-db","collectionUrl":"https://dev.azure.com/AtomSec/","teamProject":"10d575ce-a030-4b1b-865f-8f9f6b329c81","sourceSlot":"stage","targetSlot":"production"}</message>
  <progress />
  <status>Success</status>
  <statusText />
  <lastSuccessEndTime>2025-08-27T13:23:25.3489621Z</lastSuccessEndTime>
  <receivedTime>2025-08-27T13:23:25.3489621Z</receivedTime>
  <startTime>2025-08-27T13:23:25.3489621Z</startTime>
  <endTime>2025-08-27T13:23:25.3489621Z</endTime>
  <complete>True</complete>
  <is_temp>False</is_temp>
  <is_readonly>True</is_readonly>
  <buildSummary />
</deployment>