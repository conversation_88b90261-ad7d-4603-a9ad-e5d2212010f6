﻿<?xml version="1.0" encoding="utf-8"?>
<deployment>
  <id>5931756291306624</id>
  <author><PERSON><PERSON><PERSON></author>
  <deployer>VSTS</deployer>
  <authorEmail />
  <message>{"type":"SlotSwap","commitId":"19a224600358b8061eb14fcaae437e677c24c361","buildId":"593","buildNumber":"20250827.5","repoProvider":"TfsGit","repoName":"atomsec-func-db","collectionUrl":"https://dev.azure.com/AtomSec/","teamProject":"10d575ce-a030-4b1b-865f-8f9f6b329c81","sourceSlot":"stage","targetSlot":"production"}</message>
  <progress />
  <status>Success</status>
  <statusText />
  <lastSuccessEndTime>2025-08-27T10:41:52.5992471Z</lastSuccessEndTime>
  <receivedTime>2025-08-27T10:41:52.5992471Z</receivedTime>
  <startTime>2025-08-27T10:41:52.5992471Z</startTime>
  <endTime>2025-08-27T10:41:52.5992471Z</endTime>
  <complete>True</complete>
  <is_temp>False</is_temp>
  <is_readonly>True</is_readonly>
  <buildSummary />
</deployment>