﻿<?xml version="1.0" encoding="utf-8"?>
<deployment>
  <id>5941756298217840</id>
  <author><PERSON><PERSON><PERSON></author>
  <deployer>VSTS</deployer>
  <authorEmail />
  <message>{"type":"SlotSwap","commitId":"f9c37528a4aba8a8fe4f612b2fe2f7e30288ec80","buildId":"594","buildNumber":"20250827.6","repoProvider":"TfsGit","repoName":"atomsec-func-db","collectionUrl":"https://dev.azure.com/AtomSec/","teamProject":"10d575ce-a030-4b1b-865f-8f9f6b329c81","sourceSlot":"stage","targetSlot":"production"}</message>
  <progress />
  <status>Success</status>
  <statusText />
  <lastSuccessEndTime>2025-08-27T12:36:58.4706708Z</lastSuccessEndTime>
  <receivedTime>2025-08-27T12:36:58.4706708Z</receivedTime>
  <startTime>2025-08-27T12:36:58.4706708Z</startTime>
  <endTime>2025-08-27T12:36:58.4706708Z</endTime>
  <complete>True</complete>
  <is_temp>False</is_temp>
  <is_readonly>True</is_readonly>
  <buildSummary />
</deployment>