<!--
    - Policies are applied in the order they appear.
    - Position <base/> inside a section to inherit policies from the outer scope.
    - Comments within policies are not preserved.
-->
<!-- Add policies as children to the <inbound>, <outbound>, <backend>, and <on-error> elements -->
<policies>
    <!-- Throttle, authorize, validate, cache, or transform the requests -->
    <inbound>
        <base />
        <!-- Primary Policy - Use OpenID Configuration -->
        <validate-jwt header-name="Authorization" failed-validation-httpcode="401" failed-validation-error-message="Unauthorized. Access token is missing or invalid.">
            <openid-config url="https://login.microsoftonline.com/41b676db-bf6f-46ae-a354-a83a1362533f/v2.0/.well-known/openid-configuration" />
            <audiences>
                <audience>82e79715-7451-4680-bd1c-53453bfd45ea</audience>
            </audiences>
            <issuers>
                <issuer>https://sts.windows.net/41b676db-bf6f-46ae-a354-a83a1362533f/</issuer>
            </issuers>
            <required-claims>
                <claim name="appid" match="any">
                    <value>82e79715-7451-4680-bd1c-53453bfd45ea</value>
                </claim>
            </required-claims>
        </validate-jwt>
        <!-- Alternative Policy - Use Direct JWKS URI (if network issues occur) -->
        <!--
<validate-jwt header-name="Authorization" failed-validation-httpcode="401" failed-validation-error-message="Unauthorized. Access token is missing or invalid.">
    <issuer-signing-keys>
        <key-source>https://login.microsoftonline.com/41b676db-bf6f-46ae-a354-a83a1362533f/discovery/v2.0/keys</key-source>
    </issuer-signing-keys>
    <audiences>
        <audience>https://graph.microsoft.com</audience>
    </audiences>
    <issuers>
        <issuer>https://sts.windows.net/41b676db-bf6f-46ae-a354-a83a1362533f/</issuer>
    </issuers>
    <required-claims>
        <claim name="appid" match="any">
            <value>82e79715-7451-4680-bd1c-53453bfd45ea</value>
        </claim>
        <claim name="idtyp" match="any">
            <value>app</value>
        </claim>
    </required-claims>
</validate-jwt>
-->
    </inbound>
    <!-- Control if and how the requests are forwarded to services  -->
    <backend>
        <base />
    </backend>
    <!-- Customize the responses -->
    <outbound>
        <base />
    </outbound>
    <!-- Handle exceptions and customize error responses  -->
    <on-error>
        <base />
    </on-error>
</policies>