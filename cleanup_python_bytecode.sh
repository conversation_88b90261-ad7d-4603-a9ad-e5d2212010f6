#!/bin/bash

# Python Bytecode Cleanup Script
# This script removes all Python bytecode files and cache directories
# Run this before committing changes to ensure clean deployment

echo "🧹 Python Bytecode Cleanup Script"
echo "=================================="

# Function to count files before cleanup
count_files() {
    local pyc_count=$(find . -name "*.pyc" 2>/dev/null | wc -l)
    local pyo_count=$(find . -name "*.pyo" 2>/dev/null | wc -l)
    local pyd_count=$(find . -name "*.pyd" 2>/dev/null | wc -l)
    local pycache_count=$(find . -name "__pycache__" -type d 2>/dev/null | wc -l)
    
    echo "📊 Files found before cleanup:"
    echo "  - .pyc files: $pyc_count"
    echo "  - .pyo files: $pyo_count"
    echo "  - .pyd files: $pyd_count"
    echo "  - __pycache__ directories: $pycache_count"
    echo ""
}

# Function to perform cleanup
cleanup_files() {
    echo "🗑️  Starting cleanup process..."
    
    # Remove .pyc files
    echo "  - Removing .pyc files..."
    find . -name "*.pyc" -delete 2>/dev/null || true
    
    # Remove .pyo files
    echo "  - Removing .pyo files..."
    find . -name "*.pyo" -delete 2>/dev/null || true
    
    # Remove .pyd files
    echo "  - Removing .pyd files..."
    find . -name "*.pyd" -delete 2>/dev/null || true
    
    # Remove __pycache__ directories
    echo "  - Removing __pycache__ directories..."
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    
    echo "✅ Cleanup completed!"
    echo ""
}

# Function to verify cleanup
verify_cleanup() {
    echo "🔍 Verifying cleanup..."
    
    local remaining_pyc=$(find . -name "*.pyc" 2>/dev/null | wc -l)
    local remaining_pyo=$(find . -name "*.pyo" 2>/dev/null | wc -l)
    local remaining_pyd=$(find . -name "*.pyd" 2>/dev/null | wc -l)
    local remaining_pycache=$(find . -name "__pycache__" -type d 2>/dev/null | wc -l)
    
    if [ $remaining_pyc -eq 0 ] && [ $remaining_pyo -eq 0 ] && [ $remaining_pyd -eq 0 ] && [ $remaining_pycache -eq 0 ]; then
        echo "✅ All Python bytecode files successfully removed!"
    else
        echo "⚠️  Some files remain:"
        echo "  - .pyc files: $remaining_pyc"
        echo "  - .pyo files: $remaining_pyo"
        echo "  - .pyd files: $remaining_pyd"
        echo "  - __pycache__ directories: $remaining_pycache"
        
        if [ $remaining_pyc -gt 0 ]; then
            echo "  Remaining .pyc files:"
            find . -name "*.pyc" 2>/dev/null | head -5
        fi
        
        if [ $remaining_pycache -gt 0 ]; then
            echo "  Remaining __pycache__ directories:"
            find . -name "__pycache__" -type d 2>/dev/null | head -5
        fi
    fi
    echo ""
}

# Function to show git status
show_git_status() {
    echo "📋 Git Status:"
    git status --porcelain | grep -E "\.(pyc|pyo|pyd)$|__pycache__" || echo "  No bytecode files in git status"
    echo ""
}

# Main execution
echo "Current directory: $(pwd)"
echo ""

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo "⚠️  Warning: Not in a git repository"
    echo "This script should be run from the root of your project"
    echo ""
fi

# Count files before cleanup
count_files

# Perform cleanup
cleanup_files

# Verify cleanup
verify_cleanup

# Show git status
if [ -d ".git" ]; then
    show_git_status
fi

echo "🎯 Next Steps:"
echo "1. Commit the cleaned codebase"
echo "2. Run your Azure DevOps pipeline"
echo "3. The pipeline will now use Python 3.11 and clean bytecode files"
echo ""
echo "✨ Cleanup script completed!"
