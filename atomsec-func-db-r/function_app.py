"""
AtomSec Database Function App - Deployment Optimized

A simplified, deployment-ready Azure Function App for database operations.
Designed for reliable deployment and runtime stability.

Features:
- Centralized database access layer
- Simplified blueprint registration with error handling
- Health check and diagnostic endpoints
- Production-ready configuration
"""

import logging
import json
import os
import sys
import azure.functions as func
from typing import Dict, Any
from datetime import datetime, timezone

# Configure logging for deployment
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load local settings only in development
def load_local_settings():
    """Load local.settings.json if it exists and we're in development"""
    try:
        # Only load local settings if we're actually running locally
        if os.path.exists('local.settings.json') and os.getenv('WEBSITE_SITE_NAME') is None:
            with open('local.settings.json', 'r') as f:
                settings = json.load(f)

            # Set environment variables from local.settings.json
            if 'Values' in settings:
                for key, value in settings['Values'].items():
                    if key not in os.environ:  # Don't override existing env vars
                        os.environ[key] = value
                logger.info(f"Loaded {len(settings['Values'])} environment variables from local.settings.json")
            return True
    except Exception as e:
        logger.warning(f"Could not load local.settings.json: {e}")
    return False

# Load local settings at startup (only in development)
load_local_settings()

# Create the Function App first
app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)

# Blueprint registration with simplified error handling
def register_blueprint_safely(module_path: str, blueprint_name: str, display_name: str):
    """Safely register a blueprint with error handling"""
    try:
        module = __import__(module_path, fromlist=[blueprint_name])
        blueprint = getattr(module, blueprint_name)
        app.register_functions(blueprint)
        logger.info(f"✅ Successfully registered {display_name}")
        return True
    except ImportError as e:
        logger.warning(f"⚠️  Module {module_path} not found: {e}")
        return False
    except AttributeError as e:
        logger.warning(f"⚠️  Blueprint {blueprint_name} not found in {module_path}: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Failed to register {display_name}: {e}")
        return False

# Register core API blueprints
logger.info("🔄 Registering API blueprints...")
registered_count = 0
total_blueprints = 0

# Core database endpoints
blueprints_to_register = [
    ("api.user_endpoints", "bp", "User Endpoints"),
    ("api.account_endpoints", "bp", "Account Endpoints"),
    ("api.organization_endpoints", "bp", "Organization Endpoints"),
    ("api.integration_endpoints", "bp", "Integration Endpoints"),
    ("api.security_endpoints", "bp", "Security Endpoints"),
    ("api.task_endpoints", "bp", "Task Endpoints"),
    ("api.policy_endpoints", "bp", "Policy Endpoints"),
    ("api.security_assessment_endpoints", "bp", "Security Assessment Endpoints"),
    ("api.execution_log_endpoints", "bp", "Execution Log Endpoints"),
    ("api.cors_handler", "bp", "CORS Handler"),
    ("api.sfdc_proxy_endpoints", "bp", "SFDC Proxy Endpoints"),
    ("api.user_profile_endpoints", "bp", "User Profile Endpoints"),
    ("api.key_vault_endpoints", "bp", "Key Vault Endpoints"),
    ("api.general_endpoints", "bp", "General Endpoints"),
    ("api.pmd_endpoints", "bp", "PMD Endpoints"),
    ("api.sso_endpoints", "bp", "SSO Endpoints"),
    ("api.azure_ad_auth", "bp", "Azure AD Auth"),
    ("api.task_processor_endpoints", "bp", "Task Processor Endpoints"),
    ("api.scan_endpoints", "bp", "Scan Endpoints"),
]

for module_path, blueprint_name, display_name in blueprints_to_register:
    total_blueprints += 1
    if register_blueprint_safely(module_path, blueprint_name, display_name):
        registered_count += 1

logger.info(f"📊 Blueprint registration complete: {registered_count}/{total_blueprints} successful")

# Root DB API endpoint
@app.route(route="db", methods=["GET"])
def db_root(req: func.HttpRequest) -> func.HttpResponse:
    """
    Root endpoint for the database API

    Returns:
        JSON response with API information
    """
    try:
        from shared.cors_middleware import add_cors_headers

        response_data = {
            "service": "atomsec-func-db",
            "status": "running",
            "message": "AtomSec Database Service API",
            "endpoints": {
                "health": "/api/db/health",
                "info": "/api/db/info"
            }
        }

        response = func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )
        return add_cors_headers(req, response)
    except Exception as e:
        logger.error(f"DB root endpoint failed: {str(e)}")
        response = func.HttpResponse(
            json.dumps({
                "error": "Internal server error",
                "message": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )
        return add_cors_headers(req, response)

# Health check endpoint - Deployment optimized
@app.route(route="db/health", methods=["GET"])
def health_check(req: func.HttpRequest) -> func.HttpResponse:
    """
    Health check endpoint for the database function app
    Simplified for reliable deployment and startup
    """
    try:
        # Basic health status
        health_status = {
            "status": "healthy",
            "service": "atomsec-func-db-r",
            "version": "2.0.0",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "environment": {
                "site_name": os.getenv('WEBSITE_SITE_NAME', 'local'),
                "python_version": sys.version.split()[0],
                "functions_version": os.getenv('FUNCTIONS_EXTENSION_VERSION', 'unknown'),
                "worker_runtime": os.getenv('FUNCTIONS_WORKER_RUNTIME', 'unknown')
            },
            "checks": {
                "function_app": "healthy",
                "runtime": "healthy"
            }
        }

        # Test basic connectivity only if modules are available
        try:
            # Try to import and test basic functionality
            from shared.azure_services import is_local_dev
            health_status["environment"]["is_local"] = is_local_dev()
            health_status["checks"]["shared_modules"] = "loaded"
        except ImportError:
            health_status["checks"]["shared_modules"] = "not_available"
            health_status["status"] = "degraded"
        except Exception as e:
            health_status["checks"]["shared_modules"] = f"error: {str(e)}"
            health_status["status"] = "degraded"

        # Test storage connectivity (non-blocking)
        try:
            from shared.data_access import get_table_storage_repository
            test_repo = get_table_storage_repository("HealthCheck")
            health_status["checks"]["table_storage"] = "connected" if test_repo else "failed"
        except Exception as e:
            health_status["checks"]["table_storage"] = "not_tested"
            logger.warning(f"Storage health check skipped: {e}")

        # Always return 200 for basic health check (function app is running)
        status_code = 200

        # Add CORS headers manually (fallback if cors module fails)
        response = func.HttpResponse(
            json.dumps(health_status, indent=2),
            mimetype="application/json",
            status_code=status_code,
            headers={
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With'
            }
        )

        # Try to add proper CORS headers if available
        try:
            from shared.cors_middleware import add_cors_headers
            return add_cors_headers(req, response)
        except ImportError:
            return response

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        error_response = func.HttpResponse(
            json.dumps({
                "status": "unhealthy",
                "service": "atomsec-func-db-r",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }, indent=2),
            mimetype="application/json",
            status_code=503,
            headers={
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With'
            }
        )
        return error_response

# Info endpoint
@app.route(route="db/info", methods=["GET"])
def info(req: func.HttpRequest) -> func.HttpResponse:
    """
    Information endpoint for the database function app

    Returns:
        JSON response with service information
    """
    info_data = {
        "service": "atomsec-func-db",
        "version": "1.0.0",
        "description": "AtomSec Database Service - Centralized database operations",
        "endpoints": {
            "users": {
                "base": "/api/users",
                "operations": ["GET", "POST", "PUT", "DELETE"]
            },
            "accounts": {
                "base": "/api/accounts",
                "operations": ["GET", "POST", "PUT", "DELETE"]
            },
            "organizations": {
                "base": "/api/organizations",
                "operations": ["GET", "POST", "PUT", "DELETE"]
            },
            "integrations": {
                "base": "/api/integrations",
                "operations": ["GET", "POST", "PUT", "DELETE"],
                "sub_endpoints": ["/overview", "/health-check", "/profiles", "/credentials", "/pmd-issues"]
            },
            "security": {
                "base": "/api/security",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/health-checks", "/profiles", "/overview"]
            },
            "tasks": {
                "base": "/api/tasks",
                "operations": ["GET", "POST", "PUT"],
                "sub_endpoints": ["/status", "/results"]
            },
            "auth": {
                "base": "/api/auth",
                "operations": ["GET"],
                "sub_endpoints": ["/azure/login", "/azure/callback", "/azure/me"]
            },
            "policies": {
                "base": "/api/policies",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/rules", "/policy-rule-settings", "/policy-rule-settings/enabled-tasks"]
            },
            "user_profile": {
                "base": "/api/user",
                "operations": ["GET", "PUT"],
                "sub_endpoints": ["/profile", "/password"]
            },
            "key_vault": {
                "base": "/api/key-vault",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/secrets", "/create", "/access-policy", "/client-credentials"]
            },
            "sfdc_proxy": {
                "base": "/v1",
                "description": "Proxy endpoints to SFDC service",
                "operations": ["GET", "POST"],
                "sub_endpoints": [
                    "/integration/scan/{id}",
                    "/task-status",
                    "/tasks/cancel",
                    "/tasks/schedule",
                    "/health-score",
                    "/health-risks",
                    "/profiles",
                    "/permission-sets",
                    "/scan/accounts",
                    "/scan/history",
                    "/sfdc/health",
                    "/sfdc/info"
                ]
            }
        },
        "features": [
            "Dual storage support (Azure Table Storage + SQL Database)",
            "Environment-aware (local dev + production)",
            "Repository pattern implementation",
            "Comprehensive error handling",
            "RESTful API design",
            "Service Bus event publishing",
            "Event-driven architecture"
        ]
    }

    return func.HttpResponse(
        json.dumps(info_data),
        mimetype="application/json",
        status_code=200
    )

# Diagnostic endpoint
@app.route(route="diagnostic", methods=["GET"])
def diagnostic(req: func.HttpRequest) -> func.HttpResponse:
    """
    Diagnostic endpoint for troubleshooting configuration and connection issues

    Returns:
        JSON response with diagnostic information
    """
    try:
        from shared.config import validate_storage_configuration
        from shared.azure_services import is_local_dev
        
        diagnostic_info = {
            "timestamp": datetime.now().isoformat(),
            "environment": "local" if is_local_dev() else "production",
            "storage_config": validate_storage_configuration(),
            "azure_environment_vars": {
                "WEBSITE_SITE_NAME": os.environ.get("WEBSITE_SITE_NAME"),
                "WEBSITE_INSTANCE_ID": os.environ.get("WEBSITE_INSTANCE_ID"),
                "FUNCTIONS_WORKER_RUNTIME": os.environ.get("FUNCTIONS_WORKER_RUNTIME"),
                "FUNCTIONS_WORKER_RUNTIME_VERSION": os.environ.get("FUNCTIONS_WORKER_RUNTIME_VERSION"),
                "IS_LOCAL_DEV": os.environ.get("IS_LOCAL_DEV"),
                "USE_LOCAL_STORAGE": os.environ.get("USE_LOCAL_STORAGE"),
                "WEBSITE_HOSTNAME": os.environ.get("WEBSITE_HOSTNAME")
            }
        }
        
        return func.HttpResponse(
            json.dumps(diagnostic_info, indent=2),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Diagnostic endpoint failed: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }),
            mimetype="application/json",
            status_code=500
        )

def handle_error(e: Exception, operation: str) -> func.HttpResponse:
    """
    Standard error handler for database operations

    Args:
        e: Exception that occurred
        operation: Description of the operation that failed

    Returns:
        func.HttpResponse: Error response
    """
    logger.error(f"Error in {operation}: {str(e)}")
    return func.HttpResponse(
        json.dumps({
            "success": False,
            "error": f"Database operation failed: {str(e)}"
        }),
        mimetype="application/json",
        status_code=500
    )

# Log successful initialization
deployment_context = {
    "service": "atomsec-func-db-r",
    "version": "2.0.0",
    "blueprints_registered": registered_count,
    "total_blueprints": total_blueprints,
    "environment": {
        "site_name": os.getenv('WEBSITE_SITE_NAME', 'local'),
        "deployment_id": os.getenv('WEBSITE_DEPLOYMENT_ID', 'unknown'),
        "functions_version": os.getenv('FUNCTIONS_EXTENSION_VERSION', 'unknown'),
        "python_version": sys.version.split()[0]
    }
}

logger.info("🎉 AtomSec Database Function App initialized successfully")
logger.info(f"📋 Deployment context: {deployment_context}")
logger.info("🔗 Available endpoints:")
logger.info("  GET /api/db - Root API endpoint")
logger.info("  GET /api/db/health - Health check endpoint")
logger.info("  GET /api/db/info - Service information endpoint")
logger.info("  GET /api/diagnostic - Diagnostic endpoint")
logger.info("✅ Function App ready for Azure Functions host discovery")