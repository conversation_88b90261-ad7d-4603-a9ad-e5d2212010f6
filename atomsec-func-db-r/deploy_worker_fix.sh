#!/bin/bash

# Quick deployment script to fix Python worker runtime issue
# This script redeploys the function app with the corrected Python worker configuration

set -e

echo "🔧 Deploying Python Worker Runtime Fix..."
echo "=========================================="

# Check if we're in the right directory
if [ ! -f "function_app.py" ]; then
    echo "❌ ERROR: Please run this script from the atomsec-func-db-r directory"
    exit 1
fi

# Set environment variables (update these as needed)
FUNCTION_APP_NAME="func-atomsec-dbconnect-dev02"
RESOURCE_GROUP="atomsec-dev-data"
AZURE_SUBSCRIPTION="your-subscription-id"  # Update this

echo "📋 Configuration:"
echo "  Function App: $FUNCTION_APP_NAME"
echo "  Resource Group: $RESOURCE_GROUP"
echo "  Subscription: $AZURE_SUBSCRIPTION"
echo ""

# Step 1: Install Python worker runtime locally
echo "🔧 Step 1: Installing Python worker runtime..."
python -m pip install --upgrade pip setuptools wheel
pip install azure-functions==1.17.0

# Verify installation
if [ -d ".python_packages" ]; then
    echo "✅ .python_packages directory exists"
    ls -la .python_packages/
else
    echo "⚠️  .python_packages directory missing - creating it"
    mkdir -p .python_packages/lib/site-packages
    pip install azure-functions==1.17.0 -t .python_packages/lib/site-packages/
fi

# Step 2: Create deployment package
echo ""
echo "📦 Step 2: Creating deployment package..."
DEPLOY_DIR="deploy_temp"
rm -rf "$DEPLOY_DIR"
mkdir -p "$DEPLOY_DIR"

# Copy essential files
cp -r api/ "$DEPLOY_DIR/" 2>/dev/null || echo "api/ directory not found"
cp -r shared/ "$DEPLOY_DIR/" 2>/dev/null || echo "shared/ directory not found"
cp -r repositories/ "$DEPLOY_DIR/" 2>/dev/null || echo "repositories/ directory not found"
cp -r home/ "$DEPLOY_DIR/" 2>/dev/null || echo "home/ directory not found"
cp -r .python_packages/ "$DEPLOY_DIR/" 2>/dev/null || echo ".python_packages directory not found"
cp function_app.py "$DEPLOY_DIR/"
cp host.json "$DEPLOY_DIR/"
cp requirements.txt "$DEPLOY_DIR/"
cp .funcignore "$DEPLOY_DIR/"

# Verify .python_packages is included
if [ -d "$DEPLOY_DIR/.python_packages" ]; then
    echo "✅ .python_packages included in deployment package"
else
    echo "❌ ERROR: .python_packages not found in deployment package"
    exit 1
fi

# Create zip package
ZIP_FILE="deployment_worker_fix.zip"
rm -f "$ZIP_FILE"
cd "$DEPLOY_DIR"
zip -r "../$ZIP_FILE" .
cd ..

echo "✅ Deployment package created: $ZIP_FILE"

# Step 3: Deploy to Azure
echo ""
echo "🚀 Step 3: Deploying to Azure..."
echo "Note: You may need to run this manually if you don't have Azure CLI access:"
echo ""
echo "az functionapp deployment source config-zip \\"
echo "  --resource-group $RESOURCE_GROUP \\"
echo "  --name $FUNCTION_APP_NAME \\"
echo "  --src $ZIP_FILE"
echo ""

# Clean up
rm -rf "$DEPLOY_DIR"

echo "✅ Deployment package ready: $ZIP_FILE"
echo "🔧 Python worker runtime fix applied"
echo ""
echo "Next steps:"
echo "1. Deploy the package using the Azure CLI command above"
echo "2. Or run the full pipeline: pipeline-func-db-dev.yml"
echo "3. Monitor the function app logs for the worker runtime error to be resolved"
