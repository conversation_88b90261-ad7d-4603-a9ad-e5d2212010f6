# APIM Authentication Flow - JWT Validation

## Overview

The AtomSec application uses Azure API Management (APIM) for all authentication and API calls, including local development. This ensures consistent JWT validation and security across all environments.

## Architecture

```
Frontend → Azure AD (login.microsoft.com) → JWT Token → APIM → DB Service
```

### **1. Authentication Flow**
1. **User clicks "Sign in with Microsoft"**
2. **Frontend redirects to Azure AD** (`https://login.microsoftonline.com/...`)
3. **Azure AD authenticates user** and returns authorization code
4. **Frontend exchanges code for JWT token** using Azure AD
5. **Frontend includes JWT token** in all subsequent API calls
6. **APIM validates JWT token** using JWT policy
7. **APIM forwards validated request** to DB Service

### **2. JWT Validation in APIM**
- **JWT Policy**: APIM has an inbound JWT validation policy
- **Token Validation**: APIM validates the JWT token before forwarding requests
- **Security**: Only requests with valid JWT tokens reach the DB Service
- **No Local Auth**: The DB Service does not handle authentication directly

## Configuration

### **Frontend Configuration**
```javascript
// Always use APIM for authentication
const API_CONFIG = {
  baseURL: '**************************************/db',
  apiVersion: 'v1',
  apimSubscriptionKey: 'bd50cc1018444ae987a04c465534e428'
};
```

### **Environment Variables**
```bash
# .env.local
REACT_APP_APIM_BASE_URL=**************************************/db
REACT_APP_API_VERSION=v1
REACT_APP_APIM_SUBSCRIPTION_KEY=bd50cc1018444ae987a04c465534e428
REACT_APP_AZURE_CLIENT_ID=your-azure-ad-client-id
REACT_APP_AZURE_TENANT_ID=your-azure-ad-tenant-id
REACT_APP_REDIRECT_URI=http://localhost:3000/auth/callback
```

## API Endpoints

### **Authentication Endpoints (through APIM)**
```
POST /v1/auth/login                    # Local login (if needed)
GET  /v1/auth/azure/login             # Initiate Azure AD login
GET  /v1/auth/azure/callback          # Handle Azure AD callback
GET  /v1/auth/azure/me                # Get current user info
POST /v1/users/login/verify           # Verify login status
```

### **Data Endpoints (through APIM)**
```
GET  /v1/users                         # Get users
GET  /v1/accounts                      # Get accounts
GET  /v1/integrations                  # Get integrations
GET  /v1/tasks                         # Get tasks
GET  /v1/health                        # Health check
GET  /v1/info                          # Service info
```

## Request Headers

### **Required Headers**
```http
Ocp-Apim-Subscription-Key: bd50cc1018444ae987a04c465534e428
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

### **Optional Headers**
```http
X-User-ID: <user-id>
X-Organization-ID: <org-id>
X-Service: db
X-Client: atomsec-frontend
```

## Local Development

### **Important Notes**
- **No Local Authentication**: Authentication endpoints don't exist in local DB service
- **Always Use APIM**: Even in local development, use APIM endpoints
- **JWT Required**: All API calls must include valid JWT tokens
- **APIM Subscription Key**: Required for all environments

### **Testing Locally**
1. **Set environment variables** for APIM and Azure AD
2. **Start frontend application** (npm start)
3. **Click "Sign in with Microsoft"** - redirects to Azure AD
4. **Complete Azure AD authentication** - returns to frontend
5. **Frontend receives JWT token** and makes API calls through APIM
6. **APIM validates JWT** and forwards to DB Service

## Troubleshooting

### **Common Issues**

#### **1. 404 Error on Authentication Endpoints**
**Problem**: Trying to access local authentication endpoints
**Solution**: Use APIM endpoints instead
```bash
# ❌ Wrong (local endpoint doesn't exist)
http://localhost:7072/api/db/auth/azure/login

# ✅ Correct (use APIM)
**************************************/db/v1/auth/azure/login
```

#### **2. Missing APIM Subscription Key**
**Problem**: 401 Unauthorized from APIM
**Solution**: Include subscription key in headers
```http
Ocp-Apim-Subscription-Key: bd50cc1018444ae987a04c465534e428
```

#### **3. Invalid JWT Token**
**Problem**: 401 Unauthorized from APIM
**Solution**: Ensure valid JWT token from Azure AD
```http
Authorization: Bearer <valid-jwt-token>
```

#### **4. CORS Issues**
**Problem**: Preflight request failures
**Solution**: APIM handles CORS - check APIM configuration

### **Debug Steps**
1. **Check Network Tab**: Verify requests go to APIM, not localhost
2. **Verify Headers**: Ensure subscription key and JWT token are included
3. **Check APIM Logs**: Look for JWT validation errors
4. **Test APIM Directly**: Use Postman/curl to test APIM endpoints

## Security Benefits

### **1. Centralized Authentication**
- All authentication handled by APIM
- Consistent JWT validation across all services
- No authentication logic in individual services

### **2. JWT Policy Enforcement**
- APIM validates JWT tokens before forwarding requests
- Invalid tokens are rejected at the API gateway
- DB Service only receives validated requests

### **3. Environment Consistency**
- Same authentication flow in development and production
- No local authentication bypasses
- Consistent security posture

## Next Steps

1. **Update Frontend**: Ensure all authentication calls go through APIM
2. **Test Authentication**: Verify "Sign in with Microsoft" works with APIM
3. **Monitor APIM Logs**: Check for JWT validation success/failures
4. **Remove Local Auth**: Clean up any local authentication code

## Summary

The authentication flow is:
1. **Frontend** → **Azure AD** → **JWT Token**
2. **Frontend** → **APIM** (with JWT) → **DB Service**
3. **APIM** validates JWT using JWT policy
4. **DB Service** receives only validated requests

This architecture ensures consistent security and JWT validation across all environments while maintaining the benefits of centralized authentication management through APIM.
