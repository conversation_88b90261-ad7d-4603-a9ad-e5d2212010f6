[{"name": "APPLICATIONINSIGHTS_AUTHENTICATION_STRING", "value": "Authorization=AAD", "slotSetting": false}, {"name": "APPLICATIONINSIGHTS_CONNECTION_STRING", "value": "InstrumentationKey=f9571295-14d8-44a5-98c7-aaa2a0e938e4;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=6a5119dd-5ae0-4bb1-9570-3ba1ea702380", "slotSetting": false}, {"name": "AZURE_AD_ADDITIONAL_CLIENT_IDS", "value": "82e79715-7451-4680-bd1c-53453bfd45ea", "slotSetting": false}, {"name": "AZURE_AD_CLIENT_ID", "value": "@Microsoft.KeyVault(SecretUri=https://akv-atomsec-dev.vault.azure.net/secrets/azure-ad-client-id)", "slotSetting": false}, {"name": "AZURE_AD_CLIENT_SECRET", "value": "@Microsoft.KeyVault(SecretUri=https://akv-atomsec-dev.vault.azure.net/secrets/azure-ad-client-secret)", "slotSetting": false}, {"name": "AZURE_AD_TENANT_ID", "value": "@Microsoft.KeyVault(SecretUri=https://akv-atomsec-dev.vault.azure.net/secrets/azure-ad-tenant-id)", "slotSetting": false}, {"name": "AZURE_SERVICE_BUS_CONNECTION_STRING", "value": "Endpoint=sb://atomsec.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ubVwUkj4yj5IszQsD1IygjI7ox062MSKs+ASbL/bIU0=", "slotSetting": false}, {"name": "AZURE_STORAGE_CONNECTION_STRING", "value": "DefaultEndpointsProtocol=https;AccountName=statomsecdbconnectdev02;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "slotSetting": false}, {"name": "AZURE_TABLE_STORAGE_CONNECTION_STRING", "value": "DefaultEndpointsProtocol=https;AccountName=statomsecdbconnectdev02;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "slotSetting": false}, {"name": "AzureWebJobsFeatureFlags", "value": "EnableWorkerIndexing", "slotSetting": false}, {"name": "AzureWebJobsStorage__accountName", "value": "statomsecdbconnectdev02", "slotSetting": false}, {"name": "AzureWebJobsStorage__blobServiceUri", "value": "https://statomsecdbconnectdev02.blob.core.windows.net", "slotSetting": false}, {"name": "AzureWebJobsStorage__credential", "value": "managedidentity", "slotSetting": false}, {"name": "AzureWebJobsStorage__queueServiceUri", "value": "https://statomsecdbconnectdev02.queue.core.windows.net", "slotSetting": false}, {"name": "AzureWebJobsStorage__tableServiceUri", "value": "https://statomsecdbconnectdev02.table.core.windows.net", "slotSetting": false}, {"name": "DB_SERVICE_BASE_URL", "value": "https://func-atomsec-dbconnect-dev02.azurewebsites.net", "slotSetting": false}, {"name": "ENVIRONMENT", "value": "dev02", "slotSetting": false}, {"name": "FRONTEND_URL", "value": "https://app-atomsec-dev01.azurewebsites.net", "slotSetting": false}, {"name": "FUNCTIONS_EXTENSION_VERSION", "value": "~4", "slotSetting": false}, {"name": "FUNCTIONS_WORKER_RUNTIME", "value": "python", "slotSetting": false}, {"name": "FUNCTIONS_WORKER_RUNTIME_VERSION", "value": "~4", "slotSetting": false}, {"name": "IS_LOCAL_DEV", "value": "false", "slotSetting": false}, {"name": "JWT_SECRET", "value": "@Microsoft.KeyVault(SecretUri=https://akv-atomsec-dev.vault.azure.net/secrets/jwt-secret)", "slotSetting": false}, {"name": "KEY_VAULT_NAME", "value": "akv-atomsec-dev", "slotSetting": false}, {"name": "KEY_VAULT_URL", "value": "https://akv-atomsec-dev.vault.azure.net/", "slotSetting": false}, {"name": "LOG_LEVEL", "value": "INFO", "slotSetting": false}, {"name": "PYTHON_ISOLATE_WORKER_DEPENDENCIES", "value": "0", "slotSetting": false}, {"name": "PYTHON_VERSION", "value": "3.11", "slotSetting": false}, {"name": "SCM_DO_BUILD_DURING_DEPLOYMENT", "value": "false", "slotSetting": false}, {"name": "SFDC_SERVICE_URL", "value": "https://func-atomsec-sfdc-dev02-cydhasanfhdmdgar.eastus-01.azurewebsites.net", "slotSetting": false}, {"name": "USE_LOCAL_STORAGE", "value": "false", "slotSetting": false}, {"name": "WEBSITE_CONTENTAZUREFILECONNECTIONSTRING", "value": "DefaultEndpointsProtocol=https;AccountName=statomsecdbconnectdev02;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "slotSetting": false}, {"name": "WEBSITE_CONTENTSHARE", "value": "func-atomsec-dbconnect-dev02-stage", "slotSetting": false}, {"name": "WEBSITE_ENABLE_APP_SERVICE_STORAGE", "value": "false", "slotSetting": false}, {"name": "WEBSITE_ENABLE_SYNC_UPDATE_SITE", "value": "true", "slotSetting": false}, {"name": "WEBSITE_RUN_FROM_PACKAGE", "value": "1", "slotSetting": false}, {"name": "WEBSITE_WEBSOCKETS_ENABLED", "value": "false", "slotSetting": false}, {"name": "WEBSITES_ENABLE_APP_SERVICE_STORAGE", "value": "false", "slotSetting": false}]