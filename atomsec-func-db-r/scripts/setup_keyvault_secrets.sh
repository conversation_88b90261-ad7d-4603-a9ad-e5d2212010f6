#!/bin/bash

# Key Vault Secrets Setup Script for AtomSec DB Function App
# This script helps you add the required secrets to your Azure Key Vault

set -e

echo "🔐 Setting up Key Vault secrets for AtomSec DB Function App"
echo "=========================================================="

# Configuration - Update these values
KEY_VAULT_NAME="akv-atomsec-dev"
RESOURCE_GROUP="atomsec-dev-data"
SUBSCRIPTION_ID="********-3fc5-49c1-91cd-3ab90df8d78d"

echo "📋 Configuration:"
echo "  Key Vault: $KEY_VAULT_NAME"
echo "  Resource Group: $RESOURCE_GROUP"
echo "  Subscription: $SUBSCRIPTION_ID"
echo ""

# Check if Azure CLI is available
if ! command -v az &> /dev/null; then
    echo "❌ Error: Azure CLI is not installed or not in PATH"
    echo "Please install Azure CLI first: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    exit 1
fi

# Check if logged in
if ! az account show &> /dev/null; then
    echo "❌ Error: Not logged into Azure CLI"
    echo "Please run: az login"
    exit 1
fi

# Set subscription
echo "🔧 Setting subscription..."
az account set --subscription "$SUBSCRIPTION_ID"

# Check if Key Vault exists
echo "🔍 Checking if Key Vault exists..."
if ! az keyvault show --name "$KEY_VAULT_NAME" --resource-group "$RESOURCE_GROUP" &> /dev/null; then
    echo "❌ Error: Key Vault '$KEY_VAULT_NAME' not found in resource group '$RESOURCE_GROUP'"
    echo "Please create the Key Vault first or update the configuration"
    exit 1
fi

echo "✅ Key Vault found"

# Function to add secret
add_secret() {
    local secret_name="$1"
    local secret_value="$2"
    local description="$3"
    
    echo "🔐 Adding secret: $secret_name"
    echo "  Description: $description"
    
    # Check if secret already exists
    if az keyvault secret show --vault-name "$KEY_VAULT_NAME" --name "$secret_name" &> /dev/null; then
        echo "  ℹ️  Secret already exists, updating..."
        az keyvault secret set --vault-name "$KEY_VAULT_NAME" --name "$secret_name" --value "$secret_value" --description "$description"
    else
        echo "  ➕ Creating new secret..."
        az keyvault secret set --vault-name "$KEY_VAULT_NAME" --name "$secret_name" --value "$secret_value" --description "$description"
    fi
    
    echo "  ✅ Secret '$secret_name' configured successfully"
    echo ""
}

# Add Azure AD secrets
echo "📝 Adding Azure AD authentication secrets..."
echo "Note: You will need to provide the actual values for these secrets"
echo ""

# Azure AD Client ID
read -p "Enter Azure AD Client ID: " AZURE_AD_CLIENT_ID
if [ -n "$AZURE_AD_CLIENT_ID" ]; then
    add_secret "azure-ad-client-id" "$AZURE_AD_CLIENT_ID" "Azure AD Client ID for AtomSec DB Function App"
else
    echo "⚠️  Skipping Azure AD Client ID (empty value)"
fi

# Azure AD Client Secret
read -s -p "Enter Azure AD Client Secret: " AZURE_AD_CLIENT_SECRET
echo ""
if [ -n "$AZURE_AD_CLIENT_SECRET" ]; then
    add_secret "azure-ad-client-secret" "$AZURE_AD_CLIENT_SECRET" "Azure AD Client Secret for AtomSec DB Function App"
else
    echo "⚠️  Skipping Azure AD Client Secret (empty value)"
fi

# Azure AD Tenant ID
read -p "Enter Azure AD Tenant ID: " AZURE_AD_TENANT_ID
if [ -n "$AZURE_AD_TENANT_ID" ]; then
    add_secret "azure-ad-tenant-id" "$AZURE_AD_TENANT_ID" "Azure AD Tenant ID for AtomSec DB Function App"
else
    echo "⚠️  Skipping Azure AD Tenant ID (empty value)"
fi

# JWT Secret
read -s -p "Enter JWT Secret (or press Enter to generate): " JWT_SECRET
echo ""
if [ -z "$JWT_SECRET" ]; then
    JWT_SECRET=$(openssl rand -base64 32)
    echo "🔑 Generated JWT Secret: $JWT_SECRET"
fi
add_secret "jwt-secret" "$JWT_SECRET" "JWT Secret for AtomSec DB Function App"

echo ""
echo "🎉 Key Vault secrets setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Update your Azure DevOps variable group 'vg-atomsec-db-dev' with the new variables"
echo "2. Ensure your Function App has access to the Key Vault (Managed Identity or Service Principal)"
echo "3. Run your pipeline to deploy with the new configuration"
echo ""
echo "🔐 Secrets added to Key Vault '$KEY_VAULT_NAME':"
echo "  - azure-ad-client-id"
echo "  - azure-ad-client-secret" 
echo "  - azure-ad-tenant-id"
echo "  - jwt-secret"
echo ""
echo "📝 Variable group variables to add:"
echo "  - AZURE_AD_CLIENT_ID: @Microsoft.KeyVault(SecretUri=https://$KEY_VAULT_NAME.vault.azure.net/secrets/azure-ad-client-id/)"
echo "  - AZURE_AD_CLIENT_SECRET: @Microsoft.KeyVault(SecretUri=https://$KEY_VAULT_NAME.vault.azure.net/secrets/azure-ad-client-secret/)"
echo "  - AZURE_AD_TENANT_ID: @Microsoft.KeyVault(SecretUri=https://$KEY_VAULT_NAME.vault.azure.net/secrets/azure-ad-tenant-id/)"
echo "  - JWT_SECRET: @Microsoft.KeyVault(SecretUri=https://$KEY_VAULT_NAME.vault.azure.net/secrets/jwt-secret/)"
echo "  - DB_SERVICE_BASE_URL: https://func-atomsec-dbconnect-dev02.azurewebsites.net"
echo "  - DB_SERVICE_BASE_URL_STAGE: https://func-atomsec-dbconnect-dev02-stage.azurewebsites.net"



