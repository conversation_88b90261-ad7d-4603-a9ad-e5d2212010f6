# Staging Slot Configuration Scripts

This directory contains scripts for configuring the staging slot settings, allowing the architect team to manage Function App configuration separately from the deployment pipeline.

## 📁 Files

- **`configure-staging-slot.sh`** - Main configuration script for the architect team
- **`config-files/db-stage-application-settings.json`** - Configuration template with all required settings

## 🚀 Quick Start

### Prerequisites

1. **Azure CLI** installed and authenticated
2. **jq** (JSON processor) installed
3. **Appropriate permissions** to modify Function App settings

### Installation

```bash
# Make the script executable
chmod +x scripts/configure-staging-slot.sh

# Install jq if not already installed
# On Ubuntu/Debian:
sudo apt-get install jq

# On macOS:
brew install jq

# On Windows (with WSL):
sudo apt-get install jq
```

### Usage

```bash
# Run the configuration script
./scripts/configure-staging-slot.sh
```

The script will prompt you for:
- Function App name
- Resource Group name
- Subscription ID (optional)

## ⚙️ Configuration Process

The script performs the following steps:

1. **Prerequisites Check** - Verifies Azure CLI and jq are installed
2. **Function App Verification** - Confirms the Function App and staging slot exist
3. **Configuration Application** - Applies all settings from the JSON file
4. **Verification** - Confirms critical settings are properly applied
5. **Restart** - Restarts the staging slot to apply changes

## 📋 Configuration File

The `db-stage-application-settings.json` file contains all the necessary application settings:

### Critical Settings

- **`FUNCTIONS_WORKER_RUNTIME=python`** - Enables Python runtime
- **`FUNCTIONS_EXTENSION_VERSION=~4`** - Uses Azure Functions v4
- **`WEBSITE_RUN_FROM_PACKAGE=1`** - Runs from deployment package
- **`PYTHON_VERSION=3.11`** - Uses Python 3.11 (compatible with Functions v4)

### Authentication Settings

- **Azure AD Client ID/Secret** - For authentication
- **JWT Secret** - For token validation
- **Key Vault References** - For secure secret storage

### Service Configuration

- **Storage Connection Strings** - For Azure Storage
- **Service Bus Connection** - For messaging
- **Application Insights** - For monitoring

## 🔧 Modifying Configuration

To modify the configuration:

1. **Edit the JSON file**:
   ```bash
   nano scripts/config-files/db-stage-application-settings.json
   ```

2. **Run the configuration script**:
   ```bash
   ./scripts/configure-staging-slot.sh
   ```

3. **Verify changes**:
   ```bash
   az webapp config appsettings list \
     --name <FUNCTION_APP_NAME> \
     --resource-group <RESOURCE_GROUP> \
     --slot stage
   ```

## 🚨 Important Notes

### When to Use This Script

- **Initial setup** of a new staging slot
- **Configuration changes** that don't require code deployment
- **Environment-specific settings** that vary between deployments
- **Troubleshooting** configuration issues

### When NOT to Use This Script

- **Code deployments** - Use the pipeline instead
- **Frequent changes** - Consider using Azure DevOps variable groups
- **Production changes** - Always go through the staging slot first

### Security Considerations

- **Key Vault references** are used for sensitive values
- **Connection strings** should be rotated regularly
- **Access permissions** should be reviewed periodically

## 🔍 Troubleshooting

### Common Issues

1. **Permission Denied**
   - Ensure you have Contributor or Owner role on the Function App
   - Check if you're authenticated to the correct subscription

2. **Staging Slot Not Found**
   - The script will automatically create the staging slot if it doesn't exist
   - Ensure the Function App exists and is accessible

3. **Configuration Not Applied**
   - Check Azure CLI version (`az --version`)
   - Verify the JSON file is valid (`jq . config-files/db-stage-application-settings.json`)
   - Check Function App logs for errors

### Debug Mode

To see detailed output, you can modify the script to add `--verbose` flags to Azure CLI commands.

## 📚 Related Documentation

- [Azure Functions Configuration](https://docs.microsoft.com/en-us/azure/azure-functions/functions-app-settings)
- [Azure CLI Web App Commands](https://docs.microsoft.com/en-us/cli/azure/webapp)
- [Azure DevOps Variable Groups](https://docs.microsoft.com/en-us/azure/devops/pipelines/library/variable-groups)

## 🆘 Support

If you encounter issues:

1. **Check the logs** - The script provides detailed output
2. **Verify permissions** - Ensure you have the right access levels
3. **Test manually** - Try running individual Azure CLI commands
4. **Contact the team** - For persistent issues, reach out to the development team

---

**Note**: This script is designed to be run by the architect team and should not be modified by developers. For development-related changes, use the deployment pipeline instead.

