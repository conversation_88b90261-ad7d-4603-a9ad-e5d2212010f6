#!/bin/bash
# =============================================================================
# Staging Slot Configuration Script for Architect Team
# =============================================================================
# This script configures the staging slot with all necessary application settings
# based on the db-stage-application-settings.json file.
#
# Usage: ./scripts/configure-staging-slot.sh
# 
# Prerequisites:
# - Azure CLI installed and authenticated
# - Appropriate permissions to modify Function App settings
# - db-stage-application-settings.json file in the scripts/config-files/ directory
#
# =============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
CONFIG_FILE="$SCRIPT_DIR/config-files/db-stage-application-settings.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if required tools are available
check_prerequisites() {
    print_status $BLUE "🔍 Checking prerequisites..."
    
    # Check if Azure CLI is installed
    if ! command -v az &> /dev/null; then
        print_status $RED "❌ Azure CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        print_status $RED "❌ jq is not installed. Please install it first."
        exit 1
    fi
    
    # Check if config file exists
    if [ ! -f "$CONFIG_FILE" ]; then
        print_status $RED "❌ Configuration file not found: $CONFIG_FILE"
        exit 1
    fi
    
    print_status $GREEN "✅ Prerequisites check passed"
}

# Function to get user input for Function App details
get_function_app_details() {
    print_status $BLUE "📝 Function App Configuration"
    print_status $BLUE "============================="
    
    # Get Function App name
    read -p "Enter Function App name: " FUNCTION_APP_NAME
    if [ -z "$FUNCTION_APP_NAME" ]; then
        print_status $RED "❌ Function App name cannot be empty"
        exit 1
    fi
    
    # Get Resource Group
    read -p "Enter Resource Group name: " RESOURCE_GROUP
    if [ -z "$RESOURCE_GROUP" ]; then
        print_status $RED "❌ Resource Group name cannot be empty"
        exit 1
    fi
    
    # Get Subscription (optional)
    read -p "Enter Subscription ID (or press Enter to use default): " SUBSCRIPTION_ID
    if [ -n "$SUBSCRIPTION_ID" ]; then
        print_status $BLUE "Setting subscription to: $SUBSCRIPTION_ID"
        az account set --subscription "$SUBSCRIPTION_ID"
    fi
    
    print_status $GREEN "✅ Function App details captured"
}

# Function to verify Function App exists
verify_function_app() {
    print_status $BLUE "🔍 Verifying Function App..."
    
    # Check if Function App exists
    if ! az functionapp show --name "$FUNCTION_APP_NAME" --resource-group "$RESOURCE_GROUP" &> /dev/null; then
        print_status $RED "❌ Function App '$FUNCTION_APP_NAME' not found in resource group '$RESOURCE_GROUP'"
        exit 1
    fi
    
    # Check if staging slot exists
    if ! az webapp deployment slot list --name "$FUNCTION_APP_NAME" --resource-group "$RESOURCE_GROUP" --query "[?name=='stage']" --output tsv | grep -q "stage"; then
        print_status $YELLOW "⚠️  Staging slot not found. Creating it now..."
        az webapp deployment slot create \
            --name "$FUNCTION_APP_NAME" \
            --resource-group "$RESOURCE_GROUP" \
            --slot stage \
            --configuration-source "$FUNCTION_APP_NAME"
        print_status $GREEN "✅ Staging slot created successfully"
    else
        print_status $GREEN "✅ Staging slot already exists"
    fi
    
    print_status $GREEN "✅ Function App verification completed"
}

# Function to apply configuration settings
apply_configuration() {
    print_status $BLUE "⚙️  Applying configuration settings..."
    
    # Read the configuration file
    if [ ! -f "$CONFIG_FILE" ]; then
        print_status $RED "❌ Configuration file not found: $CONFIG_FILE"
        exit 1
    fi
    
    # Extract settings from JSON and apply them
    print_status $BLUE "📋 Reading configuration from: $CONFIG_FILE"
    
    # Get all settings as a JSON array
    settings_json=$(cat "$CONFIG_FILE")
    
    # Extract settings and apply them
    echo "$settings_json" | jq -r '.[] | "\(.name)=\(.value)"' | while IFS='=' read -r name value; do
        if [ -n "$name" ] && [ -n "$value" ]; then
            print_status $BLUE "Setting: $name"
            
            # Apply the setting to the staging slot
            az webapp config appsettings set \
                --resource-group "$RESOURCE_GROUP" \
                --name "$FUNCTION_APP_NAME" \
                --slot stage \
                --settings "$name=$value" \
                --output none
            
            if [ $? -eq 0 ]; then
                print_status $GREEN "  ✅ $name set successfully"
            else
                print_status $RED "  ❌ Failed to set $name"
            fi
        fi
    done
    
    print_status $GREEN "✅ Configuration settings applied"
}

# Function to verify configuration
verify_configuration() {
    print_status $BLUE "🔍 Verifying configuration..."
    
    # Get current settings from the staging slot
    print_status $BLUE "Retrieving current settings from staging slot..."
    current_settings=$(az webapp config appsettings list \
        --resource-group "$RESOURCE_GROUP" \
        --name "$FUNCTION_APP_NAME" \
        --slot stage \
        --output json)
    
    # Check critical settings
    critical_settings=(
        "FUNCTIONS_WORKER_RUNTIME"
        "FUNCTIONS_EXTENSION_VERSION"
        "WEBSITE_RUN_FROM_PACKAGE"
        "PYTHON_VERSION"
    )
    
    print_status $BLUE "Verifying critical settings..."
    for setting in "${critical_settings[@]}"; do
        value=$(echo "$current_settings" | jq -r ".[] | select(.name == \"$setting\") | .value // \"NOT_SET\"")
        if [ "$value" != "NOT_SET" ] && [ -n "$value" ]; then
            print_status $GREEN "  ✅ $setting: $value"
        else
            print_status $RED "  ❌ $setting: NOT SET"
        fi
    done
    
    print_status $GREEN "✅ Configuration verification completed"
}

# Function to restart the staging slot
restart_staging_slot() {
    print_status $BLUE "🔄 Restarting staging slot..."
    
    az webapp restart \
        --resource-group "$RESOURCE_GROUP" \
        --name "$FUNCTION_APP_NAME" \
        --slot stage
    
    print_status $GREEN "✅ Staging slot restart initiated"
    
    # Wait for restart to complete
    print_status $BLUE "⏳ Waiting for restart to complete (30 seconds)..."
    sleep 30
    
    print_status $GREEN "✅ Staging slot restart completed"
}

# Function to display summary
display_summary() {
    print_status $GREEN "🎉 Configuration Summary"
    print_status $GREEN "======================="
    print_status $GREEN "✅ Function App: $FUNCTION_APP_NAME"
    print_status $GREEN "✅ Resource Group: $RESOURCE_GROUP"
    print_status $GREEN "✅ Staging slot configured successfully"
    print_status $GREEN "✅ All application settings applied"
    print_status $GREEN "✅ Staging slot restarted"
    
    print_status $BLUE ""
    print_status $BLUE "🔗 Next Steps:"
    print_status $BLUE "1. Test the staging slot endpoints"
    print_status $BLUE "2. Run your deployment pipeline"
    print_status $BLUE "3. The pipeline will now skip configuration and deploy directly"
    
    print_status $BLUE ""
    print_status $BLUE "📋 To modify settings in the future:"
    print_status $BLUE "1. Update the $CONFIG_FILE file"
    print_status $BLUE "2. Run this script again: ./scripts/configure-staging-slot.sh"
}

# Main execution
main() {
    print_status $BLUE "🚀 Staging Slot Configuration Script"
    print_status $BLUE "===================================="
    print_status $BLUE "This script will configure the staging slot with all necessary settings"
    print_status $BLUE ""
    
    # Check prerequisites
    check_prerequisites
    
    # Get Function App details
    get_function_app_details
    
    # Verify Function App and staging slot
    verify_function_app
    
    # Apply configuration
    apply_configuration
    
    # Verify configuration
    verify_configuration
    
    # Restart staging slot
    restart_staging_slot
    
    # Display summary
    display_summary
}

# Run main function
main "$@"

