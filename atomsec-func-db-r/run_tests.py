#!/usr/bin/env python3
"""
Test runner script for atomsec-func-db-r
Provides various test execution options and reporting
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and return the result"""
    print(f"\n{'='*60}")
    print(f"Running: {description or cmd}")
    print(f"{'='*60}")
    
    result = subprocess.run(cmd, shell=True, capture_output=False)
    return result.returncode == 0


def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="Test runner for atomsec-func-db-r")
    parser.add_argument(
        "--type", 
        choices=["unit", "integration", "all"], 
        default="all",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--coverage", 
        action="store_true",
        help="Run tests with coverage reporting"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--fast",
        action="store_true",
        help="Skip slow tests"
    )
    parser.add_argument(
        "--module",
        help="Run tests for specific module (e.g., shared, api, repositories)"
    )
    parser.add_argument(
        "--file",
        help="Run specific test file"
    )
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="Install test dependencies before running tests"
    )
    
    args = parser.parse_args()
    
    # Change to project directory
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # Install dependencies if requested
    if args.install_deps:
        print("Installing test dependencies...")
        if not run_command("pip install -r requirements-dev.txt", "Installing dev dependencies"):
            print("❌ Failed to install dependencies")
            return 1
    
    # Build pytest command
    pytest_cmd = ["python", "-m", "pytest"]
    
    # Add verbosity
    if args.verbose:
        pytest_cmd.append("-v")
    else:
        pytest_cmd.append("-q")
    
    # Add coverage if requested
    if args.coverage:
        pytest_cmd.extend([
            "--cov=shared",
            "--cov=api", 
            "--cov=repositories",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov",
            "--cov-report=xml:coverage.xml"
        ])
    
    # Add test type selection
    if args.type == "unit":
        pytest_cmd.append("tests/unit")
    elif args.type == "integration":
        pytest_cmd.append("tests/integration")
    elif args.file:
        pytest_cmd.append(args.file)
    elif args.module:
        pytest_cmd.append(f"tests/unit/test_{args.module}")
    else:
        pytest_cmd.append("tests/")
    
    # Add markers for fast execution
    if args.fast:
        pytest_cmd.extend(["-m", "not slow"])
    
    # Add other useful options
    pytest_cmd.extend([
        "--tb=short",
        "--strict-markers",
        "--strict-config"
    ])
    
    # Run the tests
    cmd_str = " ".join(pytest_cmd)
    success = run_command(cmd_str, f"Running {args.type} tests")
    
    if success:
        print("\n🎉 All tests passed!")
        
        if args.coverage:
            print("\n📊 Coverage report generated:")
            print("  - Terminal: See above")
            print("  - HTML: htmlcov/index.html")
            print("  - XML: coverage.xml")
    else:
        print("\n❌ Some tests failed!")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
