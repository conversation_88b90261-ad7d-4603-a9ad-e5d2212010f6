"""
Common Module

This module provides common functions and utilities used across the application.
It helps break circular imports between modules.
"""

import os
import logging

# Configure module-level logger
logger = logging.getLogger(__name__)

# Environment detection
def is_local_dev() -> bool:
    """Check if running in local development environment"""
    # Check for explicit Azure production environment indicators (highest priority)
    if os.environ.get("WEBSITE_SITE_NAME") or os.environ.get("WEBSITE_INSTANCE_ID"):
        logger.info("Azure production environment detected via WEBSITE_SITE_NAME or WEBSITE_INSTANCE_ID")
        return False
    
    # Check for explicit local development flag
    local_dev_flag = os.environ.get("IS_LOCAL_DEV", "").lower()
    if local_dev_flag == "true":
        logger.info("Local development environment detected via IS_LOCAL_DEV flag")
        return True
    elif local_dev_flag == "false":
        logger.info("Production environment detected via IS_LOCAL_DEV=false flag")
        return False
    
    # Check if USE_LOCAL_STORAGE is set
    if os.environ.get("USE_LOCAL_STORAGE") == "true":
        logger.info("Local development environment detected via USE_LOCAL_STORAGE flag")
        return True

    # Check if running on localhost
    hostname = os.environ.get("WEBSITE_HOSTNAME", "")
    if hostname.startswith("localhost") or hostname.startswith("127.0.0.1"):
        logger.info("Local development environment detected via WEBSITE_HOSTNAME")
        return True

    # Check for Azure Functions production indicators
    if os.environ.get("FUNCTIONS_WORKER_RUNTIME") and os.environ.get("FUNCTIONS_WORKER_RUNTIME_VERSION"):
        # Check for Azure-specific environment variables
        azure_indicators = [
            "WEBSITE_SITE_NAME",
            "WEBSITE_INSTANCE_ID",
            "WEBSITE_OWNER_NAME",
            "WEBSITE_RESOURCE_GROUP",
            "REGION_NAME"
        ]
        
        for indicator in azure_indicators:
            if os.environ.get(indicator):
                logger.info(f"Azure production environment detected via {indicator}")
                return False
    
    # Check for local development indicators
    if os.environ.get("FUNCTIONS_WORKER_RUNTIME_VERSION") is None:
        logger.info("Local development environment detected via missing FUNCTIONS_WORKER_RUNTIME_VERSION")
        return True

    # Default to production for safety
    logger.info("Production environment detected (default)")
    return False

def is_test_env() -> bool:
    """Check if running in test environment"""
    return os.environ.get("ATOMSEC_TEST_ENV") == "true" or os.environ.get("PYTEST_RUNNING") == "true"
