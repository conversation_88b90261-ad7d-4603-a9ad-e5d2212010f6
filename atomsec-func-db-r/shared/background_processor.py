"""
Background Processor Module

This module provides background task processing capabilities for the AtomSec database service.
Ported from the dev branch to support the new microservices architecture.

Features:
- Task queuing with priority levels (high, medium, low)
- Task scheduling and retry mechanisms
- Integration with Azure Storage Queues
- Task status tracking and updates
- Idempotency and duplicate prevention
- Exponential backoff for retries

Best practices implemented:
- Clean separation of concerns
- Comprehensive error handling
- Proper logging and monitoring
- Type hints for better code clarity
- Environment-aware configuration
"""

import logging
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional, List
import os
import traceback

# Import shared modules
from shared.azure_services import get_queue_client, is_local_dev
from shared.task_status_service import get_task_status_service
from shared.execution_log_service import get_execution_log_service
from shared.service_bus_client import get_service_bus_client
from shared.mock_service_bus import MockServiceBusClient
from shared.service_communication import get_service_communication_client

# Configure module-level logger
logger = logging.getLogger(__name__)

# Task status constants
TASK_STATUS_PENDING = "pending"
TASK_STATUS_RUNNING = "running"
TASK_STATUS_COMPLETED = "completed"
TASK_STATUS_FAILED = "failed"
TASK_STATUS_RETRY = "retry"
TASK_STATUS_CANCELLED = "cancelled"

# Task priority constants
TASK_PRIORITY_HIGH = "high"
TASK_PRIORITY_MEDIUM = "medium"
TASK_PRIORITY_LOW = "low"

# Task type constants
TASK_TYPE_OVERVIEW = "overview"
TASK_TYPE_HEALTH_CHECK = "health_check"
TASK_TYPE_PROFILES = "profiles"
TASK_TYPE_PROFILES_PERMISSION_SETS = "profiles_permission_sets"
TASK_TYPE_PERMISSION_SETS = "permission_sets"
TASK_TYPE_DATA_EXPORT = "data_export"
TASK_TYPE_REPORT_GENERATION = "report_generation"
TASK_TYPE_SCHEDULED_SCAN = "scheduled_scan"
TASK_TYPE_NOTIFICATION = "notification"
TASK_TYPE_METADATA_EXTRACTION = "metadata_extraction"
TASK_TYPE_SFDC_AUTHENTICATE = "sfdc_authenticate"
TASK_TYPE_PMD_APEX_SECURITY = "pmd_apex_security"
TASK_TYPE_MFA_ENFORCEMENT = "mfa_enforcement"
TASK_TYPE_DEVICE_ACTIVATION = "device_activation"
TASK_TYPE_LOGIN_IP_RANGES = "login_ip_ranges"
TASK_TYPE_LOGIN_HOURS = "login_hours"
TASK_TYPE_SESSION_TIMEOUT = "session_timeout"
TASK_TYPE_API_WHITELISTING = "api_whitelisting"
TASK_TYPE_PASSWORD_POLICY = "password_policy"

# Task type to priority mapping
TASK_TYPE_PRIORITY_MAP = {
    # High priority tasks - critical for security monitoring
    TASK_TYPE_HEALTH_CHECK: TASK_PRIORITY_HIGH,
    TASK_TYPE_NOTIFICATION: TASK_PRIORITY_HIGH,
    TASK_TYPE_METADATA_EXTRACTION: TASK_PRIORITY_HIGH,
    TASK_TYPE_SFDC_AUTHENTICATE: TASK_PRIORITY_HIGH,

    # Medium priority tasks - important but not as time-critical
    TASK_TYPE_PROFILES: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_PMD_APEX_SECURITY: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_OVERVIEW: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_SCHEDULED_SCAN: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_PROFILES_PERMISSION_SETS: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_PERMISSION_SETS: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_MFA_ENFORCEMENT: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_DEVICE_ACTIVATION: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_LOGIN_IP_RANGES: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_LOGIN_HOURS: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_SESSION_TIMEOUT: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_API_WHITELISTING: TASK_PRIORITY_MEDIUM,
    TASK_TYPE_PASSWORD_POLICY: TASK_PRIORITY_MEDIUM,

    # Low priority tasks - can be processed when resources are available
    TASK_TYPE_DATA_EXPORT: TASK_PRIORITY_LOW,
    TASK_TYPE_REPORT_GENERATION: TASK_PRIORITY_LOW
}


class BackgroundProcessor:
    """
    Background task processor for the AtomSec database service
    
    This class handles task queuing, scheduling, and processing coordination
    between the database service and the SFDC service.
    """
    
    def __init__(self):
        """Initialize the background processor"""
        self.task_status_service = get_task_status_service()
        self.execution_log_service = get_execution_log_service()
        
        # Queue configuration
        self.default_queue_name = "task-queue"
        self.priority_queue_map = {
            TASK_PRIORITY_HIGH: "task-queue-high",
            TASK_PRIORITY_MEDIUM: "task-queue-medium",
            TASK_PRIORITY_LOW: "task-queue-low"
        }
        
        # Service communication client for microservice communication
        self.service_client = get_service_communication_client()

        # Service Bus client for microservice communication (fallback)
        if is_local_dev():
            self.service_bus_client = MockServiceBusClient()
            logger.info("Initialized with Mock Service Bus for local development")
        else:
            self.service_bus_client = get_service_bus_client()
            logger.info("Initialized with Azure Service Bus for production")
        
        logger.info("Background processor initialized successfully")

    def _is_task_enabled_for_user_policy(self, task_type: str, user_id: str, org_id: str) -> bool:
        """
        Returns True if the task should be enqueued for this user/org, based on Policy/Rule tables.
        Always returns True for critical task types and system user.
        """
        logger.info(f"[POLICY_CHECK] Checking if task {task_type} is enabled for user {user_id} and org {org_id}")
        
        # Always allow these critical task types
        if task_type in [TASK_TYPE_SFDC_AUTHENTICATE, TASK_TYPE_METADATA_EXTRACTION, TASK_TYPE_PMD_APEX_SECURITY, TASK_TYPE_HEALTH_CHECK]:
            logger.info(f"[POLICY_CHECK] Task {task_type} is a critical task type - allowing")
            return True
        
        # Always allow all tasks for system user
        if user_id == 'system':
            logger.info(f"[POLICY_CHECK] System user detected - allowing task {task_type} for org {org_id}")
            return True

        try:
            from shared.data_access import TableStorageRepository

            # Get repositories
            policy_repo = TableStorageRepository("Policy")
            rule_repo = TableStorageRepository("Rule")

            # Find all policies for this integration
            policies = policy_repo.query_entities(f"IntegrationId eq '{org_id}'")

            for policy in policies:
                policy_id = policy.get("PolicyId")
                if not policy_id:
                    continue

                # Build OData filter string for Rule table
                filter_query = f"PolicyId eq '{policy_id}' and TaskType eq '{task_type}' and Enabled eq 1"
                rules = rule_repo.query_entities(filter_query)

                if rules:
                    logger.info(f"Task {task_type} is enabled for org {org_id} via policy {policy_id}")
                    return True

            logger.info(f"Task {task_type} is not enabled for org {org_id} - no matching policy/rule found")
            return False

        except Exception as e:
            logger.error(f"Error checking if task {task_type} is enabled for user {user_id}, org {org_id}: {e}")
            # Default to True on error to avoid blocking tasks
            return True

    def enqueue_task(
        self,
        task_type: str,
        org_id: str,
        user_id: str,
        params: Optional[Dict[str, Any]] = None,
        priority: Optional[str] = None,
        scheduled_time: Optional[datetime] = None,
        force: bool = False,
        execution_log_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Enqueue a task for background processing, with global idempotency.

        Args:
            task_type: Type of task (overview, health_check, profiles, etc.)
            org_id: Organization ID
            user_id: User ID
            params: Additional parameters for the task
            priority: Task priority (high, medium, low). If None, priority will be determined based on task type.
            scheduled_time: Time to schedule the task for execution
            force: If True, bypass idempotency check and always enqueue
            execution_log_id: Explicit execution log ID to use (prevents race conditions)

        Returns:
            str: Task ID if successful, None otherwise
        """
        if task_type != TASK_TYPE_SFDC_AUTHENTICATE:
            logger.warning(f"[ENQUEUE_TASK WARNING] Enqueuing task_type='{task_type}' for org_id='{org_id}', user_id='{user_id}'. This should only happen in the SFDC processor after authentication. Stack trace:\n{traceback.format_stack()}")
        try:
            # Policy check: Only enqueue if enabled for user/org, except for always-on tasks
            if not self._is_task_enabled_for_user_policy(task_type, user_id, org_id):
                logger.warning(f"[ENQUEUE_TASK] REJECTED: Task {task_type} for user {user_id} and org {org_id} is not enabled in Policy/Rule tables.")
                return None
            
            logger.info(f"[ENQUEUE_TASK] APPROVED: Task {task_type} for user {user_id} and org {org_id} passed policy check")

            # Determine priority if not specified
            if priority is None:
                priority = TASK_TYPE_PRIORITY_MAP.get(task_type, TASK_PRIORITY_MEDIUM)
            
            # Validate priority
            if priority not in [TASK_PRIORITY_HIGH, TASK_PRIORITY_MEDIUM, TASK_PRIORITY_LOW]:
                logger.warning(f"Invalid priority '{priority}', using medium")
                priority = TASK_PRIORITY_MEDIUM
            
            # Global idempotency check (skip if force=True for rescan)
            if not force:
                latest_task = self.get_latest_task_for_org(org_id, task_type)
                if latest_task and latest_task.get("Status") in [TASK_STATUS_PENDING, TASK_STATUS_RUNNING]:
                    logger.info(f"[Idempotency] Skipping enqueue for {task_type} ({org_id}): existing task {latest_task.get('TaskId')} is {latest_task.get('Status')}")
                    return latest_task.get("TaskId")
            else:
                logger.info(f"[Force] Bypassing idempotency check for {task_type} ({org_id}) - force=True")
            
            # Generate unique task ID
            task_id = str(uuid.uuid4())
            logger.info(f"Generated task ID: {task_id}")
            
            # Use provided execution_log_id or create a new one if not provided
            if execution_log_id:
                logger.info(f"Using provided execution_log_id: {execution_log_id}")
            else:
                # Create execution log only if not provided
                execution_log_id = self.execution_log_service.create_execution_log(
                    org_id=org_id,
                    execution_type=task_type,
                    user_id=user_id,
                    priority=priority,
                    status="Pending"
                )
                
                if not execution_log_id:
                    logger.error("Failed to create execution log")
                    return None
            
            # Create task in database (for UI display and reference only)
            db_task_id = self.task_status_service.create_task(
                task_type=task_type,
                org_id=org_id,
                user_id=user_id,
                priority=priority,
                execution_log_id=execution_log_id,
                params=params,
                scheduled_time=scheduled_time
            )
            
            if not db_task_id:
                logger.error("Failed to create task in database")
                return None
            
            # Use the database task ID as the primary task ID
            task_id = db_task_id
            
            # Create task data for queue
            task_data = {
                "task_id": task_id,
                "task_type": task_type,
                "org_id": org_id,
                "user_id": user_id,
                "params": params or {},
                "created_at": datetime.now().isoformat(),
                "status": TASK_STATUS_PENDING,
                "priority": priority,
                "retry_count": 0,
                "scheduled_time": scheduled_time.isoformat() if scheduled_time else None,
                "execution_log_id": execution_log_id
            }
            
            # NEW: Send task to Azure Storage Queue instead of Service Bus
            success = self._send_task_to_storage_queue(task_data, priority, scheduled_time)
            
            if success:
                logger.info(f"Successfully enqueued task {task_id} with priority {priority} to Azure Storage Queue")
                return task_id
            else:
                logger.error(f"Failed to enqueue task {task_id} to Azure Storage Queue")
                # Update task status to failed
                self.task_status_service.update_task_status(
                    task_id=task_id,
                    status=TASK_STATUS_FAILED,
                    message="Failed to enqueue task to queue"
                )
                return None
                
        except Exception as e:
            logger.error(f"Error enqueuing task: {str(e)}")
            return None

    def _is_duplicate_task(self, task_type: str, org_id: str, user_id: str) -> bool:
        """
        Check if a similar task is already pending or running

        Args:
            task_type: Type of task
            org_id: Organization ID
            user_id: User ID

        Returns:
            bool: True if duplicate task exists, False otherwise
        """
        try:
            # Get recent tasks for the organization
            recent_tasks = self.task_status_service.get_tasks_by_org(
                org_id=org_id,
                status_filter=None,
                limit=10,
                include_completed=False
            )

            # Check for duplicate tasks of the same type and user
            for task in recent_tasks:
                if (task.get("TaskType") == task_type and
                    task.get("UserId") == user_id and
                    task.get("Status") in [TASK_STATUS_PENDING, TASK_STATUS_RUNNING]):
                    logger.info(f"Found duplicate task: {task.get('TaskId')}")
                    return True

            return False

        except Exception as e:
            logger.error(f"Error checking for duplicate tasks: {str(e)}")
            return False

    def _send_task_to_storage_queue(self, task_data: Dict[str, Any], priority: str, scheduled_time: Optional[datetime] = None) -> bool:
        """
        Send task to Azure Storage Queue for processing
        
        Args:
            task_data: Task data to send
            priority: Task priority (high, medium, low)
            scheduled_time: Scheduled time for task execution
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            from shared.queue_manager import get_queue_manager
            
            # Get queue manager
            queue_manager = get_queue_manager()
            if not queue_manager:
                logger.error("Failed to get queue manager")
                return False
            
            # Determine queue name based on priority
            queue_name_map = {
                TASK_PRIORITY_HIGH: "task-queue-high",
                TASK_PRIORITY_MEDIUM: "task-queue-medium",
                TASK_PRIORITY_LOW: "task-queue-low"
            }
            
            queue_name = queue_name_map.get(priority, "task-queue-medium")
            
            # Calculate visibility timeout for scheduled tasks
            visibility_timeout = None
            if scheduled_time and scheduled_time > datetime.now():
                delay_seconds = int((scheduled_time - datetime.now()).total_seconds())
                visibility_timeout = delay_seconds
                logger.info(f"Task {task_data.get('task_id')} scheduled for {scheduled_time} (visibility timeout: {visibility_timeout}s)")
            
            # Send message to queue
            success = queue_manager.send_message(
                queue_name=queue_name,
                message_data=task_data,
                visibility_timeout=visibility_timeout
            )
            
            if success:
                logger.info(f"Task {task_data.get('task_id')} sent to {queue_name} queue")
                return True
            else:
                logger.error(f"Failed to send task {task_data.get('task_id')} to {queue_name} queue")
                return False
                
        except Exception as e:
            logger.error(f"Error sending task to storage queue: {str(e)}")
            import traceback
            logger.error(f"Queue send error traceback: {traceback.format_exc()}")
            return False

    def retry_task(
        self,
        task_id: str,
        max_retries: int = 3,
        delay_seconds: Optional[int] = None
    ) -> bool:
        """
        Retry a failed task

        Args:
            task_id: ID of the task to retry
            max_retries: Maximum number of retries allowed
            delay_seconds: Delay before retry (if None, uses exponential backoff)

        Returns:
            bool: True if retry was initiated, False otherwise
        """
        try:
            # Get current task
            task = self.task_status_service.get_task(task_id)
            if not task:
                logger.warning(f"Task not found for retry: {task_id}")
                return False

            # Check retry count
            current_retry_count = task.get("RetryCount", 0)
            if current_retry_count >= max_retries:
                logger.warning(f"Task {task_id} has exceeded maximum retries ({max_retries})")
                return False

            # Calculate delay
            if delay_seconds is None:
                delay_seconds = min(30, 2 ** current_retry_count)  # Exponential backoff, max 30 seconds

            # Update task for retry
            new_retry_count = current_retry_count + 1
            retry_message = f"Retry attempt {new_retry_count}/{max_retries}"

            success = self.task_status_service.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RETRY,
                progress=0,
                message=retry_message
            )

            if success:
                # Create task data for re-enqueueing
                task_data = {
                    "task_id": task_id,
                    "task_type": task.get("TaskType"),
                    "org_id": task.get("OrgId"),
                    "user_id": task.get("UserId"),
                    "params": json.loads(task.get("Params", "{}")),
                    "created_at": datetime.now().isoformat(),
                    "status": TASK_STATUS_PENDING,
                    "priority": task.get("Priority", TASK_PRIORITY_MEDIUM),
                    "retry_count": new_retry_count,
                    "execution_log_id": task.get("ExecutionLogId")
                }

                # Send to SFDC service with delay
                # Note: In a real implementation, you'd use a delayed queue or scheduler
                success = self._send_task_to_sfdc_service(task_data, task.get("Priority", TASK_PRIORITY_MEDIUM))

                if success:
                    logger.info(f"Task {task_id} queued for retry (attempt {new_retry_count})")
                    return True
                else:
                    logger.error(f"Failed to queue task {task_id} for retry")
                    return False
            else:
                logger.error(f"Failed to update task status for retry: {task_id}")
                return False

        except Exception as e:
            logger.error(f"Error retrying task: {str(e)}")
            return False

    def cancel_task(self, task_id: str, reason: str = "Cancelled by user") -> bool:
        """
        Cancel a task

        Args:
            task_id: ID of the task to cancel
            reason: Reason for cancellation

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Update task status to cancelled
            success = self.task_status_service.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_CANCELLED,
                message=reason
            )

            if success:
                logger.info(f"Successfully cancelled task {task_id}: {reason}")
                return True
            else:
                logger.error(f"Failed to cancel task {task_id}")
                return False

        except Exception as e:
            logger.error(f"Error cancelling task {task_id}: {str(e)}")
            return False

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get task status

        Args:
            task_id: ID of the task

        Returns:
            Dict[str, Any]: Task status data if found, None otherwise
        """
        try:
            return self.task_status_service.get_task(task_id)
        except Exception as e:
            logger.error(f"Error getting task status: {str(e)}")
            return None

    def get_tasks_by_org(self, org_id: str, include_completed: bool = False) -> List[Dict[str, Any]]:
        """
        Get all tasks for an org, optionally including completed/failed/cancelled
        """
        try:
            task_status_repo = self.task_status_service.get_task_status_repo()
            if not task_status_repo:
                logger.error("Task status repository not available")
                return []

            # Query for tasks
            filter_query = f"PartitionKey eq 'task_{org_id}'"
            if not include_completed:
                filter_query += f" and Status ne '{TASK_STATUS_COMPLETED}' and Status ne '{TASK_STATUS_FAILED}' and Status ne '{TASK_STATUS_CANCELLED}'"

            entities = task_status_repo.query_entities(filter_query)

            if not entities:
                logger.info(f"No tasks found for organization {org_id}")
                return []

            # Convert entities to dictionaries
            tasks = []
            for entity in entities:
                tasks.append(dict(entity))
            return tasks
        except Exception as e:
            logger.error(f"Error getting tasks by org: {str(e)}")
            return []

    def get_latest_task_for_org(self, org_id: str, task_type: str) -> Optional[Dict[str, Any]]:
        """
        Get the latest task for an organization and task type

        Args:
            org_id: Organization ID
            task_type: Task type

        Returns:
            Dict[str, Any]: Latest task if found, None otherwise
        """
        try:
            # Get recent tasks for the organization and task type
            tasks = self.task_status_service.get_tasks_by_org(
                org_id=org_id,
                status_filter=None,
                limit=10,
                include_completed=True
            )

            # Filter by task type and find the most recent
            matching_tasks = [
                task for task in tasks
                if task.get("TaskType") == task_type
            ]

            if matching_tasks:
                # Sort by creation time (most recent first)
                matching_tasks.sort(
                    key=lambda x: x.get("CreatedAt", ""),
                    reverse=True
                )
                return matching_tasks[0]

            return None

        except Exception as e:
            logger.error(f"Error getting latest task for org {org_id}, task type {task_type}: {str(e)}")
            return None

    def get_pending_tasks(self) -> List[Dict[str, Any]]:
        """
        Get all pending tasks

        Returns:
            List[Dict[str, Any]]: List of pending tasks
        """
        try:
            task_status_repo = self.task_status_service.get_task_status_repo()
            if not task_status_repo:
                logger.error("Task status repository not available")
                return []

            # Query all entities and filter by PartitionKey pattern and status in memory
            # Azure Table Storage doesn't support startswith in OData queries
            all_entities = list(task_status_repo.query_entities())
            
            # Filter entities that start with 'task_' and have pending status
            pending_entities = []
            for entity in all_entities:
                partition_key = entity.get('PartitionKey', '')
                status = entity.get('Status', '')
                if partition_key.startswith('task_') and status == TASK_STATUS_PENDING:
                    pending_entities.append(entity)
            
            logger.info(f"[POLL-DEBUG] Found {len(pending_entities)} pending tasks out of {len(all_entities)} total entities")

            if not pending_entities:
                logger.info("No pending tasks found")
                return []

            # Convert entities to dictionaries
            pending_tasks = []
            for entity in pending_entities:
                pending_tasks.append(dict(entity))
            return pending_tasks
        except Exception as e:
            logger.error(f"Error getting pending tasks: {str(e)}")
            return []


# Global processor instance
_background_processor = None


def get_background_processor() -> BackgroundProcessor:
    """
    Get the global background processor instance

    Returns:
        BackgroundProcessor: The processor instance
    """
    global _background_processor

    if _background_processor is None:
        _background_processor = BackgroundProcessor()
        logger.debug("Created global background processor instance")

    return _background_processor
