# Python Worker Runtime Fix for Azure Functions

## Issue Description

The function app `func-atomsec-dbconnect-dev02` was experiencing the following error:

```
Microsoft.Azure.WebJobs.Script: WorkerConfig for runtime: python not found.
```

This error indicates that the Azure Functions runtime cannot find the Python worker configuration, preventing the function app from processing requests properly.

## Root Causes Identified

### 1. Missing Python Worker Runtime Files
- The `.python_packages` directory was empty or missing
- The `azure-functions` package was not properly installed during the build process
- The deployment package did not include the necessary worker runtime files

### 2. Python Version Mismatch
- Function app was configured for Python 3.11
- Pipeline was deploying Python 3.11
- This version mismatch could cause compatibility issues

### 3. Build Process Issues
- Dependencies were not installed in the correct order
- The `.python_packages` directory was not included in the deployment package
- No verification that worker runtime files were present

## Fixes Applied

### 1. Updated Pipeline Configuration (`pipeline-func-db-dev.yml`)

#### Fixed Dependency Installation Order
```yaml
# CRITICAL: Install Azure Functions Python worker first
echo 'Installing Azure Functions Python worker runtime...'
pip install --cache-dir $(PIP_CACHE_DIR) azure-functions==1.17.0

# Install all other dependencies
pip install --cache-dir $(PIP_CACHE_DIR) -r requirements.txt -t .
```

#### Added Worker Runtime Verification
```yaml
# Verify Azure Functions worker is properly installed
echo 'Verifying Azure Functions worker installation...'
if [ -d ".python_packages" ]; then
  echo "✅ .python_packages directory exists"
  ls -la .python_packages/
else
  echo "❌ .python_packages directory missing - creating it"
  mkdir -p .python_packages
fi

# Ensure the worker runtime files are present
if [ -f ".python_packages/lib/site-packages/azure/functions/__init__.py" ]; then
  echo "✅ Azure Functions worker runtime files found"
else
  echo "❌ Azure Functions worker runtime files missing"
  echo "Installing worker runtime directly to .python_packages..."
  pip install --cache-dir $(PIP_CACHE_DIR) azure-functions==1.17.0 -t .python_packages/lib/site-packages/
fi
```

#### Updated Deployment Package Preparation
```yaml
# CRITICAL: Include Python worker runtime files
if [ -d ".python_packages" ]; then
  echo "Copying .python_packages directory (contains worker runtime)..."
  cp -r .python_packages/ "$DEPLOY_DIR/" 2>/dev/null || echo "Failed to copy .python_packages"
else
  echo "⚠️  Warning: .python_packages directory not found"
fi
```

#### Added Deployment Package Verification
```yaml
# CRITICAL: Verify Python worker runtime files are included
echo "🔍 Verifying Python worker runtime files in deployment package..."
if unzip -l "$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip" | grep -q "\.python_packages"; then
  echo "✅ .python_packages directory found in deployment package"
  
  # Check for specific worker runtime files
  TEMP_DIR=$(mktemp -d)
  unzip -q "$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip" -d "$TEMP_DIR"
  
  if [ -f "$TEMP_DIR/.python_packages/lib/site-packages/azure/functions/__init__.py" ]; then
    echo "✅ Azure Functions worker runtime files verified"
  else
    echo "❌ CRITICAL ERROR: Azure Functions worker runtime files missing!"
    echo "This will cause 'WorkerConfig for runtime: python not found' error"
    rm -rf "$TEMP_DIR"
    exit 1
  fi
  
  rm -rf "$TEMP_DIR"
else
  echo "❌ CRITICAL ERROR: .python_packages directory missing from deployment package!"
  echo "This will cause 'WorkerConfig for runtime: python not found' error"
  exit 1
fi
```

### 2. Updated Requirements.txt
- Moved `azure-functions==1.17.0` to the top of the file
- Ensured it's installed first during the build process

### 3. Fixed Function App Configuration
- Updated Python version from 3.12 to 3.11 to match pipeline deployment
- Verified all function app settings are correct

## Quick Fix Script

Created `deploy_worker_fix.sh` for immediate deployment without running the full pipeline:

```bash
./deploy_worker_fix.sh
```

This script:
1. Installs the Python worker runtime locally
2. Creates a deployment package with the correct files
3. Provides Azure CLI commands for deployment

## Verification Steps

### 1. Check Function App Status
```bash
az functionapp show --name func-atomsec-dbconnect-dev02 --resource-group atomsec-dev-data --query "state" -o tsv
```

### 2. Verify Python Version
```bash
az functionapp config show --name func-atomsec-dbconnect-dev02 --resource-group atomsec-dev-data --query "linuxFxVersion" -o tsv
```

### 3. Check Function App Settings
```bash
az functionapp config appsettings list --name func-atomsec-dbconnect-dev02 --resource-group atomsec-dev-data --query "[?contains(name, 'FUNCTIONS') || contains(name, 'PYTHON')].{name: name, value: value}" -o table
```

### 4. Monitor Logs
```bash
az webapp log tail --name func-atomsec-dbconnect-dev02 --resource-group atomsec-dev-data
```

## Expected Results

After applying these fixes:

1. ✅ The `.python_packages` directory will contain the Azure Functions worker runtime
2. ✅ The deployment package will include all necessary worker runtime files
3. ✅ The function app will use Python 3.11 consistently
4. ✅ The "WorkerConfig for runtime: python not found" error will be resolved
5. ✅ The function app will be able to process requests properly

## Next Steps

1. **Immediate Fix**: Run `./deploy_worker_fix.sh` to deploy the fix
2. **Pipeline Fix**: Run the updated pipeline `pipeline-func-db-dev.yml`
3. **Monitor**: Check function app logs to confirm the error is resolved
4. **Test**: Verify that API endpoints are responding correctly

## Prevention

To prevent this issue in the future:

1. Always verify that `.python_packages` contains worker runtime files before deployment
2. Ensure Python version consistency between pipeline and function app configuration
3. Run the deployment package verification steps in the pipeline
4. Monitor function app logs after each deployment

## Related Documentation

- [Azure Functions Python Worker Runtime](https://docs.microsoft.com/en-us/azure/azure-functions/functions-reference-python)
- [Azure Functions v4 Migration Guide](https://docs.microsoft.com/en-us/azure/azure-functions/migrate-version-4)
- [Python Dependencies in Azure Functions](https://docs.microsoft.com/en-us/azure/azure-functions/functions-reference-python#python-version)
