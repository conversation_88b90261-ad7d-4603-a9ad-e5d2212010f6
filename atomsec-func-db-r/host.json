{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}}, "logLevel": {"default": "Information", "Worker.Rpc": "Warning", "Function": "Information", "Host.Triggers.Queue": "Warning", "Azure.Storage": "Warning", "Azure.Core": "Warning", "Host.Results": "Error", "Microsoft.Azure.WebJobs.Script": "Information"}}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "managedDependency": {"enabled": true}, "functionTimeout": "00:10:00", "concurrency": {"dynamicConcurrencyEnabled": false, "snapshotPersistenceEnabled": false}, "extensions": {"http": {"routePrefix": "api/db", "maxOutstandingRequests": 200, "maxConcurrentRequests": 100, "dynamicThrottlesEnabled": true}, "queues": {"maxPollingInterval": "00:00:30", "visibilityTimeout": "00:05:00", "batchSize": 16, "maxDequeueCount": 1, "newBatchThreshold": 8, "messageEncoding": "none"}, "blobs": {"maxDegreeOfParallelism": 4}}}