{"name": "vg-atomsec-db-dev", "description": "Variable group for AtomSec DB Function App - Development Environment", "variables": {"ENVIRONMENT": {"value": "dev02", "description": "Environment name"}, "AZURE_SUBSCRIPTION": {"value": "sc-atomsec-dev-data", "description": "Azure subscription service connection name"}, "SUBSCRIPTION_ID": {"value": "35518353-3fc5-49c1-91cd-3ab90df8d78d", "description": "Azure subscription ID"}, "RESOURCE_GROUP": {"value": "atomsec-dev-data", "description": "Azure resource group name"}, "FUNCTION_APP_NAME": {"value": "func-atomsec-dbconnect-dev02", "description": "Azure Function App name"}, "FUNCTION_APP_URL": {"value": "https://func-atomsec-dbconnect-dev02.azurewebsites.net", "description": "Function App URL"}, "FUNCTION_APP_URL_SCM": {"value": "https://func-atomsec-dbconnect-dev02.scm.azurewebsites.net", "description": "Function App SCM URL"}, "KEY_VAULT_NAME": {"value": "akv-atomsec-dev", "description": "Azure Key Vault name"}, "KEY_VAULT_URL": {"value": "https://akv-atomsec-dev.vault.azure.net/", "description": "Azure Key Vault URL"}, "IS_LOCAL_DEV": {"value": "false", "description": "Is local development environment"}, "LOG_LEVEL": {"value": "INFO", "description": "Logging level"}, "AZURE_STORAGE_CONNECTION_STRING": {"value": "DefaultEndpointsProtocol=https;AccountName=statomsecdbconnectdev02;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "description": "Azure Storage connection string", "isSecret": true}, "AZURE_TABLE_STORAGE_CONNECTION_STRING": {"value": "DefaultEndpointsProtocol=https;AccountName=statomsecdbconnectdev02;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "description": "Azure Table Storage connection string", "isSecret": true}, "AZURE_SERVICE_BUS_CONNECTION_STRING": {"value": "Endpoint=sb://atomsec.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ubVwUkj4yj5IszQsD1IygjI7ox062MSKs+ASbL/bIU0=", "description": "Azure Service Bus connection string", "isSecret": true}, "AZURE_WEBJOBS_STORAGE": {"value": "DefaultEndpointsProtocol=https;AccountName=statomsecdbconnectdev02;AccountKey=Orhkqb6IlGs3x4SH8Ds910Z658NowfewC1kLKTv2sbMM6uIpfARnwDrdeOCb8Y+aNIJAJmDKfZ+AStfMoUGg==;EndpointSuffix=core.windows.net", "description": "Azure WebJobs Storage connection string (required for Azure Functions)", "isSecret": true}, "FRONTEND_URL": {"value": "https://app-atomsec-dev01.azurewebsites.net", "description": "Frontend application URL"}, "SFDC_SERVICE_URL": {"value": "https://func-atomsec-sfdc-dev02-cydhasanfhdmdgar.eastus-01.azurewebsites.net", "description": "SFDC Function App service URL for service-to-service communication"}, "AZURE_AD_CLIENT_ID": {"value": "@Microsoft.KeyVault(SecretUri=https://$(KEY_VAULT_NAME).vault.azure.net/secrets/azure-ad-client-id/)", "description": "Azure AD Client ID from Key Vault", "isSecret": true}, "AZURE_AD_CLIENT_SECRET": {"value": "@Microsoft.KeyVault(SecretUri=https://$(KEY_VAULT_NAME).vault.azure.net/secrets/azure-ad-client-secret/)", "description": "Azure AD Client Secret from Key Vault", "isSecret": true}, "AZURE_AD_TENANT_ID": {"value": "@Microsoft.KeyVault(SecretUri=https://$(KEY_VAULT_NAME).vault.azure.net/secrets/azure-ad-tenant-id/)", "description": "Azure AD Tenant ID from Key Vault", "isSecret": true}, "AZURE_AD_REDIRECT_URI": {"value": "https://func-atomsec-dbconnect-dev02.azurewebsites.net/.auth/login/aad/callback", "description": "Azure AD Redirect URI for production"}, "AZURE_AD_REDIRECT_URI_STAGE": {"value": "https://func-atomsec-dbconnect-dev02-stage.azurewebsites.net/.auth/login/aad/callback", "description": "Azure AD Redirect URI for staging"}, "DB_SERVICE_BASE_URL": {"value": "https://func-atomsec-dbconnect-dev02.azurewebsites.net", "description": "DB Service Base URL for internal service communication"}, "DB_SERVICE_BASE_URL_STAGE": {"value": "https://func-atomsec-dbconnect-dev02-stage.azurewebsites.net", "description": "DB Service Base URL for staging environment"}, "JWT_SECRET": {"value": "@Microsoft.KeyVault(SecretUri=https://$(KEY_VAULT_NAME).vault.azure.net/secrets/jwt-secret/)", "description": "JWT Secret from Key Vault", "isSecret": true}}}