# Key Vault Setup Guide for AtomSec DB Function App

## Overview

This guide explains how to set up Azure Key Vault secrets and Azure DevOps variable groups to resolve the function app startup issues caused by missing environment variables.

## Current Issues

The function app is failing to start in Azure because of missing environment variables:
- ❌ `DB_SERVICE_BASE_URL` - Required for internal service communication
- ❌ `AZURE_AD_CLIENT_ID` - Required for Azure AD authentication
- ❌ `AZURE_AD_CLIENT_SECRET` - Required for Azure AD authentication  
- ❌ `AZURE_AD_TENANT_ID` - Required for Azure AD authentication
- ❌ `JWT_SECRET` - Required for JWT token validation

## Solution: Key Vault + Variable Groups

We'll use Azure Key Vault to store sensitive values and Azure DevOps variable groups to reference them securely.

## Step 1: Add Secrets to Azure Key Vault

### Option A: Use the Automated Script

1. **Run the setup script:**
   ```bash
   cd atomsec-func-db-r
   ./scripts/setup_keyvault_secrets.sh
   ```

2. **Follow the prompts** to enter your Azure AD values:
   - Azure AD Client ID
   - Azure AD Client Secret  
   - Azure AD Tenant ID
   - JWT Secret (or let it generate one)

### Option B: Manual Key Vault Setup

1. **Navigate to your Key Vault** in Azure Portal:
   - Key Vault: `akv-atomsec-dev`
   - Resource Group: `atomsec-dev-data`

2. **Add these secrets:**
   - `azure-ad-client-id` = Your Azure AD App Registration Client ID
   - `azure-ad-client-secret` = Your Azure AD App Registration Client Secret
   - `azure-ad-tenant-id` = Your Azure AD Tenant ID
   - `jwt-secret` = A secure random string for JWT validation

## Step 2: Update Azure DevOps Variable Group

### Variable Group: `vg-atomsec-db-dev`

1. **Go to Azure DevOps** → Library → Variable Groups
2. **Edit** the `vg-atomsec-db-dev` variable group
3. **Add these variables:**

| Variable Name | Value | Description | Is Secret |
|---------------|-------|-------------|-----------|
| `AZURE_AD_CLIENT_ID` | `@Microsoft.KeyVault(SecretUri=https://akv-atomsec-dev.vault.azure.net/secrets/azure-ad-client-id/)` | Azure AD Client ID from Key Vault | ✅ |
| `AZURE_AD_CLIENT_SECRET` | `@Microsoft.KeyVault(SecretUri=https://akv-atomsec-dev.vault.azure.net/secrets/azure-ad-client-secret/)` | Azure AD Client Secret from Key Vault | ✅ |
| `AZURE_AD_TENANT_ID` | `@Microsoft.KeyVault(SecretUri=https://akv-atomsec-dev.vault.azure.net/secrets/azure-ad-tenant-id/)` | Azure AD Tenant ID from Key Vault | ✅ |
| `AZURE_AD_REDIRECT_URI` | `https://func-atomsec-dbconnect-dev02.azurewebsites.net/.auth/login/aad/callback` | Azure AD Redirect URI for production | ❌ |
| `AZURE_AD_REDIRECT_URI_STAGE` | `https://func-atomsec-dbconnect-dev02-stage.azurewebsites.net/.auth/login/aad/callback` | Azure AD Redirect URI for staging | ❌ |
| `DB_SERVICE_BASE_URL` | `https://func-atomsec-dbconnect-dev02.azurewebsites.net` | DB Service Base URL for production | ❌ |
| `DB_SERVICE_BASE_URL_STAGE` | `https://func-atomsec-dbconnect-dev02-stage.azurewebsites.net` | DB Service Base URL for staging | ❌ |
| `JWT_SECRET` | `@Microsoft.KeyVault(SecretUri=https://akv-atomsec-dev.vault.azure.net/secrets/jwt-secret/)` | JWT Secret from Key Vault | ✅ |

## Step 3: Ensure Function App Access to Key Vault

### Option A: Managed Identity (Recommended)

1. **Enable Managed Identity** on your Function App:
   ```bash
   az functionapp identity assign \
     --name func-atomsec-dbconnect-dev02 \
     --resource-group atomsec-dev-data
   ```

2. **Grant Key Vault access** to the Managed Identity:
   ```bash
   az keyvault set-policy \
     --name akv-atomsec-dev \
     --resource-group atomsec-dev-data \
     --object-id <MANAGED_IDENTITY_OBJECT_ID> \
     --secret-permissions get list
   ```

### Option B: Service Principal

If using a Service Principal, ensure it has `Get` and `List` permissions on the Key Vault secrets.

## Step 4: Update Pipeline Configuration

The pipeline is already configured to use these variables. The variable group `vg-atomsec-db-dev` is referenced on line 58:

```yaml
variables:
- group: vg-atomsec-db-dev  # Variable group for dev environment
```

## Step 5: Test the Configuration

1. **Run the pipeline** to deploy with the new configuration
2. **Check the staging slot** for successful startup
3. **Verify the health endpoint** is accessible:
   ```
   https://func-atomsec-dbconnect-dev02-stage.azurewebsites.net/api/db/health
   ```

## Expected Results

After completing this setup:

✅ **Function app starts successfully** without import errors  
✅ **All endpoints are accessible** (no more 500 errors)  
✅ **Azure AD authentication works** end-to-end  
✅ **Health check passes** with proper status  
✅ **Environment variables are properly loaded** from Key Vault  

## Troubleshooting

### Common Issues

1. **"Secret not found" errors:**
   - Verify secrets exist in Key Vault
   - Check secret names match exactly
   - Ensure Function App has access permissions

2. **"Variable group not found" errors:**
   - Verify variable group name in pipeline
   - Check variable group permissions
   - Ensure variables are properly configured

3. **"Function host not running" errors:**
   - Check Python worker runtime installation
   - Verify app settings are applied
   - Review function app startup logs

### Debug Commands

```bash
# Check Key Vault secrets
az keyvault secret list --vault-name akv-atomsec-dev

# Check Function App app settings
az functionapp config appsettings list \
  --name func-atomsec-dbconnect-dev02 \
  --resource-group atomsec-dev-data

# Check Function App logs
az webapp log tail \
  --name func-atomsec-dbconnect-dev02 \
  --resource-group atomsec-dev-data
```

## Security Notes

- ✅ **Secrets are stored securely** in Azure Key Vault
- ✅ **No hardcoded values** in code or configuration
- ✅ **Access is controlled** via Azure RBAC
- ✅ **Audit logging** is available for all secret access
- ✅ **Rotation policies** can be implemented for secrets

## Next Steps

1. **Complete the Key Vault setup** using the script or manual process
2. **Update the variable group** with the new variables
3. **Run the pipeline** to deploy the updated configuration
4. **Verify the function app starts successfully**
5. **Test the authentication flow** end-to-end

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review Azure Function App logs
3. Verify Key Vault access permissions
4. Ensure all environment variables are properly set



