"""
Unit tests for api.general_endpoints module
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
import azure.functions as func

from api.general_endpoints import get_stats_business_logic, create_backup_business_logic


class TestGeneralEndpoints:
    """Test cases for general endpoints"""

    @pytest.fixture
    def mock_request(self):
        """Mock HTTP request"""
        mock_req = Mock(spec=func.HttpRequest)
        mock_req.method = "GET"
        mock_req.params = {}
        return mock_req

    @pytest.fixture
    def mock_repositories(self):
        """Mock repositories for testing"""
        repos = {}
        for repo_name in ['user_repo', 'account_repo', 'org_repo', 'integration_repo', 'task_repo', 'security_repo', 'policy_repo']:
            mock_repo = Mock()
            mock_repo.query_entities.return_value = [{"id": 1}, {"id": 2}, {"id": 3}]  # 3 items each
            repos[repo_name] = mock_repo
        return repos

    @pytest.mark.unit
    @patch('api.general_endpoints.is_local_dev')
    @patch('api.general_endpoints.get_table_storage_repository')
    def test_get_stats_local_dev(self, mock_get_table_repo, mock_is_local_dev, mock_repositories):
        """Test get_stats business logic in local development"""
        mock_is_local_dev.return_value = True

        # Configure mock to return different repos based on table name
        def get_repo_side_effect(table_name):
            repo_mapping = {
                'users': mock_repositories['user_repo'],
                'Account': mock_repositories['account_repo'],
                'UserAccount': mock_repositories['org_repo'],
                'Integrations': mock_repositories['integration_repo'],
                'TaskStatus': mock_repositories['task_repo'],
                'HealthCheck': mock_repositories['security_repo'],
                'Policy': mock_repositories['policy_repo']
            }
            return repo_mapping.get(table_name)

        mock_get_table_repo.side_effect = get_repo_side_effect

        result = get_stats_business_logic()

        # Verify the result is a dictionary with expected structure
        assert isinstance(result, dict)
        assert result["users"] == 3
        assert result["accounts"] == 3
        assert result["organizations"] == 3
        assert result["integrations"] == 3
        assert result["tasks"] == 3
        assert result["security_checks"] == 3
        assert result["policies"] == 3

    @pytest.mark.unit
    @patch('api.general_endpoints.is_local_dev')
    @patch('api.general_endpoints.get_sql_database_repository')
    def test_get_stats_production(self, mock_get_sql_repo, mock_is_local_dev, mock_repositories):
        """Test get_stats business logic in production"""
        mock_is_local_dev.return_value = False
        
        # Configure mock to return different repos based on table name
        def get_repo_side_effect(table_name):
            repo_mapping = {
                'users': mock_repositories['user_repo'],
                'Account': mock_repositories['account_repo'],
                'UserAccount': mock_repositories['org_repo'],
                'Integrations': mock_repositories['integration_repo'],
                'TaskStatus': mock_repositories['task_repo'],
                'HealthCheck': mock_repositories['security_repo'],
                'Policy': mock_repositories['policy_repo']
            }
            return repo_mapping.get(table_name)
        
        mock_get_sql_repo.side_effect = get_repo_side_effect

        result = get_stats_business_logic()

        assert isinstance(result, dict)
        # Should have some stats even if some repos fail
        assert len(result) > 0
        # Verify expected keys exist
        expected_keys = ["users", "accounts", "organizations", "integrations", "tasks", "security_checks", "policies"]
        for key in expected_keys:
            assert key in result

    @pytest.mark.unit
    @patch('api.general_endpoints.is_local_dev')
    @patch('api.general_endpoints.get_table_storage_repository')
    def test_get_stats_with_repo_errors(self, mock_get_table_repo, mock_is_local_dev):
        """Test get_stats business logic when some repositories fail"""
        mock_is_local_dev.return_value = True

        def get_repo_side_effect(table_name):
            if table_name == 'users':
                mock_repo = Mock()
                mock_repo.query_entities.return_value = [{"id": 1}, {"id": 2}]
                return mock_repo
            elif table_name == 'Account':
                mock_repo = Mock()
                mock_repo.query_entities.side_effect = Exception("Database error")
                return mock_repo
            else:
                return None  # Repository not available

        mock_get_table_repo.side_effect = get_repo_side_effect

        result = get_stats_business_logic()

        assert isinstance(result, dict)
        assert result["users"] == 2  # Should work
        # Other stats should be 0 due to errors/unavailable repos
        assert result["accounts"] == 0  # Failed due to exception
        assert result["organizations"] == 0  # Not available

    @pytest.mark.unit
    def test_get_stats_exception(self):
        """Test get_stats business logic with general exception"""
        with patch('api.general_endpoints.is_local_dev') as mock_is_local_dev:
            mock_is_local_dev.side_effect = Exception("Configuration error")

            # Should raise exception since is_local_dev fails
            with pytest.raises(Exception, match="Configuration error"):
                get_stats_business_logic()

    @pytest.mark.unit
    def test_create_backup_success(self):
        """Test create_backup business logic success"""
        request_data = {
            "type": "full",
            "include_data": True
        }

        with patch('shared.service_bus_client.get_service_bus_client') as mock_get_sb_client:
            mock_sb_client = Mock()
            mock_get_sb_client.return_value = mock_sb_client

            result = create_backup_business_logic(request_data, "test-user")

            assert result["success"] is True
            assert result["status_code"] == 202
            assert result["message"] == "Backup creation initiated"

            backup_data = result["data"]
            assert backup_data["backup_type"] == "full"
            assert backup_data["include_data"] is True
            assert backup_data["created_by"] == "test-user"
            assert backup_data["status"] == "initiated"
            assert "backup_id" in backup_data

    @pytest.mark.unit
    def test_create_backup_invalid_type(self):
        """Test create_backup business logic with invalid backup type"""
        request_data = {
            "type": "invalid_type",
            "include_data": True
        }

        result = create_backup_business_logic(request_data, "test-user")

        assert result["success"] is False
        assert result["status_code"] == 400
        assert "Invalid backup type" in result["error"]

    @pytest.mark.unit
    def test_create_backup_empty_request(self):
        """Test create_backup business logic with empty request data"""
        request_data = {}  # Empty request data

        result = create_backup_business_logic(request_data, "test-user")

        # Should succeed with default values
        assert result["success"] is True
        assert result["status_code"] == 202

        backup_data = result["data"]
        assert backup_data["backup_type"] == "full"  # Default value
        assert backup_data["include_data"] is True  # Default value
        assert backup_data["created_by"] == "test-user"

    @pytest.mark.unit
    @patch('api.general_endpoints.create_default_policies_and_rules_for_integration')
    def test_create_default_policies_integration(self, mock_create_policies, mock_request):
        """Test integration with policy creation function"""
        mock_create_policies.return_value = True

        # This would be called from another endpoint, but we test the integration
        result = mock_create_policies("test-integration-id")

        assert result is True
        mock_create_policies.assert_called_once_with("test-integration-id")

    @pytest.mark.unit
    @patch('api.general_endpoints.is_local_dev')
    @patch('api.general_endpoints.get_table_storage_repository')
    def test_response_headers(self, mock_get_table_repo, mock_is_local_dev):
        """Test that business logic returns correct data structure"""
        mock_is_local_dev.return_value = True
        mock_repo = Mock()
        mock_repo.query_entities.return_value = []
        mock_get_table_repo.return_value = mock_repo

        result = get_stats_business_logic()

        assert isinstance(result, dict)
        # Verify all expected keys are present
        expected_keys = ["users", "accounts", "organizations", "integrations", "tasks", "security_checks", "policies"]
        for key in expected_keys:
            assert key in result



    @pytest.mark.unit
    @patch('api.general_endpoints.is_local_dev')
    @patch('api.general_endpoints.get_table_storage_repository')
    def test_stats_zero_counts(self, mock_get_repo, mock_is_local_dev):
        """Test get_stats business logic when all repositories return empty results"""
        mock_is_local_dev.return_value = True

        mock_repo = Mock()
        mock_repo.query_entities.return_value = []  # Empty results
        mock_get_repo.return_value = mock_repo

        result = get_stats_business_logic()

        assert isinstance(result, dict)
        # All counts should be 0
        for key, value in result.items():
            assert value == 0
