"""
Unit tests for repositories.base_repository module
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from repositories.base_repository import BaseRepository


class TestBaseRepository:
    """Test cases for BaseRepository"""

    @pytest.fixture
    def mock_db_client(self):
        """Mock DB service client"""
        return Mock()

    @pytest.fixture
    @patch('repositories.base_repository.get_db_client')
    def base_repo(self, mock_get_db_client, mock_db_client):
        """Create BaseRepository instance for testing"""
        mock_get_db_client.return_value = mock_db_client
        return BaseRepository()

    @pytest.mark.unit
    def test_init_success(self, base_repo, mock_db_client):
        """Test BaseRepository initialization with valid DB client"""
        assert base_repo.db_client == mock_db_client

    @pytest.mark.unit
    @patch('repositories.base_repository.get_db_client')
    def test_init_no_db_client(self, mock_get_db_client):
        """Test BaseRepository initialization when DB client is not available"""
        mock_get_db_client.return_value = None
        
        repo = BaseRepository()
        
        assert repo.db_client is None

    @pytest.mark.unit
    def test_execute_query_success(self, base_repo, mock_db_client):
        """Test successful query execution"""
        expected_result = [{"id": 1, "name": "test"}]
        mock_db_client.execute_query.return_value = expected_result
        
        result = base_repo.execute_query("SELECT * FROM test", {"param": "value"})
        
        assert result == expected_result
        mock_db_client.execute_query.assert_called_once_with("SELECT * FROM test", {"param": "value"})

    @pytest.mark.unit
    def test_execute_query_no_params(self, base_repo, mock_db_client):
        """Test query execution without parameters"""
        expected_result = [{"id": 1, "name": "test"}]
        mock_db_client.execute_query.return_value = expected_result
        
        result = base_repo.execute_query("SELECT * FROM test")
        
        assert result == expected_result
        mock_db_client.execute_query.assert_called_once_with("SELECT * FROM test", {})

    @pytest.mark.unit
    def test_execute_query_no_db_client(self, mock_db_client):
        """Test query execution when DB client is not available"""
        with patch('repositories.base_repository.get_db_client', return_value=None):
            repo = BaseRepository()
            
            result = repo.execute_query("SELECT * FROM test")
            
            assert result == []

    @pytest.mark.unit
    def test_execute_query_exception(self, base_repo, mock_db_client):
        """Test query execution with exception"""
        mock_db_client.execute_query.side_effect = Exception("Database error")
        
        result = base_repo.execute_query("SELECT * FROM test")
        
        assert result == []

    @pytest.mark.unit
    def test_execute_non_query_success(self, base_repo, mock_db_client):
        """Test successful non-query execution"""
        mock_db_client.execute_non_query.return_value = True
        
        result = base_repo.execute_non_query("INSERT INTO test VALUES (?)", {"value": "test"})
        
        assert result is True
        mock_db_client.execute_non_query.assert_called_once_with("INSERT INTO test VALUES (?)", {"value": "test"})

    @pytest.mark.unit
    def test_execute_non_query_no_db_client(self, mock_db_client):
        """Test non-query execution when DB client is not available"""
        with patch('repositories.base_repository.get_db_client', return_value=None):
            repo = BaseRepository()
            
            result = repo.execute_non_query("INSERT INTO test VALUES (?)")
            
            assert result is False

    @pytest.mark.unit
    def test_execute_non_query_exception(self, base_repo, mock_db_client):
        """Test non-query execution with exception"""
        mock_db_client.execute_non_query.side_effect = Exception("Database error")
        
        result = base_repo.execute_non_query("INSERT INTO test VALUES (?)")
        
        assert result is False

    @pytest.mark.unit
    def test_format_datetime_with_datetime(self, base_repo):
        """Test datetime formatting with datetime object"""
        test_datetime = datetime(2024, 1, 1, 12, 0, 0)
        
        result = base_repo.format_datetime(test_datetime)
        
        assert result == "2024-01-01T12:00:00"

    @pytest.mark.unit
    def test_format_datetime_with_string(self, base_repo):
        """Test datetime formatting with string input"""
        test_string = "2024-01-01T12:00:00"
        
        result = base_repo.format_datetime(test_string)
        
        assert result == "2024-01-01T12:00:00"

    @pytest.mark.unit
    def test_format_datetime_with_none(self, base_repo):
        """Test datetime formatting with None input"""
        result = base_repo.format_datetime(None)

        # Should return current datetime ISO format
        assert isinstance(result, str)
        assert "T" in result

    @pytest.mark.unit
    def test_format_datetime_with_invalid_string(self, base_repo):
        """Test datetime formatting with invalid string"""
        result = base_repo.format_datetime("invalid-date")

        # The actual implementation returns the string as-is for invalid input
        assert isinstance(result, str)
        assert result == "invalid-date"

    @pytest.mark.unit
    def test_sanitize_string_normal(self, base_repo):
        """Test string sanitization with normal string"""
        result = base_repo.sanitize_string("normal string")
        
        assert result == "normal string"

    @pytest.mark.unit
    def test_sanitize_string_with_quotes(self, base_repo):
        """Test string sanitization with single quotes"""
        result = base_repo.sanitize_string("string with 'quotes'")
        
        assert result == "string with ''quotes''"

    @pytest.mark.unit
    def test_sanitize_string_with_semicolon(self, base_repo):
        """Test string sanitization with semicolon"""
        result = base_repo.sanitize_string("string; with semicolon")
        
        assert result == "string with semicolon"

    @pytest.mark.unit
    def test_sanitize_string_empty(self, base_repo):
        """Test string sanitization with empty string"""
        result = base_repo.sanitize_string("")
        
        assert result == ""

    @pytest.mark.unit
    def test_sanitize_string_none(self, base_repo):
        """Test string sanitization with None"""
        result = base_repo.sanitize_string(None)
        
        assert result == ""

    @pytest.mark.unit
    def test_test_connection_success(self, base_repo, mock_db_client):
        """Test successful database connection test"""
        mock_db_client.execute_scalar.return_value = 1

        result = base_repo.test_connection()

        assert result is True
        mock_db_client.execute_scalar.assert_called_once()

    @pytest.mark.unit
    def test_test_connection_failure(self, base_repo, mock_db_client):
        """Test database connection test failure"""
        mock_db_client.execute_scalar.side_effect = Exception("Connection error")

        result = base_repo.test_connection()

        assert result is False

    @pytest.mark.unit
    def test_table_exists_true(self, base_repo, mock_db_client):
        """Test table existence check when table exists"""
        mock_db_client.execute_scalar.return_value = 1

        result = base_repo.table_exists("test_table")

        assert result is True
        mock_db_client.execute_scalar.assert_called_once()

    @pytest.mark.unit
    def test_table_exists_false(self, base_repo, mock_db_client):
        """Test table existence check when table doesn't exist"""
        mock_db_client.execute_scalar.return_value = 0

        result = base_repo.table_exists("test_table")

        assert result is False
