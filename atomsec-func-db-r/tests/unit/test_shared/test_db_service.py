"""
Unit tests for shared.db_service module
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from shared.db_service import DBService
from shared.database_models_new import ExecutionLog


class TestDBService:
    """Test cases for DBService"""

    @pytest.fixture
    def mock_table_repo(self):
        """Mock table storage repository"""
        return Mock()

    @pytest.fixture
    def mock_sql_repo(self):
        """Mock SQL database repository"""
        return Mock()

    @pytest.fixture
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.db_service.get_table_storage_repository')
    def db_service_local(self, mock_get_table_repo, mock_is_local_dev, mock_table_repo):
        """Create DBService instance for local development"""
        mock_is_local_dev.return_value = True
        mock_get_table_repo.return_value = mock_table_repo
        return DBService()

    @pytest.fixture
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.db_service.get_sql_database_repository')
    def db_service_prod(self, mock_get_sql_repo, mock_is_local_dev, mock_sql_repo):
        """Create DBService instance for production"""
        mock_is_local_dev.return_value = False
        mock_get_sql_repo.return_value = mock_sql_repo
        return DBService()

    @pytest.mark.unit
    def test_init_local_dev(self, db_service_local):
        """Test DBService initialization in local development"""
        assert db_service_local.table_name == "ExecutionLog"
        assert db_service_local.repo is not None

    @pytest.mark.unit
    def test_init_production(self, db_service_prod):
        """Test DBService initialization in production"""
        assert db_service_prod.table_name == "ExecutionLog"
        assert db_service_prod.repo is not None

    @pytest.mark.unit
    def test_create_execution_log_local(self, db_service_local, sample_execution_log_data):
        """Test creating execution log in local development"""
        execution_log = ExecutionLog.from_dict(sample_execution_log_data)
        db_service_local.repo.insert_entity.return_value = True
        
        result = db_service_local.create_execution_log(execution_log)
        
        assert result is True
        db_service_local.repo.insert_entity.assert_called_once()
        
        # Verify the entity structure
        call_args = db_service_local.repo.insert_entity.call_args[0][0]
        assert call_args['PartitionKey'] == execution_log.OrgId
        assert call_args['RowKey'] == execution_log.ExecutionLogId
        assert 'StartedAt' in call_args

    @pytest.mark.unit
    @patch('shared.db_service.is_local_dev')
    def test_create_execution_log_production(self, mock_is_local_dev, db_service_prod, sample_execution_log_data):
        """Test creating execution log in production"""
        mock_is_local_dev.return_value = False
        execution_log = ExecutionLog.from_dict(sample_execution_log_data)
        db_service_prod.repo.execute_non_query.return_value = True

        result = db_service_prod.create_execution_log(execution_log)

        assert result is True
        db_service_prod.repo.execute_non_query.assert_called_once()

        # Verify SQL query structure
        call_args = db_service_prod.repo.execute_non_query.call_args
        sql_query = call_args[0][0]
        assert "INSERT INTO ExecutionLog" in sql_query
        assert "VALUES" in sql_query

    @pytest.mark.unit
    def test_update_execution_log_local(self, db_service_local):
        """Test updating execution log in local development"""
        # Mock finding the entity
        mock_entity = {
            'PartitionKey': 'test-org-id',
            'RowKey': 'test-execution-log-id',
            'Status': 'PENDING'
        }
        db_service_local.repo.query_entities.return_value = [mock_entity]
        db_service_local.repo.update_entity.return_value = True
        
        update_data = {'Status': 'COMPLETED', 'CompletedAt': datetime.now()}
        result = db_service_local.update_execution_log('test-execution-log-id', update_data)
        
        assert result is True
        db_service_local.repo.query_entities.assert_called_once()
        db_service_local.repo.update_entity.assert_called_once()

    @pytest.mark.unit
    @patch('shared.db_service.is_local_dev')
    def test_update_execution_log_production(self, mock_is_local_dev, db_service_prod):
        """Test updating execution log in production"""
        mock_is_local_dev.return_value = False
        # Reset the mock to clear any previous calls
        db_service_prod.repo.execute_non_query.reset_mock()
        db_service_prod.repo.execute_non_query.return_value = True

        update_data = {'Status': 'COMPLETED', 'CompletedAt': datetime.now()}
        result = db_service_prod.update_execution_log('test-execution-log-id', update_data)

        assert result is True
        db_service_prod.repo.execute_non_query.assert_called_once()

        # Verify SQL query structure
        call_args = db_service_prod.repo.execute_non_query.call_args
        sql_query = call_args[0][0]
        assert "UPDATE ExecutionLog SET" in sql_query
        assert "WHERE ExecutionLogId = ?" in sql_query

    @pytest.mark.unit
    def test_get_execution_log_by_id_local(self, db_service_local, sample_execution_log_data):
        """Test getting execution log by ID in local development"""
        mock_entity = sample_execution_log_data.copy()
        db_service_local.repo.query_entities.return_value = [mock_entity]
        
        result = db_service_local.get_execution_log_by_id('test-execution-log-id')
        
        assert result is not None
        assert isinstance(result, ExecutionLog)
        assert result.ExecutionLogId == 'test-execution-log-id'
        db_service_local.repo.query_entities.assert_called_once_with("RowKey eq 'test-execution-log-id'")

    @pytest.mark.unit
    @patch('shared.db_service.is_local_dev')
    def test_get_execution_log_by_id_production(self, mock_is_local_dev, db_service_prod, sample_execution_log_data):
        """Test getting execution log by ID in production"""
        mock_is_local_dev.return_value = False
        # Mock the column query and data query
        db_service_prod.repo.execute_query.side_effect = [
            [('ExecutionLogId',), ('OrgId',), ('UserId',), ('TaskType',), ('Status',)],  # Column names
            [tuple(sample_execution_log_data.values())]  # Data row
        ]

        result = db_service_prod.get_execution_log_by_id('test-execution-log-id')

        assert result is not None
        assert isinstance(result, ExecutionLog)
        # Two calls: one for columns, one for data
        assert db_service_prod.repo.execute_query.call_count == 2

    @pytest.mark.unit
    def test_get_execution_log_by_id_not_found_local(self, db_service_local):
        """Test getting execution log by ID when not found in local development"""
        db_service_local.repo.query_entities.return_value = []
        
        result = db_service_local.get_execution_log_by_id('non-existent-id')
        
        assert result is None

    @pytest.mark.unit
    def test_get_execution_log_by_id_not_found_production(self, db_service_prod):
        """Test getting execution log by ID when not found in production"""
        db_service_prod.repo.execute_query.return_value = []
        
        result = db_service_prod.get_execution_log_by_id('non-existent-id')
        
        assert result is None

    @pytest.mark.unit
    def test_update_execution_log_entity_not_found_local(self, db_service_local):
        """Test updating execution log when entity not found in local development"""
        db_service_local.repo.query_entities.return_value = []
        
        update_data = {'Status': 'COMPLETED'}
        result = db_service_local.update_execution_log('non-existent-id', update_data)
        
        assert result is False
        db_service_local.repo.update_entity.assert_not_called()

    @pytest.mark.unit
    def test_datetime_handling_local(self, db_service_local, sample_execution_log_data):
        """Test datetime handling in local development"""
        execution_log = ExecutionLog.from_dict(sample_execution_log_data)
        execution_log.StartedAt = datetime.now()
        execution_log.CompletedAt = datetime.now()
        
        db_service_local.repo.insert_entity.return_value = True
        
        result = db_service_local.create_execution_log(execution_log)
        
        assert result is True
        
        # Verify datetime conversion to ISO format
        call_args = db_service_local.repo.insert_entity.call_args[0][0]
        assert isinstance(call_args['StartedAt'], str)
        assert isinstance(call_args['CompletedAt'], str)
