"""
Unit tests for shared.data_access module
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
import json

from shared.data_access import (
    TableStorageRepository,
    SqlDatabaseRepository,
    get_table_storage_repository,
    get_sql_database_repository
)


class TestTableStorageRepository:
    """Test cases for TableStorageRepository"""

    @pytest.fixture
    @patch('shared.data_access.get_table_client')
    def table_repo(self, mock_get_table_client, mock_table_client):
        """Create a TableStorageRepository instance for testing"""
        # Mock table service
        mock_table_service = Mock()
        mock_get_table_client.return_value = mock_table_service

        # Make table service methods return iterables and the mock table client
        mock_table_service.list_tables.return_value = []
        mock_table_service.get_table_client.return_value = mock_table_client
        mock_table_service.create_table_if_not_exists.return_value = mock_table_client

        # Make table client methods return iterables
        mock_table_client.query_entities.return_value = []

        return TableStorageRepository("test_table")

    @pytest.mark.unit
    def test_init(self, table_repo):
        """Test TableStorageRepository initialization"""
        assert table_repo.table_name == "test_table"
        assert table_repo.table_client is not None

    @pytest.mark.unit
    def test_insert_entity_success(self, table_repo, mock_table_client):
        """Test successful entity insertion"""
        entity = {"PartitionKey": "test_pk", "RowKey": "test_rk", "data": "test"}
        
        result = table_repo.insert_entity(entity)
        
        assert result is True
        mock_table_client.upsert_entity.assert_called_once_with(entity)

    @pytest.mark.unit
    def test_insert_entity_failure(self, table_repo, mock_table_client):
        """Test entity insertion failure"""
        entity = {"PartitionKey": "test_pk", "RowKey": "test_rk"}
        mock_table_client.upsert_entity.side_effect = Exception("Insert failed")
        
        result = table_repo.insert_entity(entity)
        
        assert result is False

    @pytest.mark.unit
    def test_get_entity_success(self, table_repo, mock_table_client):
        """Test successful entity retrieval"""
        expected_entity = {"PartitionKey": "test_pk", "RowKey": "test_rk", "data": "test"}

        # Mock query_entities to return the entity (get_entity uses query_entities internally)
        mock_table_client.query_entities.return_value = [expected_entity]

        result = table_repo.get_entity("test_pk", "test_rk")

        assert result == expected_entity
        mock_table_client.query_entities.assert_called_once_with(query_filter="PartitionKey eq 'test_pk' and RowKey eq 'test_rk'")

    @pytest.mark.unit
    def test_get_entity_not_found(self, table_repo, mock_table_client):
        """Test entity retrieval when entity not found"""
        from azure.core.exceptions import ResourceNotFoundError
        mock_table_client.get_entity.side_effect = ResourceNotFoundError("Not found")
        
        result = table_repo.get_entity("test_pk", "test_rk")
        
        assert result is None

    @pytest.mark.unit
    def test_query_entities(self, table_repo, mock_table_client):
        """Test entity querying"""
        expected_entities = [
            {"PartitionKey": "test_pk", "RowKey": "test_rk1"},
            {"PartitionKey": "test_pk", "RowKey": "test_rk2"}
        ]
        mock_table_client.query_entities.return_value = expected_entities
        
        result = table_repo.query_entities("PartitionKey eq 'test_pk'")
        
        assert result == expected_entities
        mock_table_client.query_entities.assert_called_once_with(query_filter="PartitionKey eq 'test_pk'")

    @pytest.mark.unit
    def test_update_entity_success(self, table_repo, mock_table_client):
        """Test successful entity update"""
        entity = {"PartitionKey": "test_pk", "RowKey": "test_rk", "data": "updated"}
        
        result = table_repo.update_entity(entity)
        
        assert result is True
        mock_table_client.upsert_entity.assert_called_once_with(entity)

    @pytest.mark.unit
    def test_delete_entity_success(self, table_repo, mock_table_client):
        """Test successful entity deletion"""
        result = table_repo.delete_entity("test_pk", "test_rk")
        
        assert result is True
        mock_table_client.delete_entity.assert_called_once_with("test_pk", "test_rk")


class TestSqlDatabaseRepository:
    """Test cases for SqlDatabaseRepository"""

    @pytest.fixture
    def sql_repo(self, mock_sql_connection):
        """Create a SqlDatabaseRepository instance for testing"""
        return SqlDatabaseRepository("test_table")

    @pytest.mark.unit
    def test_init(self, sql_repo):
        """Test SqlDatabaseRepository initialization"""
        assert sql_repo.table_name == "test_table"

    @pytest.mark.unit
    def test_execute_query_success(self, sql_repo):
        """Test query execution in local development (returns empty list)"""
        # In local development, execute_query returns empty list
        result = sql_repo.execute_query("SELECT * FROM test_table")
        assert result == []

    @pytest.mark.unit
    def test_execute_query_with_params(self, sql_repo):
        """Test query execution with parameters in local development"""
        # In local development, execute_query returns empty list regardless of parameters
        result = sql_repo.execute_query("SELECT * FROM test_table WHERE id = ?", ("test_id",))
        assert result == []

    @pytest.mark.unit
    def test_execute_non_query_success(self, sql_repo):
        """Test non-query execution in local development (returns True)"""
        # In local development, execute_non_query returns True without executing
        result = sql_repo.execute_non_query("INSERT INTO test_table VALUES (?)", ("test_value",))
        assert result is True

    @pytest.mark.unit
    def test_execute_non_query_failure(self, sql_repo):
        """Test non-query execution in local development (always returns True)"""
        # In local development, execute_non_query always returns True
        result = sql_repo.execute_non_query("INSERT INTO test_table VALUES (?)", ("test_value",))
        assert result is True


class TestRepositoryFactories:
    """Test cases for repository factory functions"""

    @pytest.mark.unit
    @patch('shared.data_access.TableStorageRepository')
    def test_get_table_storage_repository(self, mock_table_repo_class):
        """Test table storage repository factory"""
        mock_repo_instance = Mock()
        mock_repo_instance.table_name = "test_table"
        mock_table_repo_class.return_value = mock_repo_instance

        repo = get_table_storage_repository("test_table")

        assert repo is not None
        assert repo == mock_repo_instance
        mock_table_repo_class.assert_called_once_with(table_name="test_table")

    @pytest.mark.unit
    @patch('shared.data_access.SqlDatabaseRepository')
    def test_get_sql_database_repository(self, mock_sql_repo_class):
        """Test SQL database repository factory"""
        mock_repo_instance = Mock()
        mock_repo_instance.table_name = "test_table"
        mock_sql_repo_class.return_value = mock_repo_instance

        repo = get_sql_database_repository("test_table")

        assert repo is not None
        assert repo == mock_repo_instance
        mock_sql_repo_class.assert_called_once_with(table_name="test_table")

    @pytest.mark.unit
    @patch('shared.data_access.is_local_dev')
    def test_repository_caching(self, mock_is_local_dev, mock_table_client):
        """Test that repositories are cached properly"""
        mock_is_local_dev.return_value = True
        
        repo1 = get_table_storage_repository("test_table")
        repo2 = get_table_storage_repository("test_table")
        
        # Should return the same cached instance
        assert repo1 is repo2
