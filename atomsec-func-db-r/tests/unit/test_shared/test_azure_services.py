"""
Unit tests for shared.azure_services module
"""

import pytest
import os
from unittest.mock import Mock, patch, MagicMock

from shared.azure_services import (
    is_local_dev,
    get_credential,
    get_table_client,
    get_blob_client,
    get_queue_client,
    get_keyvault_client
)


class TestAzureServices:
    """Test cases for Azure services functionality"""

    @pytest.mark.unit
    def test_is_local_dev_true_explicit(self):
        """Test is_local_dev returns True when IS_LOCAL_DEV is explicitly set to true"""
        with patch.dict(os.environ, {'IS_LOCAL_DEV': 'true'}):
            assert is_local_dev() is True

    @pytest.mark.unit
    def test_is_local_dev_false_explicit(self):
        """Test is_local_dev returns False when IS_LOCAL_DEV is explicitly set to false"""
        with patch.dict(os.environ, {'IS_LOCAL_DEV': 'false'}):
            assert is_local_dev() is False

    @pytest.mark.unit
    def test_is_local_dev_azure_environment(self):
        """Test is_local_dev returns False in Azure environment"""
        with patch.dict(os.environ, {
            'WEBSITE_SITE_NAME': 'test-function-app',
            'WEBSITE_INSTANCE_ID': 'test-instance'
        }, clear=True):
            assert is_local_dev() is False

    @pytest.mark.unit
    def test_is_local_dev_no_indicators(self):
        """Test is_local_dev returns True when no environment indicators are present"""
        with patch.dict(os.environ, {}, clear=True):
            assert is_local_dev() is True

    @pytest.mark.unit
    def test_is_local_dev_case_insensitive(self):
        """Test is_local_dev is case insensitive"""
        test_cases = ['True', 'TRUE', 'tRuE', 'False', 'FALSE', 'fAlSe']
        
        for case in test_cases:
            with patch.dict(os.environ, {'IS_LOCAL_DEV': case}):
                expected = case.lower() == 'true'
                assert is_local_dev() is expected

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.azure_services.DefaultAzureCredential')
    def test_get_credential_production(self, mock_default_cred, mock_is_local_dev):
        """Test get_credential in production environment"""
        mock_is_local_dev.return_value = False
        mock_credential = Mock()
        mock_default_cred.return_value = mock_credential

        result = get_credential()

        assert result == mock_credential
        mock_default_cred.assert_called_once()

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.azure_services.DefaultAzureCredential')
    def test_get_credential_local_dev(self, mock_default_cred, mock_is_local_dev):
        """Test get_credential in local development"""
        mock_is_local_dev.return_value = True
        mock_credential = Mock()
        mock_default_cred.return_value = mock_credential

        result = get_credential()

        assert result == mock_credential
        mock_default_cred.assert_called_once()

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.azure_services.DefaultAzureCredential')
    def test_get_credential_exception(self, mock_default_cred, mock_is_local_dev):
        """Test get_credential with exception"""
        mock_is_local_dev.return_value = False
        mock_default_cred.side_effect = Exception("Credential error")

        result = get_credential()

        assert result is None

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.azure_services.TableServiceClient')
    def test_get_table_client_local_dev(self, mock_table_service, mock_is_local_dev):
        """Test get_table_client in local development"""
        mock_is_local_dev.return_value = True
        mock_client = Mock()
        mock_table_service.from_connection_string.return_value = mock_client

        result = get_table_client()

        assert result is not None
        mock_table_service.from_connection_string.assert_called_once()

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.azure_services.TableServiceClient')
    def test_get_table_client_production(self, mock_table_service, mock_is_local_dev):
        """Test get_table_client in production"""
        mock_is_local_dev.return_value = False
        mock_client = Mock()
        mock_table_service.from_connection_string.return_value = mock_client

        with patch.dict(os.environ, {'AZURE_STORAGE_CONNECTION_STRING': 'test-connection-string'}):
            result = get_table_client()

            assert result is not None
            mock_table_service.from_connection_string.assert_called_once()

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    def test_get_table_client_no_storage_account(self, mock_is_local_dev):
        """Test get_table_client without storage account name"""
        mock_is_local_dev.return_value = False

        with patch.dict(os.environ, {}, clear=True):
            try:
                result = get_table_client()
                # Should raise an error due to None connection string
                assert False, "Expected error for None connection string"
            except (ValueError, AttributeError) as e:
                # Could be ValueError from Azure SDK or AttributeError from None.strip()
                assert True  # Any of these exceptions is expected

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.azure_services.BlobServiceClient')
    def test_get_blob_client_local_dev(self, mock_blob_service, mock_is_local_dev):
        """Test get_blob_client in local development"""
        mock_is_local_dev.return_value = True
        mock_client = Mock()
        mock_blob_service.from_connection_string.return_value = mock_client

        result = get_blob_client()

        assert result is not None
        mock_blob_service.from_connection_string.assert_called_once()

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.azure_services.BlobServiceClient')
    def test_get_blob_client_production(self, mock_blob_service, mock_is_local_dev):
        """Test get_blob_client in production"""
        mock_is_local_dev.return_value = False
        mock_client = Mock()
        mock_blob_service.from_connection_string.return_value = mock_client

        with patch.dict(os.environ, {'AZURE_STORAGE_CONNECTION_STRING': 'test-connection-string'}):
            result = get_blob_client()

            assert result is not None
            mock_blob_service.from_connection_string.assert_called_once()

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.azure_services.QueueServiceClient')
    def test_get_queue_client_local_dev(self, mock_queue_service, mock_is_local_dev):
        """Test get_queue_client in local development"""
        mock_is_local_dev.return_value = True
        mock_client = Mock()
        mock_queue_service.from_connection_string.return_value = mock_client

        result = get_queue_client()

        assert result is not None
        mock_queue_service.from_connection_string.assert_called_once()

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.azure_services.QueueServiceClient')
    def test_get_queue_client_production(self, mock_queue_service, mock_is_local_dev):
        """Test get_queue_client in production"""
        mock_is_local_dev.return_value = False
        mock_client = Mock()
        mock_queue_service.from_connection_string.return_value = mock_client

        with patch.dict(os.environ, {'AZURE_STORAGE_CONNECTION_STRING': 'test-connection-string'}):
            result = get_queue_client()

            assert result is not None
            mock_queue_service.from_connection_string.assert_called_once()

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.azure_services.get_credential')
    @patch('shared.azure_services.SecretClient')
    def test_get_keyvault_client_production(self, mock_secret_client, mock_get_cred, mock_is_local_dev):
        """Test get_keyvault_client in production"""
        mock_is_local_dev.return_value = False
        mock_credential = Mock()
        mock_get_cred.return_value = mock_credential
        mock_client = Mock()
        mock_secret_client.return_value = mock_client

        with patch.dict(os.environ, {'KEY_VAULT_URL': 'https://test-kv.vault.azure.net/'}):
            result = get_keyvault_client()

            assert result == mock_client
            mock_secret_client.assert_called_once()

    @pytest.mark.unit
    @patch('shared.azure_services.get_credential')
    @patch('shared.azure_services.SecretClient')
    @patch('shared.config.get_key_vault_url')
    def test_get_keyvault_client_local_dev(self, mock_get_vault_url, mock_secret_client, mock_get_cred):
        """Test get_keyvault_client in local development"""
        mock_get_vault_url.return_value = "https://test-kv.vault.azure.net/"
        mock_credential = Mock()
        mock_get_cred.return_value = mock_credential
        mock_client = Mock()
        mock_secret_client.return_value = mock_client

        result = get_keyvault_client()

        assert result == mock_client
        mock_secret_client.assert_called_once_with(vault_url="https://test-kv.vault.azure.net/", credential=mock_credential)

    @pytest.mark.unit
    @patch('shared.azure_services.get_credential')
    @patch('shared.azure_services.SecretClient')
    @patch('shared.config.get_key_vault_url')
    def test_get_keyvault_client_no_url(self, mock_get_vault_url, mock_secret_client, mock_get_cred):
        """Test get_keyvault_client without Key Vault URL"""
        mock_get_vault_url.return_value = None
        mock_credential = Mock()
        mock_get_cred.return_value = mock_credential
        mock_client = Mock()
        mock_secret_client.return_value = mock_client

        result = get_keyvault_client()

        assert result == mock_client
        mock_secret_client.assert_called_once_with(vault_url=None, credential=mock_credential)

    @pytest.mark.unit
    @patch('shared.azure_services.get_credential')
    @patch('shared.config.get_key_vault_url')
    def test_get_keyvault_client_no_credential(self, mock_get_vault_url, mock_get_cred):
        """Test get_keyvault_client without credential"""
        mock_get_vault_url.return_value = "https://test-kv.vault.azure.net/"
        mock_get_cred.return_value = None

        try:
            result = get_keyvault_client()
            assert False, "Expected ValueError for None credential"
        except ValueError as e:
            assert "credential should be an object supporting the TokenCredential protocol" in str(e)

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.azure_services.TableServiceClient')
    def test_get_table_client_exception(self, mock_table_service, mock_is_local_dev):
        """Test get_table_client with exception"""
        mock_is_local_dev.return_value = True
        mock_table_service.from_connection_string.side_effect = Exception("Connection error")

        try:
            result = get_table_client()
            assert False, "Expected exception to be raised"
        except Exception as e:
            assert "Connection error" in str(e)

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.azure_services.BlobServiceClient')
    def test_get_blob_client_exception(self, mock_blob_service, mock_is_local_dev):
        """Test get_blob_client with exception"""
        mock_is_local_dev.return_value = True
        mock_blob_service.from_connection_string.side_effect = Exception("Connection error")

        try:
            result = get_blob_client()
            assert False, "Expected exception to be raised"
        except Exception as e:
            assert "Connection error" in str(e)

    @pytest.mark.unit
    @patch('shared.azure_services.is_local_dev')
    @patch('shared.azure_services.QueueServiceClient')
    def test_get_queue_client_exception(self, mock_queue_service, mock_is_local_dev):
        """Test get_queue_client with exception"""
        mock_is_local_dev.return_value = True
        mock_queue_service.from_connection_string.side_effect = Exception("Connection error")

        try:
            result = get_queue_client()
            assert False, "Expected exception to be raised"
        except Exception as e:
            assert "Connection error" in str(e)

    @pytest.mark.unit
    def test_azurite_connection_strings(self):
        """Test that Azurite connection strings are properly formatted"""
        with patch('shared.azure_services.is_local_dev', return_value=True):
            # Test that local development uses Azurite endpoints
            with patch('shared.azure_services.TableServiceClient') as mock_table_service:
                get_table_client("test_table")
                
                # Verify Azurite connection string is used
                call_args = mock_table_service.call_args
                if call_args and call_args[1].get('endpoint'):
                    endpoint = call_args[1]['endpoint']
                    assert '127.0.0.1' in endpoint or 'localhost' in endpoint

    @pytest.mark.unit
    def test_environment_variable_handling(self):
        """Test proper handling of environment variables"""
        # Test with missing environment variables
        with patch.dict(os.environ, {}, clear=True):
            assert is_local_dev() is True  # Should default to local dev
            
        # Test with empty environment variables
        with patch.dict(os.environ, {'IS_LOCAL_DEV': ''}):
            assert is_local_dev() is True  # Empty should be treated as True
