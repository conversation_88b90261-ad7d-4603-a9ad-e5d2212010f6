"""
Unit tests for shared.common module
"""

import pytest
import os
from unittest.mock import Mock, patch
from datetime import datetime

from shared.common import is_local_dev, is_test_env


class TestCommonUtilities:
    """Test cases for common utility functions"""

    @pytest.mark.unit
    def test_is_local_dev_true_explicit(self):
        """Test is_local_dev returns True when IS_LOCAL_DEV is explicitly set to true"""
        with patch.dict(os.environ, {'IS_LOCAL_DEV': 'true'}, clear=True):
            assert is_local_dev() is True

    @pytest.mark.unit
    def test_is_local_dev_false_explicit(self):
        """Test is_local_dev returns False when IS_LOCAL_DEV is explicitly set to 'false'"""
        with patch.dict(os.environ, {'IS_LOCAL_DEV': 'false'}, clear=True):
            # When explicitly set to 'false', should return False
            assert is_local_dev() is False

    @pytest.mark.unit
    def test_is_local_dev_azure_environment(self):
        """Test is_local_dev returns False in Azure environment"""
        with patch.dict(os.environ, {
            'WEBSITE_SITE_NAME': 'test-function-app',
            'WEBSITE_INSTANCE_ID': 'test-instance'
        }, clear=True):
            assert is_local_dev() is False

    @pytest.mark.unit
    def test_is_local_dev_no_indicators(self):
        """Test is_local_dev returns True when no environment indicators are present"""
        with patch.dict(os.environ, {}, clear=True):
            assert is_local_dev() is True

    @pytest.mark.unit
    def test_is_test_env_true(self):
        """Test is_test_env returns True when test environment is set"""
        with patch.dict(os.environ, {'ATOMSEC_TEST_ENV': 'true'}, clear=True):
            assert is_test_env() is True

    @pytest.mark.unit
    def test_is_test_env_pytest_running(self):
        """Test is_test_env returns True when pytest is running"""
        with patch.dict(os.environ, {'PYTEST_RUNNING': 'true'}, clear=True):
            assert is_test_env() is True

    @pytest.mark.unit
    def test_is_test_env_false(self):
        """Test is_test_env returns False when no test indicators are present"""
        with patch.dict(os.environ, {}, clear=True):
            assert is_test_env() is False

    @pytest.mark.unit
    def test_is_local_dev_use_local_storage(self):
        """Test is_local_dev returns True when USE_LOCAL_STORAGE is set"""
        with patch.dict(os.environ, {'USE_LOCAL_STORAGE': 'true'}, clear=True):
            assert is_local_dev() is True

    @pytest.mark.unit
    def test_is_local_dev_localhost_hostname(self):
        """Test is_local_dev returns True for localhost hostname"""
        with patch.dict(os.environ, {'WEBSITE_HOSTNAME': 'localhost:7071'}, clear=True):
            assert is_local_dev() is True

    @pytest.mark.unit
    def test_is_local_dev_127_hostname(self):
        """Test is_local_dev returns True for 127.0.0.1 hostname"""
        with patch.dict(os.environ, {'WEBSITE_HOSTNAME': '127.0.0.1:7071'}, clear=True):
            assert is_local_dev() is True

    @pytest.mark.unit
    def test_is_local_dev_azure_functions_production(self):
        """Test is_local_dev returns False for Azure Functions production"""
        with patch.dict(os.environ, {
            'FUNCTIONS_WORKER_RUNTIME': 'python',
            'FUNCTIONS_WORKER_RUNTIME_VERSION': '4',
            'WEBSITE_SITE_NAME': 'test-function-app'
        }, clear=True):
            assert is_local_dev() is False

    @pytest.mark.unit
    def test_is_local_dev_missing_runtime_version(self):
        """Test is_local_dev returns True when FUNCTIONS_WORKER_RUNTIME_VERSION is missing"""
        with patch.dict(os.environ, {
            'FUNCTIONS_WORKER_RUNTIME': 'python'
            # FUNCTIONS_WORKER_RUNTIME_VERSION is missing
        }, clear=True):
            assert is_local_dev() is True

    @pytest.mark.unit
    def test_is_local_dev_default_production(self):
        """Test is_local_dev defaults to production for safety"""
        with patch.dict(os.environ, {
            'FUNCTIONS_WORKER_RUNTIME': 'python',
            'FUNCTIONS_WORKER_RUNTIME_VERSION': '4'
            # No Azure indicators, but has runtime info
        }, clear=True):
            assert is_local_dev() is False

    @pytest.mark.unit
    def test_is_local_dev_case_sensitivity(self):
        """Test is_local_dev with different case values"""
        test_cases = [
            ('true', True),   # Explicit true
            ('True', True),   # Case insensitive true
            ('TRUE', True),   # Case insensitive true
            ('false', False), # Explicit false
            ('False', False)  # Case insensitive false
        ]

        for value, expected in test_cases:
            with patch.dict(os.environ, {'IS_LOCAL_DEV': value}, clear=True):
                assert is_local_dev() is expected, f"IS_LOCAL_DEV='{value}' should return {expected}"

    @pytest.mark.unit
    def test_is_test_env_case_sensitivity(self):
        """Test is_test_env with different case values"""
        test_cases = [
            ('true', True),
            ('True', False),  # Only lowercase 'true' is accepted
            ('TRUE', False),
            ('false', False)
        ]

        for value, expected in test_cases:
            with patch.dict(os.environ, {'ATOMSEC_TEST_ENV': value}, clear=True):
                assert is_test_env() is expected, f"ATOMSEC_TEST_ENV='{value}' should return {expected}"
