"""
Integration tests for APIM connectivity
Converted from existing test_apim_connectivity.py
"""

import pytest
import requests
import json
from datetime import datetime


class TestAPIMConnectivity:
    """Integration tests for APIM connectivity and endpoints"""

    @pytest.fixture
    def apim_config(self):
        """APIM configuration for testing"""
        return {
            "base_url": "**************************************/db",
            "subscription_key": "bd50cc1018444ae987a04c465534e428",
            "headers": {
                'Ocp-Apim-Subscription-Key': "bd50cc1018444ae987a04c465534e428",
                'Content-Type': 'application/json',
                'User-Agent': 'APIM-Connectivity-Test'
            }
        }

    @pytest.fixture
    def test_endpoints(self):
        """List of endpoints to test"""
        return [
            "/v1/health",
            "/v1/info",
            "/v1/diagnostic"
        ]

    @pytest.mark.integration
    @pytest.mark.slow
    def test_apim_health_endpoint(self, apim_config):
        """Test APIM health endpoint connectivity"""
        url = f"{apim_config['base_url']}/v1/health"
        
        try:
            response = requests.get(url, headers=apim_config['headers'], timeout=15)
            
            # Accept various success responses
            assert response.status_code in [200, 401, 403], f"Unexpected status code: {response.status_code}"
            
            if response.status_code == 200:
                # If we get 200, verify response structure
                try:
                    data = response.json()
                    assert "status" in data or "health" in data
                except json.JSONDecodeError:
                    # Non-JSON response is acceptable for health checks
                    pass
                    
        except requests.exceptions.RequestException as e:
            pytest.skip(f"APIM endpoint not accessible: {e}")

    @pytest.mark.integration
    @pytest.mark.slow
    def test_apim_info_endpoint(self, apim_config):
        """Test APIM info endpoint connectivity"""
        url = f"{apim_config['base_url']}/v1/info"
        
        try:
            response = requests.get(url, headers=apim_config['headers'], timeout=15)
            
            # Accept various success responses
            assert response.status_code in [200, 401, 403], f"Unexpected status code: {response.status_code}"
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    assert "service" in data or "version" in data or "name" in data
                except json.JSONDecodeError:
                    pass
                    
        except requests.exceptions.RequestException as e:
            pytest.skip(f"APIM endpoint not accessible: {e}")

    @pytest.mark.integration
    @pytest.mark.slow
    def test_apim_subscription_key_required(self, apim_config):
        """Test that APIM correctly requires subscription key"""
        url = f"{apim_config['base_url']}/v1/health"
        headers_without_key = {
            'Content-Type': 'application/json',
            'User-Agent': 'APIM-Connectivity-Test'
        }
        
        try:
            response = requests.get(url, headers=headers_without_key, timeout=15)
            
            # Should return 401 (Unauthorized) without subscription key
            assert response.status_code == 401, f"Expected 401 without subscription key, got {response.status_code}"
            
        except requests.exceptions.RequestException as e:
            pytest.skip(f"APIM endpoint not accessible: {e}")

    @pytest.mark.integration
    @pytest.mark.slow
    def test_apim_response_time(self, apim_config):
        """Test APIM response time performance"""
        url = f"{apim_config['base_url']}/v1/health"
        
        try:
            start_time = datetime.now()
            response = requests.get(url, headers=apim_config['headers'], timeout=30)
            end_time = datetime.now()
            
            response_time_ms = (end_time - start_time).total_seconds() * 1000
            
            # Response should be reasonably fast (under 5 seconds)
            assert response_time_ms < 5000, f"Response time too slow: {response_time_ms}ms"
            
            # Accept various success responses
            assert response.status_code in [200, 401, 403], f"Unexpected status code: {response.status_code}"
            
        except requests.exceptions.RequestException as e:
            pytest.skip(f"APIM endpoint not accessible: {e}")

    @pytest.mark.integration
    @pytest.mark.slow
    def test_all_apim_endpoints(self, apim_config, test_endpoints):
        """Test all APIM endpoints for basic connectivity"""
        results = {}
        
        for endpoint in test_endpoints:
            url = f"{apim_config['base_url']}{endpoint}"
            
            try:
                response = requests.get(url, headers=apim_config['headers'], timeout=15)
                results[endpoint] = {
                    'status_code': response.status_code,
                    'accessible': response.status_code in [200, 401, 403]
                }
                
            except requests.exceptions.RequestException as e:
                results[endpoint] = {
                    'status_code': None,
                    'accessible': False,
                    'error': str(e)
                }
        
        # At least one endpoint should be accessible
        accessible_endpoints = [ep for ep, result in results.items() if result['accessible']]
        assert len(accessible_endpoints) > 0, f"No endpoints accessible. Results: {results}"

    @pytest.mark.integration
    @pytest.mark.slow
    def test_apim_error_responses(self, apim_config):
        """Test APIM error response handling"""
        # Test non-existent endpoint
        url = f"{apim_config['base_url']}/v1/nonexistent"
        
        try:
            response = requests.get(url, headers=apim_config['headers'], timeout=15)
            
            # Should return 404 for non-existent endpoints
            assert response.status_code in [404, 401, 403], f"Unexpected status code for non-existent endpoint: {response.status_code}"
            
        except requests.exceptions.RequestException as e:
            pytest.skip(f"APIM endpoint not accessible: {e}")

    @pytest.mark.integration
    @pytest.mark.slow
    def test_apim_headers_validation(self, apim_config):
        """Test APIM headers validation"""
        url = f"{apim_config['base_url']}/v1/health"
        
        # Test with invalid subscription key
        invalid_headers = apim_config['headers'].copy()
        invalid_headers['Ocp-Apim-Subscription-Key'] = 'invalid-key'
        
        try:
            response = requests.get(url, headers=invalid_headers, timeout=15)
            
            # Should return 401 or 403 for invalid subscription key
            assert response.status_code in [401, 403], f"Expected 401/403 for invalid key, got {response.status_code}"
            
        except requests.exceptions.RequestException as e:
            pytest.skip(f"APIM endpoint not accessible: {e}")

    @pytest.mark.integration
    @pytest.mark.slow
    def test_apim_content_type_handling(self, apim_config):
        """Test APIM content type handling"""
        url = f"{apim_config['base_url']}/v1/health"
        
        # Test with different content types
        content_types = [
            'application/json',
            'application/xml',
            'text/plain'
        ]
        
        for content_type in content_types:
            headers = apim_config['headers'].copy()
            headers['Content-Type'] = content_type
            
            try:
                response = requests.get(url, headers=headers, timeout=15)
                
                # Should handle different content types gracefully
                assert response.status_code in [200, 401, 403, 415], f"Unexpected status for content-type {content_type}: {response.status_code}"
                
            except requests.exceptions.RequestException:
                # Network errors are acceptable for this test
                continue

    @pytest.mark.integration
    @pytest.mark.slow
    def test_apim_cors_headers(self, apim_config):
        """Test APIM CORS headers if applicable"""
        url = f"{apim_config['base_url']}/v1/health"
        
        # Add CORS headers
        cors_headers = apim_config['headers'].copy()
        cors_headers['Origin'] = 'https://example.com'
        
        try:
            response = requests.options(url, headers=cors_headers, timeout=15)
            
            # CORS preflight should be handled appropriately
            assert response.status_code in [200, 204, 401, 403, 405], f"Unexpected CORS response: {response.status_code}"
            
        except requests.exceptions.RequestException as e:
            pytest.skip(f"APIM CORS test not accessible: {e}")

    @pytest.mark.integration
    @pytest.mark.slow
    def test_apim_rate_limiting(self, apim_config):
        """Test APIM rate limiting behavior"""
        url = f"{apim_config['base_url']}/v1/health"
        
        # Make multiple rapid requests to test rate limiting
        responses = []
        for i in range(5):
            try:
                response = requests.get(url, headers=apim_config['headers'], timeout=10)
                responses.append(response.status_code)
            except requests.exceptions.RequestException:
                responses.append(None)
        
        # Should handle multiple requests gracefully
        # Rate limiting might return 429, but basic functionality should work
        successful_responses = [r for r in responses if r in [200, 401, 403]]
        assert len(successful_responses) > 0, f"No successful responses in rate limiting test: {responses}"
