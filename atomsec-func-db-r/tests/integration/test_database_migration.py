"""
Integration tests for database migration functionality
Converted from scripts/test_migration.py
"""

import pytest
import requests
import json
import uuid
from datetime import datetime
from typing import Dict, Any
import os


class TestDatabaseMigration:
    """Integration tests for database migration validation"""

    @pytest.fixture
    def base_url(self):
        """Get base URL for testing"""
        return os.environ.get('TEST_BASE_URL', 'http://localhost:7072/api')

    @pytest.fixture
    def test_session(self, base_url):
        """Create a test session with proper headers"""
        session = requests.Session()
        session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'DatabaseMigrationTester/1.0.0'
        })
        return session

    @pytest.fixture
    def test_account_data(self):
        """Generate test account data"""
        return {
            "name": f"Test Account {uuid.uuid4().hex[:8]}",
            "description": "Test account for migration validation",
            "contact_email": "<EMAIL>",
            "is_active": True
        }

    @pytest.fixture
    def test_user_data(self):
        """Generate test user data"""
        return {
            "email": f"test-{uuid.uuid4().hex[:8]}@example.com",
            "first_name": "Test",
            "last_name": "User",
            "is_active": True
        }

    def make_request(self, session, base_url, method: str, endpoint: str, 
                    data: Dict = None, params: Dict = None) -> Dict[str, Any]:
        """Make HTTP request to the database service"""
        url = f"{base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        try:
            response = session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                timeout=30
            )
            
            if response.status_code >= 200 and response.status_code < 300:
                try:
                    return {"success": True, "data": response.json(), "status_code": response.status_code}
                except json.JSONDecodeError:
                    return {"success": True, "data": response.text, "status_code": response.status_code}
            else:
                return {
                    "success": False, 
                    "error": f"HTTP {response.status_code}", 
                    "status_code": response.status_code,
                    "response": response.text
                }
                
        except Exception as e:
            return {"success": False, "error": str(e)}

    @pytest.mark.integration
    @pytest.mark.database
    @pytest.mark.slow
    def test_service_info_endpoint(self, test_session, base_url):
        """Test the service info endpoint"""
        response = self.make_request(test_session, base_url, 'GET', '/')
        
        # Accept various success responses
        assert response.get('success') or response.get('status_code') in [401, 403], \
            f"Service info endpoint failed: {response}"

    @pytest.mark.integration
    @pytest.mark.database
    @pytest.mark.slow
    def test_health_endpoint(self, test_session, base_url):
        """Test the health endpoint"""
        response = self.make_request(test_session, base_url, 'GET', 'health')
        
        # Accept various success responses
        assert response.get('success') or response.get('status_code') in [401, 403], \
            f"Health endpoint failed: {response}"

    @pytest.mark.integration
    @pytest.mark.database
    @pytest.mark.slow
    def test_account_crud_operations(self, test_session, base_url, test_account_data):
        """Test account CRUD operations"""
        # Create account
        create_response = self.make_request(test_session, base_url, 'POST', 'accounts', data=test_account_data)
        
        if not create_response.get('success'):
            # If creation fails due to auth, skip the test
            if create_response.get('status_code') in [401, 403]:
                pytest.skip("Account creation requires authentication")
            else:
                pytest.fail(f"Account creation failed: {create_response}")
        
        account_data = create_response.get('data', {})
        account_id = account_data.get('account_id') or account_data.get('id')
        
        assert account_id, f"No account ID returned: {account_data}"
        
        # Read account
        read_response = self.make_request(test_session, base_url, 'GET', f'accounts/{account_id}')
        
        if read_response.get('success'):
            retrieved_account = read_response.get('data', {})
            assert retrieved_account.get('name') == test_account_data['name']
        
        # Update account
        update_data = {"name": f"Updated {test_account_data['name']}"}
        update_response = self.make_request(test_session, base_url, 'PUT', f'accounts/{account_id}', data=update_data)
        
        # Delete account
        delete_response = self.make_request(test_session, base_url, 'DELETE', f'accounts/{account_id}')

    @pytest.mark.integration
    @pytest.mark.database
    @pytest.mark.slow
    def test_user_crud_operations(self, test_session, base_url, test_user_data):
        """Test user CRUD operations"""
        # Create user
        create_response = self.make_request(test_session, base_url, 'POST', 'users', data=test_user_data)
        
        if not create_response.get('success'):
            # If creation fails due to auth, skip the test
            if create_response.get('status_code') in [401, 403]:
                pytest.skip("User creation requires authentication")
            else:
                pytest.fail(f"User creation failed: {create_response}")
        
        user_data = create_response.get('data', {})
        user_id = user_data.get('user_id') or user_data.get('id')
        
        assert user_id, f"No user ID returned: {user_data}"
        
        # Read user
        read_response = self.make_request(test_session, base_url, 'GET', f'users/{user_id}')
        
        if read_response.get('success'):
            retrieved_user = read_response.get('data', {})
            assert retrieved_user.get('email') == test_user_data['email']
        
        # Update user
        update_data = {"first_name": "Updated"}
        update_response = self.make_request(test_session, base_url, 'PUT', f'users/{user_id}', data=update_data)
        
        # Delete user
        delete_response = self.make_request(test_session, base_url, 'DELETE', f'users/{user_id}')

    @pytest.mark.integration
    @pytest.mark.database
    @pytest.mark.slow
    def test_execution_log_operations(self, test_session, base_url):
        """Test execution log operations"""
        execution_log_data = {
            "org_id": f"test-org-{uuid.uuid4().hex[:8]}",
            "user_id": f"test-user-{uuid.uuid4().hex[:8]}",
            "task_type": "PMD_SCAN",
            "status": "PENDING"
        }
        
        # Create execution log
        create_response = self.make_request(test_session, base_url, 'POST', 'execution-logs', data=execution_log_data)
        
        if not create_response.get('success'):
            if create_response.get('status_code') in [401, 403]:
                pytest.skip("Execution log creation requires authentication")
            else:
                pytest.fail(f"Execution log creation failed: {create_response}")
        
        log_data = create_response.get('data', {})
        log_id = log_data.get('execution_log_id') or log_data.get('id')
        
        assert log_id, f"No execution log ID returned: {log_data}"
        
        # Read execution log
        read_response = self.make_request(test_session, base_url, 'GET', f'execution-logs/{log_id}')
        
        if read_response.get('success'):
            retrieved_log = read_response.get('data', {})
            assert retrieved_log.get('task_type') == execution_log_data['task_type']
        
        # Update execution log status
        update_data = {"status": "COMPLETED"}
        update_response = self.make_request(test_session, base_url, 'PUT', f'execution-logs/{log_id}', data=update_data)

    @pytest.mark.integration
    @pytest.mark.database
    @pytest.mark.slow
    def test_policy_operations(self, test_session, base_url):
        """Test policy operations"""
        policy_data = {
            "name": f"Test Policy {uuid.uuid4().hex[:8]}",
            "description": "Test policy for migration validation",
            "is_active": True
        }
        
        # Create policy
        create_response = self.make_request(test_session, base_url, 'POST', 'policies', data=policy_data)
        
        if not create_response.get('success'):
            if create_response.get('status_code') in [401, 403]:
                pytest.skip("Policy creation requires authentication")
            else:
                pytest.fail(f"Policy creation failed: {create_response}")
        
        policy_response_data = create_response.get('data', {})
        policy_id = policy_response_data.get('policy_id') or policy_response_data.get('id')
        
        assert policy_id, f"No policy ID returned: {policy_response_data}"
        
        # Read policy
        read_response = self.make_request(test_session, base_url, 'GET', f'policies/{policy_id}')
        
        if read_response.get('success'):
            retrieved_policy = read_response.get('data', {})
            assert retrieved_policy.get('name') == policy_data['name']

    @pytest.mark.integration
    @pytest.mark.database
    def test_endpoint_accessibility(self, test_session, base_url):
        """Test that all major endpoints are accessible"""
        endpoints = [
            '/',
            'health',
            'info',
            'accounts',
            'users',
            'policies',
            'execution-logs'
        ]
        
        accessible_endpoints = []
        
        for endpoint in endpoints:
            response = self.make_request(test_session, base_url, 'GET', endpoint)
            
            # Consider endpoint accessible if it returns success or auth error
            if response.get('success') or response.get('status_code') in [401, 403]:
                accessible_endpoints.append(endpoint)
        
        # At least some endpoints should be accessible
        assert len(accessible_endpoints) > 0, f"No endpoints accessible. Tested: {endpoints}"

    @pytest.mark.integration
    @pytest.mark.database
    @pytest.mark.slow
    def test_error_handling(self, test_session, base_url):
        """Test error handling for invalid requests"""
        # Test invalid endpoint
        response = self.make_request(test_session, base_url, 'GET', 'nonexistent-endpoint')
        assert response.get('status_code') in [404, 401, 403], \
            f"Expected 404/401/403 for invalid endpoint, got: {response}"
        
        # Test invalid data
        invalid_data = {"invalid": "data"}
        response = self.make_request(test_session, base_url, 'POST', 'accounts', data=invalid_data)
        # Should return error or auth error
        assert not response.get('success') or response.get('status_code') in [401, 403], \
            f"Expected error for invalid data, got: {response}"
