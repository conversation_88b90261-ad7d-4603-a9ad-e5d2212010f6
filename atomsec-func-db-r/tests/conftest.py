"""
Pytest configuration and fixtures for atomsec-func-db-r tests
"""

import os
import sys
import pytest
from unittest.mock import Mock, patch
from typing import Dict, Any

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Set up test environment variables"""
    test_env_vars = {
        "IS_LOCAL_DEV": "true",
        "AZURE_AD_TENANT_ID": "test-tenant-id",
        "AZURE_AD_CLIENT_ID": "test-client-id",
        "AZURE_AD_CLIENT_SECRET": "test-client-secret",
        "KEY_VAULT_NAME": "test-keyvault",
        "KEY_VAULT_URL": "https://test-keyvault.vault.azure.net/",
        "ENVIRONMENT": "test",
        "SFDC_SERVICE_URL": "http://localhost:7071",
        "FRONTEND_URL": "http://localhost:3000",
        "FUNCTIONS_WORKER_RUNTIME": "python",
        "FUNCTIONS_EXTENSION_VERSION": "~4",
    }
    
    # Set environment variables for tests
    for key, value in test_env_vars.items():
        os.environ[key] = value
    
    yield
    
    # Clean up environment variables after tests
    for key in test_env_vars.keys():
        os.environ.pop(key, None)

@pytest.fixture
def mock_azure_credential():
    """Mock Azure credential for testing"""
    with patch('shared.azure_services.get_credential') as mock_cred:
        mock_cred.return_value = Mock()
        yield mock_cred

@pytest.fixture
def mock_db_client():
    """Mock database client for repository testing"""
    mock_client = Mock()
    mock_client.execute_query.return_value = []
    mock_client.execute_non_query.return_value = True
    mock_client.execute_scalar.return_value = 1
    return mock_client

@pytest.fixture(autouse=True)
def mock_repository_storage_classes():
    """Auto-mock storage repository classes for all repository tests"""
    with patch('shared.azure_services.get_table_client') as mock_get_table_client, \
         patch('shared.data_access.TableStorageRepository') as mock_table_repo_class, \
         patch('shared.data_access.SqlDatabaseRepository') as mock_sql_repo_class:

        # Mock the table client
        mock_table_client = Mock()
        mock_table_client.list_tables.return_value = []
        mock_table_client.create_table_if_not_exists.return_value = Mock()
        mock_table_client.get_table_client.return_value = Mock()
        mock_get_table_client.return_value = mock_table_client

        # Configure the mock classes to return mock instances
        mock_table_repo = Mock()
        mock_table_repo.query_entities.return_value = []
        mock_table_repo.insert_entity.return_value = True
        mock_table_repo.update_entity.return_value = True
        mock_table_repo.delete_entity.return_value = True
        mock_table_repo_class.return_value = mock_table_repo

        mock_sql_repo = Mock()
        mock_sql_repo.execute_query.return_value = []
        mock_sql_repo.execute_non_query.return_value = True
        mock_sql_repo.query_entities.return_value = []
        mock_sql_repo.insert_entity.return_value = True
        mock_sql_repo.update_entity.return_value = True
        mock_sql_repo.delete_entity.return_value = True
        mock_sql_repo_class.return_value = mock_sql_repo

        yield {
            'table_client': mock_table_client,
            'table_repo_class': mock_table_repo_class,
            'sql_repo_class': mock_sql_repo_class,
            'table_repo': mock_table_repo,
            'sql_repo': mock_sql_repo
        }

@pytest.fixture
def mock_table_client():
    """Mock Azure Table Storage client"""
    with patch('shared.azure_services.get_table_client') as mock_client:
        mock_table = Mock()
        mock_table.create_table.return_value = None
        mock_table.query_entities.return_value = []
        mock_table.get_entity.return_value = {}
        mock_table.upsert_entity.return_value = None
        mock_table.delete_entity.return_value = None
        mock_client.return_value = mock_table
        yield mock_table

@pytest.fixture
def mock_blob_client():
    """Mock Azure Blob Storage client"""
    with patch('shared.azure_services.get_blob_client') as mock_client:
        mock_blob = Mock()
        mock_blob.upload_blob.return_value = None
        mock_blob.download_blob.return_value = Mock(readall=lambda: b'test data')
        mock_blob.delete_blob.return_value = None
        mock_client.return_value = mock_blob
        yield mock_blob

@pytest.fixture
def mock_queue_client():
    """Mock Azure Queue Storage client"""
    with patch('shared.azure_services.get_queue_client') as mock_client:
        mock_queue = Mock()
        mock_queue.send_message.return_value = None
        mock_queue.receive_messages.return_value = []
        mock_queue.delete_message.return_value = None
        mock_client.return_value = mock_queue
        yield mock_queue

@pytest.fixture
def mock_sql_connection():
    """Mock SQL database connection"""
    with patch('shared.data_access.SqlDatabaseRepository._get_connection') as mock_conn:
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_cursor.fetchall.return_value = []
        mock_cursor.fetchone.return_value = None
        mock_cursor.rowcount = 0
        mock_connection.cursor.return_value = mock_cursor
        mock_conn.return_value = mock_connection
        yield mock_connection

@pytest.fixture
def sample_execution_log_data():
    """Sample execution log data for testing"""
    from datetime import datetime
    return {
        "ExecutionLogId": "test-execution-log-id",
        "OrgId": "test-org-id",
        "UserId": "test-user-id",
        "TaskType": "PMD_SCAN",
        "Status": "PENDING",
        "StartedAt": datetime(2024, 1, 1, 0, 0, 0),  # Use datetime object
        "CompletedAt": None,
        "ErrorMessage": None,
        "ResultData": None
    }

@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        "UserId": "test-user-id",
        "Email": "<EMAIL>",
        "FirstName": "Test",
        "LastName": "User",
        "OrgId": "test-org-id",
        "IsActive": True,
        "CreatedAt": "2024-01-01T00:00:00Z"
    }

@pytest.fixture
def sample_policy_data():
    """Sample policy data for testing"""
    return {
        "PolicyId": "test-policy-id",
        "PolicyName": "Test Policy",
        "Description": "Test policy description",
        "IsActive": True,
        "CreatedAt": "2024-01-01T00:00:00Z",
        "Rules": []
    }

@pytest.fixture
def mock_azure_functions_request():
    """Mock Azure Functions HTTP request"""
    mock_req = Mock()
    mock_req.method = "GET"
    mock_req.url = "http://localhost:7072/api/test"
    mock_req.headers = {}
    mock_req.params = {}
    mock_req.get_json.return_value = {}
    mock_req.get_body.return_value = b'{}'
    return mock_req

@pytest.fixture
def mock_jwt_token():
    """Mock JWT token for testing"""
    return {
        "iss": "https://sts.windows.net/test-tenant-id/",
        "aud": "test-client-id",
        "sub": "test-user-id",
        "email": "<EMAIL>",
        "name": "Test User",
        "tid": "test-tenant-id",
        "appid": "test-client-id",
        "exp": 9999999999,  # Far future expiration
        "iat": 1000000000,
        "nbf": 1000000000
    }

@pytest.fixture
def mock_keyvault_client():
    """Mock Azure Key Vault client"""
    with patch('shared.azure_services.get_keyvault_client') as mock_client:
        mock_kv = Mock()
        mock_kv.get_secret.return_value = Mock(value="test-secret-value")
        mock_client.return_value = mock_kv
        yield mock_kv

# Test markers
pytest_plugins = []

def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "auth: mark test as authentication related"
    )
    config.addinivalue_line(
        "markers", "database: mark test as database related"
    )
