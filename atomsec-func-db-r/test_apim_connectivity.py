#!/usr/bin/env python3
"""
Test script to verify APIM connectivity and diagnose deployment issues
"""

import requests
import json
import sys
from datetime import datetime

def test_apim_connectivity():
    """Test APIM connectivity and endpoints"""
    
    print("🔍 Testing APIM Connectivity for AtomSec Database Service")
    print("=" * 60)
    
    # APIM configuration
    apim_base_url = "**************************************/db"
    subscription_key = "bd50cc1018444ae987a04c465534e428"
    
    # Test endpoints
    endpoints = [
        "/v1/health",
        "/v1/info",
        "/v1/diagnostic"
    ]
    
    headers = {
        'Ocp-Apim-Subscription-Key': subscription_key,
        'Content-Type': 'application/json',
        'User-Agent': 'APIM-Connectivity-Test'
    }
    
    print(f"🌐 APIM Base URL: {apim_base_url}")
    print(f"🔑 Subscription Key: {subscription_key[:8]}...{subscription_key[-4:]}")
    print()
    
    # Test each endpoint
    for endpoint in endpoints:
        url = f"{apim_base_url}{endpoint}"
        print(f"🧪 Testing: {endpoint}")
        print(f"   URL: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=15)
            print(f"   Status: HTTP {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ SUCCESS: Endpoint is working")
                try:
                    data = response.json()
                    if endpoint == "/v1/health":
                        status = data.get("status", "unknown")
                        print(f"   📊 Health Status: {status}")
                    elif endpoint == "/v1/info":
                        service = data.get("service", "unknown")
                        print(f"   📊 Service: {service}")
                except json.JSONDecodeError:
                    print("   📊 Response: Not JSON")
                    
            elif response.status_code == 401:
                print("   ✅ SUCCESS: Endpoint is accessible (authentication required)")
                print("   📝 This is expected behavior - APIM is working correctly")
                
            elif response.status_code == 403:
                print("   ❌ ERROR: Access forbidden")
                print("   🔍 This suggests IP restrictions or authentication middleware issues")
                
            elif response.status_code == 404:
                print("   ❌ ERROR: Endpoint not found")
                print("   🔍 Check if the endpoint is properly configured in APIM")
                
            elif response.status_code == 500:
                print("   ❌ ERROR: Internal server error")
                print("   🔍 Check function app logs for errors")
                
            else:
                print(f"   ⚠️  UNEXPECTED: Status {response.status_code}")
                
            # Show response headers for debugging
            if response.status_code not in [200, 401]:
                print(f"   📋 Response Headers:")
                for key, value in response.headers.items():
                    if key.lower() in ['content-type', 'content-length', 'server', 'date']:
                        print(f"      {key}: {value}")
                        
        except requests.exceptions.Timeout:
            print("   ❌ ERROR: Request timeout")
            print("   🔍 Check network connectivity and APIM availability")
            
        except requests.exceptions.ConnectionError:
            print("   ❌ ERROR: Connection failed")
            print("   🔍 Check if APIM is accessible and network connectivity")
            
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")
            
        print()
    
    # Test without subscription key (should fail)
    print("🧪 Testing without subscription key (should fail)")
    url = f"{apim_base_url}/v1/health"
    try:
        response = requests.get(url, timeout=15)
        print(f"   Status: HTTP {response.status_code}")
        if response.status_code == 401:
            print("   ✅ SUCCESS: APIM correctly rejects requests without subscription key")
        else:
            print(f"   ⚠️  UNEXPECTED: Got {response.status_code} without subscription key")
    except Exception as e:
        print(f"   ❌ ERROR: {str(e)}")
    
    print()
    print("=" * 60)
    print("📊 Test Summary:")
    print("✅ HTTP 200: Perfect success")
    print("✅ HTTP 401: Expected (authentication required)")
    print("❌ HTTP 403: Access forbidden (investigate)")
    print("❌ HTTP 404: Endpoint not found (check APIM config)")
    print("❌ HTTP 500: Server error (check function app logs)")
    print()
    print("🔧 Next Steps:")
    print("1. If you see HTTP 401 responses, APIM is working correctly")
    print("2. If you see HTTP 403, check IP restrictions and authentication middleware")
    print("3. If you see HTTP 500, check function app logs and configuration")
    print("4. Update the deployment pipeline to use APIM endpoints")

if __name__ == "__main__":
    test_apim_connectivity()
