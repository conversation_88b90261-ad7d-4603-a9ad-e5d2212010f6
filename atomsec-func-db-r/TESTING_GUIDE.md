# Testing Guide for AtomSec Database Function App

## Overview

This guide explains the new test structure implemented for the atomsec-func-db-r project. The testing framework has been restructured to follow best practices with proper unit tests, integration tests, and comprehensive coverage.

## Test Structure

```
atomsec-func-db-r/
├── tests/
│   ├── __init__.py
│   ├── conftest.py                    # Pytest configuration and fixtures
│   ├── unit/                          # Unit tests
│   │   ├── __init__.py
│   │   ├── test_shared/               # Tests for shared/ modules
│   │   │   ├── test_data_access.py
│   │   │   ├── test_db_service.py
│   │   │   └── ...
│   │   ├── test_api/                  # Tests for api/ modules
│   │   │   └── ...
│   │   └── test_repositories/         # Tests for repositories/ modules
│   │       ├── test_base_repository.py
│   │       └── ...
│   └── integration/                   # Integration tests
│       ├── __init__.py
│       ├── test_auth_integration.py
│       ├── test_apim_connectivity.py
│       └── ...
├── pytest.ini                        # Pytest configuration
├── run_tests.py                      # Test runner script
└── requirements-dev.txt              # Development dependencies
```

## Running Tests

### Quick Start

```bash
# Install test dependencies
pip install -r requirements-dev.txt

# Run all tests
python run_tests.py

# Run with coverage
python run_tests.py --coverage

# Run only unit tests
python run_tests.py --type unit

# Run only integration tests
python run_tests.py --type integration
```

### Advanced Usage

```bash
# Run tests for specific module
python run_tests.py --module shared

# Run specific test file
python run_tests.py --file tests/unit/test_shared/test_data_access.py

# Run fast tests only (skip slow integration tests)
python run_tests.py --fast

# Verbose output
python run_tests.py --verbose

# Install dependencies and run tests
python run_tests.py --install-deps --coverage
```

### Direct Pytest Usage

```bash
# Run all tests
pytest

# Run unit tests only
pytest tests/unit

# Run integration tests only
pytest tests/integration

# Run with coverage
pytest --cov=shared --cov=api --cov=repositories --cov-report=html

# Run specific test markers
pytest -m "unit"
pytest -m "integration"
pytest -m "auth"
pytest -m "database"
```

## Test Categories

### Unit Tests
- **Location**: `tests/unit/`
- **Purpose**: Test individual functions and classes in isolation
- **Mocking**: Extensive use of mocks for external dependencies
- **Speed**: Fast execution
- **Coverage**: High code coverage expected

### Integration Tests
- **Location**: `tests/integration/`
- **Purpose**: Test component interactions and external services
- **Dependencies**: May require network access or external services
- **Speed**: Slower execution
- **Markers**: Use `@pytest.mark.slow` for tests that take significant time

## Test Markers

The following pytest markers are available:

- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.slow` - Slow running tests
- `@pytest.mark.auth` - Authentication related tests
- `@pytest.mark.database` - Database related tests
- `@pytest.mark.api` - API endpoint tests

## Fixtures

Common fixtures are available in `tests/conftest.py`:

- `mock_azure_credential` - Mock Azure credential
- `mock_table_client` - Mock Azure Table Storage client
- `mock_blob_client` - Mock Azure Blob Storage client
- `mock_queue_client` - Mock Azure Queue Storage client
- `mock_sql_connection` - Mock SQL database connection
- `sample_execution_log_data` - Sample test data
- `mock_jwt_token` - Mock JWT token for authentication tests

## Pipeline Integration

The Azure DevOps pipeline has been updated to include:

1. **Unit Tests**: Run during the build stage
2. **Integration Tests**: Run against the staging slot
3. **Coverage Reporting**: Generate coverage reports
4. **Test Results**: Publish test results and artifacts

## Migration from Old Tests

The following existing test files have been restructured:

### Migrated Files
- `test_auth_diagnosis.py` → `tests/integration/test_auth_integration.py`
- `test_apim_connectivity.py` → `tests/integration/test_apim_connectivity.py`
- `test_jwt_fix.py` → Integrated into `test_auth_integration.py`

### Files to Migrate
The following files in `scripts/` should be converted to proper unit/integration tests:

- `scripts/test_azure_auth.py`
- `scripts/test_migration.py`
- `scripts/test_token_validation.py`
- `scripts/test_enhanced_auth.py`
- And others...

## Writing New Tests

### Unit Test Example

```python
import pytest
from unittest.mock import Mock, patch
from shared.data_access import TableStorageRepository

class TestTableStorageRepository:
    @pytest.fixture
    def table_repo(self, mock_table_client):
        return TableStorageRepository("test_table")
    
    @pytest.mark.unit
    def test_insert_entity_success(self, table_repo, mock_table_client):
        entity = {"PartitionKey": "test_pk", "RowKey": "test_rk"}
        
        result = table_repo.insert_entity(entity)
        
        assert result is True
        mock_table_client.upsert_entity.assert_called_once_with(entity)
```

### Integration Test Example

```python
import pytest
import requests

class TestAPIIntegration:
    @pytest.mark.integration
    @pytest.mark.slow
    def test_health_endpoint(self):
        response = requests.get("http://localhost:7072/api/health")
        assert response.status_code in [200, 401, 403]
```

## Coverage Requirements

- **Minimum Coverage**: 80% (configured in pytest.ini)
- **Target Coverage**: 90%+ for critical modules
- **Reports**: HTML, XML, and terminal reports generated

## Best Practices

1. **Isolation**: Unit tests should not depend on external services
2. **Mocking**: Use mocks for Azure services, databases, and HTTP calls
3. **Fixtures**: Reuse common test data through fixtures
4. **Markers**: Use appropriate markers for test categorization
5. **Naming**: Follow the pattern `test_<function_name>_<scenario>`
6. **Documentation**: Include docstrings for test classes and complex tests

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure the project root is in Python path
2. **Mock Issues**: Check that mocks are properly configured in conftest.py
3. **Slow Tests**: Use `--fast` flag to skip slow integration tests
4. **Coverage Issues**: Check that all modules are included in coverage configuration

### Environment Variables

For integration tests, set these environment variables:

```bash
export IS_LOCAL_DEV=true
export AZURE_AD_TENANT_ID=test-tenant-id
export AZURE_AD_CLIENT_ID=test-client-id
export TEST_BASE_URL=http://localhost:7072
```

## Next Steps

1. **Complete Migration**: Move remaining test scripts to proper test structure
2. **Add More Unit Tests**: Increase coverage for API endpoints and repositories
3. **Performance Tests**: Add performance testing for critical paths
4. **End-to-End Tests**: Consider adding E2E tests for complete workflows
5. **Test Data Management**: Implement test data factories for complex scenarios
