# APIM-Integrated Deployment Guide

## Overview

This guide addresses deployment issues when the Azure Function App is configured to only accept requests through Azure API Management (APIM). The deployment pipeline has been updated to work with APIM instead of trying to access the function app directly.

## Problem Description

### **Error: 403 Forbidden**
```
Error 403 - Forbidden
The web app you have attempted to reach has blocked your access.
```

### **Root Cause**
- The Azure Function App is now configured to only accept requests through APIM
- Direct access to the function app endpoints is blocked
- The deployment pipeline was trying to access health endpoints directly
- IP restrictions or authentication middleware prevent direct access

## Solution: APIM-Only Deployment Verification

### 1. Updated Pipeline Configuration

The deployment pipeline now:
- Tests function app health through APIM endpoints
- Uses APIM subscription keys for authentication
- Accepts both HTTP 200 (success) and HTTP 401 (authentication required) as valid responses
- Provides detailed diagnostic information for troubleshooting

### 2. APIM Endpoints for Testing

**Health Check:**
```
**************************************/db/v1/health
```

**Info Endpoint:**
```
**************************************/db/v1/info
```

**Required Headers:**
```
Ocp-Apim-Subscription-Key: bd50cc1018444ae987a04c465534e428
Content-Type: application/json
```

### 3. Expected Responses

#### **Success (HTTP 200):**
```json
{
  "status": "healthy",
  "service": "atomsec-func-db",
  "environment": "production",
  "checks": {
    "table_storage": "connected",
    "sql_database": "connected",
    "service_bus": "connected"
  }
}
```

#### **Authentication Required (HTTP 401):**
```json
{
  "error": "Valid authorization token is required"
}
```

**Note:** HTTP 401 is considered a **successful response** because it means:
- APIM is working correctly
- The function app is accessible through APIM
- Authentication is properly enforced

## Deployment Pipeline Changes

### 1. Variables Added
```yaml
variables:
- name: APIM_BASE_URL
  value: '**************************************/db'
- name: APIM_SUBSCRIPTION_KEY
  value: 'bd50cc1018444ae987a04c465534e428'
```

### 2. Health Check Updated
```bash
# Test health endpoint through APIM
HEALTH_URL="$APIM_BASE_URL/v1/health"

# Make request through APIM with subscription key
HEALTH_RESPONSE=$(curl -s -w "%{http_code}" \
  -H "Ocp-Apim-Subscription-Key: $APIM_SUBSCRIPTION_KEY" \
  -H "Content-Type: application/json" \
  "$HEALTH_URL" -o /tmp/health_response 2>/dev/null || echo "000")
```

### 3. Success Criteria Updated
- HTTP 200: Perfect success
- HTTP 401: Expected authentication requirement (success)
- HTTP 403: Function app accessible but blocked (investigate)
- HTTP 500: Function app error (investigate)

## Troubleshooting Steps

### 1. Verify APIM Configuration
```bash
# Check if APIM is accessible
curl -I "**************************************/db/v1/info" \
  -H "Ocp-Apim-Subscription-Key: bd50cc1018444ae987a04c465534e428"
```

### 2. Check Function App Status
```bash
# Verify function app is running
az functionapp show \
  --name func-atomsec-dbconnect-dev \
  --resource-group atomsec-dev-data \
  --query "state"
```

### 3. Test APIM Endpoints
```bash
# Test health endpoint through APIM
curl -s "**************************************/db/v1/health" \
  -H "Ocp-Apim-Subscription-Key: bd50cc1018444ae987a04c465534e428" \
  -H "Content-Type: application/json"
```

### 4. Check Function App Logs
```bash
# Get function app logs
az functionapp log download \
  --name func-atomsec-dbconnect-dev \
  --resource-group atomsec-dev-data \
  --log-file deployment-logs.zip
```

## Common Issues and Solutions

### Issue 1: APIM Subscription Key Invalid
**Symptoms:** HTTP 401 with "Invalid subscription key" message
**Solution:** Verify the subscription key in the pipeline variables

### Issue 2: Function App Not Responding
**Symptoms:** HTTP 500 or timeout errors
**Solution:** Check function app logs and restart if necessary

### Issue 3: CORS Issues
**Symptoms:** Preflight request failures
**Solution:** Verify CORS configuration in host.json

### Issue 4: Authentication Middleware Blocking
**Symptoms:** HTTP 403 errors
**Solution:** Check if authentication middleware is blocking health checks

## Environment Variables

### Required for APIM Integration
```bash
APIM_BASE_URL=**************************************/db
APIM_SUBSCRIPTION_KEY=bd50cc1018444ae987a04c465534e428
```

### Function App Settings
```bash
FUNCTIONS_WORKER_RUNTIME=python
FUNCTIONS_EXTENSION_VERSION=~4
AZURE_AD_CLIENT_ID=<your-client-id>
AZURE_AD_TENANT_ID=<your-tenant-id>
ENVIRONMENT=production
```

## Monitoring and Validation

### 1. Deployment Success Indicators
- ✅ Function app is running
- ✅ APIM endpoints are accessible
- ✅ Health check returns HTTP 200 or 401
- ✅ All critical configuration settings are present

### 2. Post-Deployment Validation
- Test frontend application connectivity
- Verify APIM routing is working
- Check function app logs for errors
- Monitor Application Insights (if configured)

## Next Steps

1. **Deploy the updated pipeline** with APIM integration
2. **Monitor deployment logs** for APIM connectivity
3. **Verify APIM endpoints** are working correctly
4. **Test frontend integration** through APIM
5. **Monitor function app logs** for any runtime issues

## Support

If issues persist after implementing these fixes:

1. Check Azure Function App logs in the Azure portal
2. Verify APIM configuration and subscription keys
3. Test APIM endpoints manually with subscription keys
4. Review network security groups and IP restrictions
5. Check if authentication middleware is blocking health checks
