# Python and Virtual Environment
.venv
.python_packages
__pycache__/
*.pyc
.pytest_cache

# Local development settings (NEVER commit these)
local.settings.json

# Conflicting modules
/asyncio/

# Azure Functions
!task_processor/function.json
!WrapperFunction/function.json

# Azure Storage Emulator
.azurite/
.azurite/__azurite_db_*.json
__blobstorage__/
__queuestorage__/

# VS Code settings
.vscode/*
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Project directories
certs/
static/
logs/

# Log files
*.log
/LogFiles/*
deployments/*

# Documentation files (uncomment to ignore)
# API_CONSOLIDATION_SUMMARY.md
# CONSOLIDATED_API_ENDPOINTS.md

# Local settings (uncomment to ignore)
local.settings.json
# config.py
AZURE_AD_AUTHENTICATION_MIGRATION.md
CONSOLIDATED_API_ENDPOINTS.md
DEPLOYMENT_GUIDE.md
requirements-dev.txt
CONSOLIDATED_API_ENDPOINTS.md
dbfunction.log
dbfunction.log
dbfunction.log
dbfunction.log
shared/__pycache__/__init__.cpython-312.pyc
shared/__pycache__/azure_services.cpython-312.pyc
shared/__pycache__/common.cpython-312.pyc
shared/__pycache__/config.cpython-312.pyc
shared/__pycache__/__init__.cpython-312.pyc
shared/__pycache__/azure_services.cpython-312.pyc
shared/__pycache__/common.cpython-312.pyc
shared/__pycache__/config.cpython-312.pyc
shared/__pycache__/data_access.cpython-312.pyc
check_deployment.py
debug_deployment.py
deploy_hotfix.sh
verify_hotfix.py
__pycache__/function_app.cpython-312.pyc
shared/__pycache__/__init__.cpython-312.pyc
shared/__pycache__/azure_services.cpython-312.pyc
shared/__pycache__/common.cpython-312.pyc
shared/__pycache__/config.cpython-312.pyc
shared/__pycache__/data_access.cpython-312.pyc
backendfunction.log
dbfunction.log
function-app-logs.zip
shared/__pycache__/data_access.cpython-312.pyc
