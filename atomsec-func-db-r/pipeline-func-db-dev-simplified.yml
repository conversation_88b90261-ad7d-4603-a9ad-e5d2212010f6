# Azure DevOps Pipeline for AtomSec DB Function App (dev) - SIMPLIFIED
# This pipeline fixes the circular dependency issues and streamlines deployment
#
# ===========================================================================================
# PYTHON WORKER RUNTIME FIX - Addresses "Function host is not running" Error
# ===========================================================================================

trigger:
- dev
- dev-db

pool:
  vmImage: ubuntu-latest

variables:
- group: vg-atomsec-db-dev  # Variable group for dev environment
- name: APIM_BASE_URL
  value: '**************************************/db'
- name: APIM_SUBSCRIPTION_KEY
  value: 'bd50cc1018444ae987a04c465534e428'
- name: APIM_ONLY_ACCESS
  value: 'true'

stages:
- stage: Build
  displayName: 'Build and Package'
  jobs:
  - job: Build
    displayName: 'Build Function App'
    steps:
    - task: UsePythonVersion@0
      inputs:
        versionSpec: '3.11'
      displayName: 'Set up Python 3.11'

    # Clean up Python bytecode files
    - script: |
        echo "🧹 Cleaning up Python bytecode files..."
        find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
        find . -type f -name "*.pyc" -delete 2>/dev/null || true
        find . -type f -name "*.pyo" -delete 2>/dev/null || true
        find . -type f -name "*.pyd" -delete 2>/dev/null || true
        echo "✅ Python bytecode cleanup completed"
      displayName: 'Clean up Python bytecode files'

    # Setup Azure CLI and Functions Tools
    - task: AzureCLI@2
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo 'Installing Azure Functions Core Tools...'
          npm config set timeout 300000
          npm config set registry https://registry.npmjs.org/
          timeout 300 npm install -g azure-functions-core-tools@4 --unsafe-perm true --verbose
          
          if command -v func &> /dev/null; then
            echo "✅ Azure Functions Core Tools installed successfully"
            func --version
          else
            echo "❌ Azure Functions Core Tools installation failed"
            exit 1
          fi
      displayName: 'Setup Azure CLI and Functions Tools'

    # Install dependencies with Python worker runtime
    - script: |
        python --version
        python -m pip install --upgrade pip setuptools wheel
        
        # Install system dependencies
        sudo apt-get update
        sudo apt-get install -y build-essential python3-dev pkg-config curl gnupg2
        
        # Install ODBC driver
        curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | sudo gpg --dearmor -o /usr/share/keyrings/microsoft-archive-keyring.gpg
        echo "deb [arch=amd64,arm64,armhf signed-by=/usr/share/keyrings/microsoft-archive-keyring.gpg] https://packages.microsoft.com/ubuntu/$(lsb_release -rs)/prod $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/microsoft-prod.list
        sudo apt-get update
        sudo ACCEPT_EULA=Y apt-get install -y msodbcsql18 unixodbc-dev
        
        # Create .python_packages directory
        mkdir -p .python_packages/lib/site-packages
        
        # Install Azure Functions Python worker with retry
        for attempt in 1 2 3; do
          echo "Azure Functions worker installation attempt $attempt/3..."
          pip install azure-functions==1.17.0 -t .python_packages/lib/site-packages/ --no-deps --force-reinstall
          
          if [ -f ".python_packages/lib/site-packages/azure/functions/__init__.py" ]; then
            echo "✅ Azure Functions worker runtime installed successfully"
            break
          elif [ $attempt -eq 3 ]; then
            echo "❌ CRITICAL: Azure Functions worker installation failed after 3 attempts!"
            exit 1
          fi
          sleep 5
        done
        
        # Install other dependencies
        pip install typing-extensions azure-core -t .python_packages/lib/site-packages/
        pip install -r requirements.txt -t . --ignore-installed azure-functions
        
        echo "✅ Dependencies installation completed"
      displayName: 'Install dependencies with Python worker runtime'

    # Verify package structure
    - script: |
        if [ ! -f "function_app.py" ]; then
          echo "❌ ERROR: function_app.py not found!"
          exit 1
        fi
        
        python -c "import function_app; print('✅ function_app.py imports successfully')"
        echo "✅ Package verification completed successfully"
      displayName: 'Verify package structure'

    # Prepare deployment package
    - script: |
        DEPLOY_DIR="$(Build.ArtifactStagingDirectory)/deploy"
        mkdir -p "$DEPLOY_DIR"
        
        # Copy essential files
        cp -r api/ shared/ repositories/ home/ "$DEPLOY_DIR/" 2>/dev/null || echo "Some directories not found"
        cp -r .python_packages/ "$DEPLOY_DIR/" 2>/dev/null || echo "Failed to copy .python_packages"
        cp function_app.py host.json requirements.txt .funcignore "$DEPLOY_DIR/" 2>/dev/null || echo "Some files not found"
        
        # Ensure local.settings.json is NOT copied
        if [ -f "$DEPLOY_DIR/local.settings.json" ]; then
          rm -f "$DEPLOY_DIR/local.settings.json"
        fi
        
        echo "✅ Deployment directory prepared successfully"
      displayName: 'Prepare deployment package'

    # Archive deployment package
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/deploy'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
        replaceExistingArchive: true
      displayName: 'Archive deployment package'

    # Verify deployment package
    - script: |
        if unzip -l "$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip" | grep -q "local.settings.json"; then
          echo "❌ CRITICAL ERROR: local.settings.json is in deployment package!"
          exit 1
        fi
        
        if ! unzip -l "$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip" | grep -q "\.python_packages"; then
          echo "❌ CRITICAL ERROR: .python_packages missing from deployment package!"
          exit 1
        fi
        
        echo "✅ Deployment package verification completed"
      displayName: 'Verify deployment package'

    # Publish artifacts
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish deployment artifacts'

- stage: DeployToStaging
  displayName: 'Deploy to Staging Slot'
  dependsOn: Build
  jobs:
  - job: DeployStagingJob
    displayName: 'Deploy to Staging Slot'
    steps:
    - task: DownloadBuildArtifacts@1
      inputs:
        buildType: 'current'
        downloadType: 'single'
        artifactName: 'drop'
        downloadPath: '$(System.ArtifactsDirectory)'
      displayName: 'Download build artifacts'

    # Create/verify staging slot
    - task: AzureCLI@2
      displayName: 'Create/verify staging slot'
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          slot_exists=$(az webapp deployment slot list --name $(DB_FUNCTION_APP_NAME) --resource-group $(DB_RESOURCE_GROUP) --query "[?name=='stage']" --output tsv)
          
          if [ -z "$slot_exists" ]; then
            echo "Creating staging slot..."
            az webapp deployment slot create \
              --name $(DB_FUNCTION_APP_NAME) \
              --resource-group $(DB_RESOURCE_GROUP) \
              --slot stage \
              --configuration-source $(DB_FUNCTION_APP_NAME)
          fi
          
          echo "✅ Staging slot ready"
      displayName: 'Create/verify staging slot'

    # Deploy to staging
    - task: AzureCLI@2
      displayName: 'Deploy to staging slot'
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo "Deploying to staging slot..."
          az webapp deployment source config-zip \
            --resource-group $(DB_RESOURCE_GROUP) \
            --name $(DB_FUNCTION_APP_NAME) \
            --slot stage \
            --src "$(System.ArtifactsDirectory)/drop/build-$(Build.BuildNumber).zip" \
            --timeout 2400
          
          if [ $? -eq 0 ]; then
            echo "✅ Staging deployment successful"
          else
            echo "❌ Staging deployment failed"
            exit 1
          fi
      displayName: 'Deploy to staging slot'

    # Start staging slot
    - task: AzureAppServiceManage@0
      displayName: 'Start staging slot'
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        Action: 'Start Azure App Service'
        WebAppName: '$(DB_FUNCTION_APP_NAME)'
        ResourceGroupName: '$(DB_RESOURCE_GROUP)'
        SpecifySlotOrASE: true
        Slot: 'stage'

    # Validate staging slot
    - task: AzureCLI@2
      displayName: 'Validate staging slot'
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo "Waiting for staging slot to start up..."
          sleep 60
          
          staging_host=$(az webapp show --name $(DB_FUNCTION_APP_NAME) --resource-group $(DB_RESOURCE_GROUP) --slot stage --query "defaultHostName" -o tsv)
          staging_url="https://$staging_host"
          
          echo "Testing staging slot connectivity..."
          if curl -sf --max-time 30 "$staging_url" > /dev/null 2>&1; then
            echo "✅ Staging slot connectivity: PASS"
          else
            echo "❌ Staging slot connectivity: FAIL"
            exit 1
          fi
          
          # Test health endpoint
          health_endpoint="/api/db/health"
          health_response=$(curl -s -w "%{http_code}" "$staging_url$health_endpoint" -o /dev/null --max-time 30 || echo "000")
          
          if [ "$health_response" = "200" ] || [ "$health_response" = "403" ] || [ "$health_response" = "401" ]; then
            echo "✅ Staging slot health check: PASS (HTTP $health_response)"
          else
            echo "❌ Staging slot health check: FAIL (HTTP $health_response)"
            exit 1
          fi
          
          echo "✅ Staging slot validation completed successfully"
      displayName: 'Validate staging slot'

- stage: DeployToProduction
  displayName: 'Deploy to Production'
  dependsOn: DeployToStaging
  condition: and(succeeded(), eq(dependencies.DeployToStaging.result, 'Succeeded'))
  jobs:
  - job: DeployProductionJob
    displayName: 'Swap staging to production'
    steps:
    # Pre-deployment validation
    - task: AzureCLI@2
      displayName: 'Pre-deployment validation'
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo "Verifying staging slot health before production deployment..."
          
          staging_host=$(az webapp show --name $(DB_FUNCTION_APP_NAME) --resource-group $(DB_RESOURCE_GROUP) --slot stage --query "defaultHostName" -o tsv)
          staging_url="https://$staging_host"
          
          health_endpoint="/api/db/health"
          health_response=$(curl -s -w "%{http_code}" "$staging_url$health_endpoint" -o /dev/null --max-time 30 || echo "000")
          
          if [ "$health_response" = "200" ] || [ "$health_response" = "403" ] || [ "$health_response" = "401" ]; then
            echo "✅ Staging slot health check passed"
          else
            echo "❌ Staging slot health check failed - aborting production deployment"
            exit 1
          fi
      displayName: 'Pre-deployment validation'

    # Swap staging to production
    - task: AzureAppServiceManage@0
      displayName: 'Swap staging to production'
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        Action: 'Swap Slots'
        WebAppName: '$(DB_FUNCTION_APP_NAME)'
        ResourceGroupName: '$(DB_RESOURCE_GROUP)'
        SourceSlot: 'stage'
        TargetSlot: 'production'

    # Configure production slot
    - task: AzureCLI@2
      displayName: 'Configure production slot'
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo "Configuring production slot..."
          
          RG="$(DB_RESOURCE_GROUP)"
          APP="$(DB_FUNCTION_APP_NAME)"
          
          # Set core Functions settings
          az webapp config appsettings set -g "$RG" -n "$APP" --settings \
            "FUNCTIONS_WORKER_RUNTIME=python" \
            "FUNCTIONS_EXTENSION_VERSION=~4" \
            "WEBSITE_RUN_FROM_PACKAGE=1" \
            "AzureWebJobsFeatureFlags=EnableWorkerIndexing" \
            "PYTHON_ISOLATE_WORKER_DEPENDENCIES=0"
          
          # Set AzureWebJobsStorage settings
          az webapp config appsettings set -g "$RG" -n "$APP" --settings \
            "AzureWebJobsStorage__accountName=statomsecdbconnectdev02" \
            "AzureWebJobsStorage__blobServiceUri=https://statomsecdbconnectdev02.blob.core.windows.net" \
            "AzureWebJobsStorage__queueServiceUri=https://statomsecdbconnectdev02.queue.core.windows.net" \
            "AzureWebJobsStorage__tableServiceUri=https://statomsecdbconnectdev02.table.core.windows.net" \
            "AzureWebJobsStorage__credential=managedidentity"
          
          # Set application settings
          az webapp config appsettings set -g "$RG" -n "$APP" --settings \
            "KEY_VAULT_NAME=$(KEY_VAULT_NAME)" \
            "KEY_VAULT_URL=$(KEY_VAULT_URL)" \
            "ENVIRONMENT=$(ENVIRONMENT)" \
            "SFDC_SERVICE_URL=$(SFDC_SERVICE_URL)" \
            "FRONTEND_URL=$(FRONTEND_URL)"
          
          echo "Restarting production slot..."
          az webapp restart --name "$APP" --resource-group "$RG"
          sleep 30
          
          echo "✅ Production slot configuration completed"
      displayName: 'Configure production slot'

    # Validate production deployment
    - task: AzureCLI@2
      displayName: 'Validate production deployment'
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo "Validating production deployment..."
          
          RG="$(DB_RESOURCE_GROUP)"
          APP="$(DB_FUNCTION_APP_NAME)"
          
          # Wait for production to restart
          sleep 60
          
          # Verify app settings
          production_settings=$(az webapp config appsettings list --name "$APP" --resource-group "$RG" --output json)
          
          runtime_check=$(echo "$production_settings" | jq -r ".[] | select(.name == \"FUNCTIONS_WORKER_RUNTIME\") | .value // \"NOT_SET\"")
          version_check=$(echo "$production_settings" | jq -r ".[] | select(.name == \"FUNCTIONS_EXTENSION_VERSION\") | .value // \"NOT_SET\"")
          package_check=$(echo "$production_settings" | jq -r ".[] | select(.name == \"WEBSITE_RUN_FROM_PACKAGE\") | .value // \"NOT_SET\"")
          
          if [ "$runtime_check" != "python" ] || [ "$version_check" != "~4" ] || [ "$package_check" != "1" ]; then
            echo "❌ Production slot app settings validation failed!"
            exit 1
          fi
          
          # Test production functionality
          production_host=$(az webapp show --name "$APP" --resource-group "$RG" --query "defaultHostName" -o tsv)
          production_url="https://$production_host"
          
          health_status=$(curl -s -w "%{http_code}" "$production_url/api/db/health" -o /dev/null --max-time 30 || echo "000")
          
          if [ "$health_status" = "200" ] || [ "$health_status" = "403" ] || [ "$health_status" = "401" ]; then
            echo "✅ Production deployment validation successful"
          else
            echo "❌ Production deployment validation failed (HTTP $health_status)"
            exit 1
          fi
      displayName: 'Validate production deployment'

- stage: FinalVerification
  displayName: 'Final Verification and Cleanup'
  dependsOn: DeployToProduction
  condition: and(succeeded(), eq(dependencies.DeployToProduction.result, 'Succeeded'))
  jobs:
  - job: FinalVerificationJob
    displayName: 'Final verification and cleanup'
    steps:
    # Cleanup staging slot
    - task: AzureCLI@2
      displayName: 'Cleanup staging slot'
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo "Stopping staging slot..."
          az webapp stop --name $(DB_FUNCTION_APP_NAME) --resource-group $(DB_RESOURCE_GROUP) --slot stage || echo "Staging slot stop completed"
          
          echo "✅ Deployment pipeline completed successfully"
      displayName: 'Cleanup staging slot'

    # Final production verification
    - task: AzureCLI@2
      displayName: 'Final production verification'
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo "Running final production verification..."
          
          production_host=$(az webapp show --name $(DB_FUNCTION_APP_NAME) --resource-group $(DB_RESOURCE_GROUP) --query "defaultHostName" -o tsv)
          production_url="https://$production_host"
          
          health_endpoint="/api/db/health"
          health_response=$(curl -s -w "%{http_code}" "$production_url$health_endpoint" -o /tmp/health_response --max-time 30 || echo "000")
          
          if [ "$health_response" = "200" ] || [ "$health_response" = "403" ] || [ "$health_response" = "401" ]; then
            echo "✅ Final verification successful - function app is working!"
            echo "Health response: $(cat /tmp/health_response 2>/dev/null || echo 'No response body')"
          else
            echo "❌ Final verification failed (HTTP $health_response)"
            exit 1
          fi
          
          echo ""
          echo "🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!"
          echo "✅ Python worker runtime issue resolved"
          echo "✅ Function app running and responding"
          echo "✅ Zero-downtime deployment completed"
      displayName: 'Final production verification'
