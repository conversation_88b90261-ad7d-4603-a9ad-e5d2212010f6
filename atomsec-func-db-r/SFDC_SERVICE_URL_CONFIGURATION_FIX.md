# SFDC_SERVICE_URL Configuration Fix

## Problem Description

The DB Function App was incorrectly calling `http://localhost:7071` for SFDC service communication instead of the Azure-hosted SFDC service. This was causing connection errors in the Azure environment.

**Error in logs:**
```
[Information] Making POST request to SFDC service: http://localhost:7071/api/integration/test-connection
[Error] Connection error calling SFDC service endpoint: integration/test-connection
```

## Root Cause

The `SFDC_SERVICE_URL` environment variable was not being configured during Azure deployment, causing the application to fall back to the localhost default value.

## Solution Implemented

### 1. Updated Variable Group (`atomsec-dev-vg.json`)

Added the `SFDC_SERVICE_URL` configuration:

```json
"SFDC_SERVICE_URL": {
  "value": "https://func-atomsec-sfdc-dev.azurewebsites.net",
  "description": "SFDC Function App service URL for service-to-service communication"
}
```

### 2. Updated Main Pipeline (`pipeline-func-db-dev.yml`)

Added `SFDC_SERVICE_URL` and `FRONTEND_URL` to the application settings configuration:

```bash
az functionapp config appsettings set \
  --name $(FUNCTION_APP_NAME) \
  --resource-group $(RESOURCE_GROUP) \
  --settings \
    # ... existing settings ...
    SFDC_SERVICE_URL="$(SFDC_SERVICE_URL)" \
    FRONTEND_URL="$(FRONTEND_URL)"
```

### 3. Updated Fast Pipeline (`pipeline-func-db-dev-fast.yml`)

Added the same configuration to the fast deployment pipeline for consistency.

## Configuration Details

- **SFDC Service URL**: `https://func-atomsec-sfdc-dev.azurewebsites.net`
- **Environment**: Development (`dev`)
- **Resource Group**: `atomsec-dev-backend` (for SFDC service)
- **Resource Group**: `atomsec-dev-data` (for DB service)

## How It Works

1. **Local Development**: Uses `http://localhost:7071` from `local.settings.json`
2. **Azure Production**: Uses `https://func-atomsec-sfdc-dev02.azurewebsites.net` from environment variables
3. **Service-to-Service Communication**: Direct communication between Azure Function Apps (not through APIM)

## Verification

After deployment, verify the configuration:

```bash
# Check current application settings
az functionapp config appsettings list \
  --name func-atomsec-dbconnect-dev02 \
  --resource-group atomsec-dev-data \
  --query "[?name=='SFDC_SERVICE_URL']"
```

Expected output:
```json
[
  {
    "name": "SFDC_SERVICE_URL",
    "value": "https://func-atomsec-sfdc-dev.azurewebsites.net"
  }
]
```

## Next Steps

1. **Deploy the updated pipeline** to apply the configuration changes
2. **Monitor logs** to ensure SFDC service calls are now going to the correct Azure URL
3. **Test the integration endpoint** to verify successful communication

## Files Modified

- `atomsec-func-db-r/variable-groups/atomsec-dev-vg.json`
- `atomsec-func-db-r/pipeline-func-db-dev.yml`
- `atomsec-func-db-r/pipeline-func-db-dev-fast.yml`

## Notes

- The `IS_LOCAL_DEV` setting is already set to `false` in the variable group
- The SFDC service client code already handles the environment detection correctly
- This fix ensures Azure-hosted services communicate with each other directly
- No code changes were required - only configuration updates
