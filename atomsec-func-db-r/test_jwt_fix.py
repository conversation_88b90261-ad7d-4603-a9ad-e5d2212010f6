#!/usr/bin/env python3
"""
Test script to verify the JWT token validation fix
"""

import os
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_jwt_validation():
    """Test JWT validation with the sample token from the logs"""
    
    # Sample Azure AD token from the logs (this is the actual token causing the issue)
    test_token = "eyJ0eXAiOiJKV1QiLCJub25jZSI6IklXU2w5aHk1SDRfZmNTT1hIU2FnaVNWcG13dE9QUFlZYnhNeHpnVWh3bGsiLCJhbGciOiJSUzI1NiIsIng1dCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSIsImtpZCI6IkpZaEFjVFBNWl9MWDZEQmxPV1E3SG4wTmVYRSJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ocpJMapexF5NcBQNve_xTSgyZbr7brWZcdSS82wCv_TnKV_SwcVa3IfGDxwZYfKki0dwdfnikaj79AML7OOr5q25EWvj0jrud9i0hVvw7V7CjRkt60TgUex3WondgMnx1hjFBa9Gt-1XeHNMOZP69zHYIkN9_RTVLYfZDZMY5475CODYp78ZISxt0aADB3jBdyVH2Wj3cwL26BejRkq25xsD_bgunjpYPu3-dkWQ0fy49SFZtSXtSXwdRVDbTYObH6LhhrJSvoTR77rc0FVbqWeWaanhljq-5G_mxLgHRXCIqHcZgigZ1KsOaYit49JQ54X8PPvJNlNxUWAOXBwQ2g"
    
    # Set environment variables for testing
    os.environ["AZURE_AD_TENANT_ID"] = "41b676db-bf6f-46ae-a354-a83a1362533f"
    os.environ["AZURE_AD_CLIENT_ID"] = "2d313c1a-d62d-492c-869e-cf8cb9258204"
    os.environ["AZURE_AD_ADDITIONAL_CLIENT_IDS"] = "82e79715-7451-4680-bd1c-53453bfd45ea"
    os.environ["IS_LOCAL_DEV"] = "true"
    
    print("Testing Azure AD token validation...")
    print(f"Token length: {len(test_token)}")
    print(f"Environment: IS_LOCAL_DEV = {os.environ.get('IS_LOCAL_DEV')}")
    
    try:
        from shared.auth_utils import decode_azure_ad_token
        
        print("\nAttempting to decode Azure AD token...")
        payload = decode_azure_ad_token(test_token)
        
        if payload:
            print("✅ SUCCESS: Token validation successful!")
            print(f"Token payload keys: {list(payload.keys())}")
            print(f"Token client ID: {payload.get('appid', payload.get('aud'))}")
            print(f"Token tenant ID: {payload.get('tid')}")
            print(f"User email: {payload.get('upn', payload.get('email'))}")
            return True
        else:
            print("❌ ERROR: Token validation failed")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: Token validation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_detection():
    """Test environment detection"""
    print("\nTesting environment detection...")
    
    try:
        from shared.azure_services import is_local_dev
        
        is_dev = is_local_dev()
        print(f"is_local_dev() result: {is_dev}")
        
        # Log relevant environment variables
        env_vars = ['WEBSITE_SITE_NAME', 'WEBSITE_INSTANCE_ID', 'FUNCTIONS_WORKER_RUNTIME',
                    'FUNCTIONS_WORKER_RUNTIME_VERSION', 'WEBSITE_HOSTNAME', 'IS_LOCAL_DEV']
        for var in env_vars:
            value = os.environ.get(var)
            print(f"Environment variable {var}: {value}")
            
        return True
        
    except Exception as e:
        print(f"❌ ERROR: Environment detection error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("JWT Token Validation Fix Test")
    print("=" * 50)
    
    success1 = test_environment_detection()
    success2 = test_jwt_validation()
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print(f"Environment detection: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"JWT token validation: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("\n🎉 All tests passed! The JWT validation fix should work.")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
    
    sys.exit(0 if (success1 and success2) else 1)
