"""
Azure AD Authentication Endpoints (DB Service)

Minimal implementation to support local development without relying on the
SFDC service. In production, APIM/Easy Auth should front authentication.

Endpoints (behind routePrefix "/api/db"):
  - GET  /auth/azure/login      → 302 redirect to Azure authorize endpoint
  - GET  /auth/azure/callback   → exchanges code, redirects to frontend /auth-callback
  - GET  /auth/azure/me         → returns current user info using AAD token

Local setup requirements:
  - AZURE_AD_CLIENT_ID
  - AZURE_AD_CLIENT_SECRET
  - AZURE_AD_TENANT_ID
  - AZURE_AD_REDIRECT_URI=http://localhost:7072/api/db/auth/azure/callback
  - FRONTEND_URL=http://localhost:3000
"""

from __future__ import annotations

import json
import logging
import os
import secrets
import urllib.parse
from datetime import datetime, timedelta, timezone
from typing import Dict

import azure.functions as func
import requests

from shared.cors_middleware import add_cors_headers
from shared.config import get_azure_ad_config, get_frontend_url


logger = logging.getLogger(__name__)
bp = func.Blueprint()

# In-memory state for CSRF (dev only)
_STATE_TTL_MIN = 30
_STATE: Dict[str, datetime] = {}


def _cleanup_state() -> None:
    now = datetime.now(timezone.utc)
    expired = [k for k, v in _STATE.items() if v <= now]
    for k in expired:
        _STATE.pop(k, None)


@bp.route(route="auth/azure/login", methods=["GET"])  # GET /api/db/auth/azure/login
def azure_login(req: func.HttpRequest) -> func.HttpResponse:
    try:
        _cleanup_state()
        state = secrets.token_urlsafe(24)
        _STATE[state] = datetime.now(timezone.utc) + timedelta(minutes=_STATE_TTL_MIN)

        azure = get_azure_ad_config()
        authority = azure.get("authority")
        client_id = azure.get("client_id")
        redirect_uri = azure.get("redirect_uri")
        scope = azure.get("scope", "openid profile email")

        # Optional domain hint from discover flow
        domain_hint = req.params.get("domain_hint")

        auth_url = (
            f"{authority}/oauth2/v2.0/authorize?" + urllib.parse.urlencode({
                "client_id": client_id,
                "response_type": "code",
                "redirect_uri": redirect_uri,
                "scope": scope,
                "state": state,
                "response_mode": "query",
                "prompt": "select_account",
                **({"domain_hint": domain_hint} if domain_hint else {}),
            })
        )

        resp = func.HttpResponse(status_code=302)
        resp.headers["Location"] = auth_url
        return add_cors_headers(req, resp)
    except Exception as exc:
        logger.error(f"azure_login error: {exc}")
        return add_cors_headers(req, func.HttpResponse(
            json.dumps({"success": False, "error": "login_init_failed"}),
            mimetype="application/json",
            status_code=500,
        ))


@bp.route(route="auth/azure/callback", methods=["GET"])  # GET /api/db/auth/azure/callback
def azure_callback(req: func.HttpRequest) -> func.HttpResponse:
    try:
        code = req.params.get("code")
        state = req.params.get("state")
        if not code or not state:
            return func.HttpResponse("Missing code/state", status_code=400)

        if state not in _STATE:
            # dev leniency: proceed but log
            logger.warning("State not found or expired; continuing in dev mode")
        else:
            _STATE.pop(state, None)

        azure = get_azure_ad_config()
        authority = azure.get("authority")
        client_id = azure.get("client_id")
        client_secret = azure.get("client_secret")
        redirect_uri = azure.get("redirect_uri")

        token_url = f"{authority}/oauth2/v2.0/token"
        data = {
            "client_id": client_id,
            "client_secret": client_secret,
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": redirect_uri,
        }
        token_res = requests.post(token_url, data=data, timeout=30)
        if token_res.status_code != 200:
            logger.error(f"Token exchange failed: {token_res.text[:200]}")
            return func.HttpResponse("Token exchange failed", status_code=500)

        token_json = token_res.json()
        access_token = token_json.get("access_token")
        id_token = token_json.get("id_token")

        # Fetch profile from Graph using access token (dev only)
        email = None
        name = None
        if access_token:
            graph_res = requests.get(
                "https://graph.microsoft.com/v1.0/me",
                headers={"Authorization": f"Bearer {access_token}"},
                timeout=30,
            )
            if graph_res.status_code == 200:
                user = graph_res.json()
                email = user.get("userPrincipalName") or user.get("mail")
                name = user.get("displayName")

        frontend = get_frontend_url()
        redirect = (
            f"{frontend}/auth-callback?"
            + urllib.parse.urlencode({
                "access_token": access_token or id_token or "",
                "refresh_token": token_json.get("refresh_token", ""),
                "email": email or "",
                "name": name or "",
            })
        )

        resp = func.HttpResponse(status_code=302)
        resp.headers["Location"] = redirect
        return add_cors_headers(req, resp)
    except Exception as exc:
        logger.error(f"azure_callback error: {exc}")
        return add_cors_headers(req, func.HttpResponse(
            json.dumps({"success": False, "error": "callback_failed"}),
            mimetype="application/json",
            status_code=500,
        ))


@bp.route(route="auth/azure/me", methods=["GET"])  # GET /api/db/auth/azure/me
def azure_me(req: func.HttpRequest) -> func.HttpResponse:
    try:
        auth = req.headers.get("Authorization", "")
        if not auth.startswith("Bearer "):
            return add_cors_headers(req, func.HttpResponse(
                json.dumps({"success": False, "error": "unauthorized"}),
                mimetype="application/json",
                status_code=401,
            ))

        token = auth.split(" ", 1)[1]
        # Dev-only: call Graph /me to fetch profile
        user = None
        try:
            graph_res = requests.get(
                "https://graph.microsoft.com/v1.0/me",
                headers={"Authorization": f"Bearer {token}"},
                timeout=20,
            )
            if graph_res.status_code == 200:
                user = graph_res.json()
        except Exception:
            user = None

        data = {
            "email": (user or {}).get("userPrincipalName") or (user or {}).get("mail") or "",
            "name": (user or {}).get("displayName") or "",
            "accessToken": token,
            "roles": [],
            "isAdmin": False,
        }
        return add_cors_headers(req, func.HttpResponse(
            json.dumps({"success": True, "data": data}),
            mimetype="application/json",
            status_code=200,
        ))
    except Exception as exc:
        logger.error(f"azure_me error: {exc}")
        return add_cors_headers(req, func.HttpResponse(
            json.dumps({"success": False, "error": "me_failed"}),
            mimetype="application/json",
            status_code=500,
        ))


