"""
SSO Discovery Endpoints

This module implements a lightweight SSO discovery API to support email-domain
based routing to the appropriate identity provider. The goal is to enable the
frontend to:

1) Ask the user for their email
2) Call /v1/sso/discover?email=... to determine the correct login path
3) Redirect the browser to the platform auth endpoint with domain hints when possible

Notes:
- We do NOT create or modify Azure resources here. Architects can wire the
  correct multi-tenant or per-tenant configuration in App Service and APIM.
- We only return metadata the UI needs to build the redirect URL.
"""

import json
import logging
import re
from typing import Dict, Optional

import azure.functions as func

from shared.azure_services import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository


logger = logging.getLogger(__name__)


# Lazy repos
_org_table_repo: Optional[TableStorageRepository] = None
_org_sql_repo: Optional[SqlDatabaseRepository] = None


def get_org_table_repo() -> Optional[TableStorageRepository]:
    global _org_table_repo
    if _org_table_repo is None:
        try:
            _org_table_repo = TableStorageRepository(table_name="Organizations")
            logger.info("Initialized Organizations table repository for SSO discovery")
        except Exception as exc:
            logger.error(f"Failed to init Organizations table repo: {exc}")
            _org_table_repo = None
    return _org_table_repo


def get_org_sql_repo() -> Optional[SqlDatabaseRepository]:
    global _org_sql_repo
    if _org_sql_repo is None and not is_local_dev():
        try:
            _org_sql_repo = SqlDatabaseRepository(table_name="UserAccount")
            logger.info("Initialized SQL repo for SSO discovery")
        except Exception as exc:
            logger.error(f"Failed to init SQL repo: {exc}")
            _org_sql_repo = None
    return _org_sql_repo


def _extract_domain(email: str) -> Optional[str]:
    if not email:
        return None
    email = email.strip().lower()
    if not re.match(r"^[^@]+@[^@]+\.[^@]+$", email):
        return None
    return email.split("@")[-1]


def _find_org_by_domain(domain: str) -> Optional[Dict[str, str]]:
    """Look up an organization by its email domain.

    Returns a dict with at least { 'domain': str, 'name': str } when found.
    """
    try:
        if is_local_dev():
            repo = get_org_table_repo()
            if not repo:
                return None
            filter_query = "PartitionKey eq 'organization' and Domain eq '{}'".format(domain)
            entities = list(repo.query_entities(filter_query))
            if not entities:
                return None
            entity = entities[0]
            return {
                "id": entity.get("RowKey"),
                "name": entity.get("Name", ""),
                "domain": entity.get("Domain", domain),
            }
        else:
            repo = get_org_sql_repo()
            if not repo:
                return None
            query = "SELECT TOP 1 Id, Name, Domain FROM UserAccount WHERE Domain = ?"
            results = repo.execute_query(query, (domain,))
            if not results:
                return None
            row = results[0]
            return {"id": row[0], "name": row[1], "domain": row[2]}
    except Exception as exc:
        logger.error(f"Org discovery failed for domain {domain}: {exc}")
        return None


bp = func.Blueprint()


@bp.route(route="sso/discover", methods=["GET"])
def sso_discover(req: func.HttpRequest) -> func.HttpResponse:
    """Discover the SSO provider for a given email.

    Query params:
      - email: user email address

    Response:
      {
        success: true,
        data: {
          provider: "aad" | "atomsec",
          domain: string,               # parsed domain
          orgName?: string,             # if known
          loginStrategy: "platform" | "backend",
          hints: { domain_hint?: string }
        }
      }
    """
    email = req.params.get("email", "")
    domain = _extract_domain(email)

    if not domain:
        return func.HttpResponse(
            json.dumps({"success": False, "error": "invalid_email"}),
            mimetype="application/json",
            status_code=400,
        )

    org = _find_org_by_domain(domain)

    # For now, we only support Azure AD via platform auth (EasyAuth). If an org
    # record exists for the domain, we'll provide domain_hint to optimize the flow.
    # Otherwise, we still default to Azure AD without hints (acts like AtomSec default).
    provider = "aad"
    login_strategy = "platform"  # use /.auth/login/aad on the frontend host
    hints: Dict[str, str] = {"domain_hint": domain}

    response = {
        "success": True,
        "data": {
            "provider": provider,
            "domain": domain,
            "orgName": org.get("name") if org else None,
            "loginStrategy": login_strategy,
            "hints": hints,
        },
    }

    return func.HttpResponse(
        json.dumps(response), mimetype="application/json", status_code=200
    )


