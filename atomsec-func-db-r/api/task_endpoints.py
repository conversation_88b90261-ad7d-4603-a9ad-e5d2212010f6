"""
Task Management Endpoints

This module provides task-related database operations for the AtomSec application.
Moved from atomsec-func-sfdc to centralize database operations.

Endpoints:
- GET /api/tasks - List tasks with optional filtering
- POST /api/tasks - Create new task
- GET /api/tasks/{id} - Get task by ID
- PUT /api/tasks/{id} - Update task status
- DELETE /api/tasks/{id} - Delete task
- GET /api/tasks/org/{org_id} - Get tasks by organization
"""

import logging
import json
import azure.functions as func
from datetime import datetime
from typing import Dict, List, Any, Optional

from shared.azure_services import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.database_models_new import Task
from shared.event_publisher import publish_task_event
# Authentication removed - APIM will handle authentication
from shared.task_status_service import get_task_status_service

logger = logging.getLogger(__name__)

# Global repository instances (lazy initialized)
_task_table_repo = None
_task_sql_repo = None


def get_task_table_repo() -> Optional[TableStorageRepository]:
    """Get task table repository for local development"""
    global _task_table_repo
    if _task_table_repo is None:
        try:
            _task_table_repo = TableStorageRepository(table_name="TaskStatus")
            logger.info("Initialized task table repository")
        except Exception as e:
            logger.error(f"Failed to initialize task table repository: {str(e)}")
            _task_table_repo = None
    return _task_table_repo


def get_task_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get task SQL repository for production"""
    global _task_sql_repo
    if _task_sql_repo is None and not is_local_dev():
        try:
            _task_sql_repo = SqlDatabaseRepository(table_name="TaskStatus")
            logger.info("Initialized task SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize task SQL repository: {str(e)}")
            _task_sql_repo = None
    return _task_sql_repo


def create_task(task_data: Dict[str, Any], is_internal: bool = False) -> Optional[str]:
    """
    Create a new task and enqueue it for processing
    Args:
        task_data: Task data dictionary
        is_internal: True if request is from SFDC processor (internal), False otherwise
    Returns:
        Task ID if created, None otherwise
    """
    try:
        from shared.background_processor import get_background_processor
        processor = get_background_processor()
        task_type = task_data.get('task_type')
        org_id = task_data.get('org_id')
        user_id = task_data.get('user_id')
        params = task_data.get('params') or task_data.get('data', {})
        priority = task_data.get('priority', 'medium')
        force = task_data.get('force', False)
        execution_log_id = task_data.get('execution_log_id')  # Extract execution_log_id from task_data

        # Only allow sfdc_authenticate unless internal call
        if not is_internal and task_type != 'sfdc_authenticate':
            logger.error(f"Forbidden attempt to enqueue task_type='{task_type}' for org_id='{org_id}', user_id='{user_id}'. Only 'sfdc_authenticate' is allowed via this endpoint.")
            return None

        if not task_type or not org_id or not user_id:
            logger.error(f"Missing required task data: task_type={task_type}, org_id={org_id}, user_id={user_id}")
            return None

        # Use background processor to enqueue the task
        task_id = processor.enqueue_task(
            task_type=task_type,
            org_id=org_id,
            user_id=user_id,
            params=params,
            priority=priority,
            force=force,
            execution_log_id=execution_log_id  # Pass the execution_log_id to the background processor
        )

        if task_id:
            logger.info(f"Successfully created task {task_id} via background processor")
            return task_id
        else:
            logger.error("Background processor failed to create task")
            return None

    except Exception as e:
        logger.error(f"Error creating task: {str(e)}")
        return None


def get_task_by_id(task_id: str) -> Optional[Dict[str, Any]]:
    """
    Get task by ID

    Args:
        task_id: Task ID

    Returns:
        Task data dictionary or None
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_task_table_repo()
            if not repo:
                logger.error("Task table repository not available")
                return None

            # Query all entities and filter by RowKey since we don't know the org_id
            # Azure Table Storage doesn't support startswith in OData queries
            entities = list(repo.query_entities())
            
            # Find the entity with matching RowKey (task_id)
            target_entity = None
            for entity in entities:
                if entity.get('RowKey') == task_id:
                    target_entity = entity
                    break

            if target_entity:
                return {
                    'task_id': target_entity.get('RowKey'),  # TaskId is stored as RowKey
                    'org_id': target_entity.get('OrgId'),
                    'task_type': target_entity.get('TaskType'),
                    'status': target_entity.get('Status'),
                    'priority': target_entity.get('Priority'),
                    'created_at': target_entity.get('CreatedAt'),
                    'created_by': target_entity.get('CreatedBy'),
                    'started_at': target_entity.get('StartedAt'),
                    'completed_at': target_entity.get('CompletedAt'),
                    'error_message': target_entity.get('ErrorMessage'),
                    'data': json.loads(target_entity.get('Params', '{}')) if target_entity.get('Params') else {}  # Params, not Data
                }
            else:
                logger.warning(f"Task not found: {task_id}")
                return None

        else:
            # Use SQL Database for production
            repo = get_task_sql_repo()
            if not repo:
                logger.error("Task SQL repository not available")
                return None

            query = """
            SELECT TaskId, OrgId, TaskType, Status, Priority, CreatedAt, CreatedBy,
                   StartedAt, CompletedAt, ErrorMessage, Data
            FROM TaskStatus
            WHERE TaskId = ?
            """

            results = repo.execute_query(query, (task_id,))

            if results:
                row = results[0]
                return {
                    'task_id': row[0],
                    'org_id': row[1],
                    'task_type': row[2],
                    'status': row[3],
                    'priority': row[4],
                    'created_at': row[5].isoformat() if row[5] else None,
                    'created_by': row[6],
                    'started_at': row[7].isoformat() if row[7] else None,
                    'completed_at': row[8].isoformat() if row[8] else None,
                    'error_message': row[9],
                    'data': json.loads(row[10]) if row[10] else {}
                }
            else:
                logger.warning(f"Task not found: {task_id}")
                return None

    except Exception as e:
        logger.error(f"Error getting task by ID: {str(e)}")
        return None


def get_tasks(org_id: Optional[str] = None, status: Optional[str] = None,
              task_type: Optional[str] = None, priority: Optional[str] = None,
              execution_log_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get tasks with optional filtering

    Args:
        org_id: Filter by organization ID
        status: Filter by status
        task_type: Filter by task type
        priority: Filter by priority
        execution_log_id: Filter by execution log ID

    Returns:
        List of task dictionaries
    """
    try:
        if is_local_dev():
            # Use TaskStatus table for local development (where tasks are actually stored)
            from shared.task_status_service import get_task_status_service
            task_status_service = get_task_status_service()

            # Get tasks using the task status service
            if org_id:
                all_tasks = task_status_service.get_tasks_by_org(org_id, include_completed=True, limit=1000)
            else:
                # For no org_id, get tasks for a dummy org (this case shouldn't happen in practice)
                all_tasks = []

            tasks = []
            for task in all_tasks:
                # Apply filters
                if org_id is not None and task.get('OrgId') != org_id:
                    continue
                # Case-insensitive status comparison
                if status and task.get('Status', '').lower() != status.lower():
                    continue
                if task_type and task.get('TaskType') != task_type:
                    continue
                # Case-insensitive priority comparison
                if priority and task.get('Priority', '').lower() != priority.lower():
                    continue
                if execution_log_id and task.get('ExecutionLogId') != execution_log_id:
                    continue

                tasks.append({
                    'task_id': task.get('TaskId'),
                    'org_id': task.get('OrgId'),
                    'task_type': task.get('TaskType'),
                    'status': task.get('Status'),
                    'priority': task.get('Priority'),
                    'created_at': task.get('CreatedAt'),
                    'created_by': task.get('UserId'),
                    'started_at': task.get('StartedAt'),
                    'completed_at': task.get('CompletedAt'),
                    'error_message': task.get('ErrorMessage'),
                    'execution_log_id': task.get('ExecutionLogId'),
                    'data': task.get('Params', {})
                })

            # Sort by created_at descending to get latest first
            tasks.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            return tasks

        else:
            # Use TaskStatus service for production (consistent with local dev)
            from shared.task_status_service import get_task_status_service
            task_status_service = get_task_status_service()

            # Get tasks using the task status service
            if org_id:
                all_tasks = task_status_service.get_tasks_by_org(org_id, include_completed=True, limit=1000)
            else:
                # For no org_id, get tasks for a dummy org (this case shouldn't happen in practice)
                all_tasks = []

            tasks = []
            for task in all_tasks:
                # Apply filters
                if org_id is not None and task.get('OrgId') != org_id:
                    continue
                # Case-insensitive status comparison
                if status and task.get('Status', '').lower() != status.lower():
                    continue
                if task_type and task.get('TaskType') != task_type:
                    continue
                # Case-insensitive priority comparison
                if priority and task.get('Priority', '').lower() != priority.lower():
                    continue
                if execution_log_id and task.get('ExecutionLogId') != execution_log_id:
                    continue

                tasks.append({
                    'task_id': task.get('TaskId'),
                    'org_id': task.get('OrgId'),
                    'task_type': task.get('TaskType'),
                    'status': task.get('Status'),
                    'priority': task.get('Priority'),
                    'created_at': task.get('CreatedAt'),
                    'created_by': task.get('UserId'),
                    'started_at': task.get('StartedAt'),
                    'completed_at': task.get('CompletedAt'),
                    'error_message': task.get('ErrorMessage'),
                    'execution_log_id': task.get('ExecutionLogId'),
                    'data': task.get('Params', {})
                })

            # Sort by created_at descending to get latest first
            tasks.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            return tasks

    except Exception as e:
        logger.error(f"Error getting tasks: {str(e)}")
        return []


def update_task_status(task_id: str, status: str, error_message: Optional[str] = None,
                      progress: Optional[int] = None, message: Optional[str] = None) -> bool:
    """
    Update task status

    Args:
        task_id: Task ID
        status: New status
        error_message: Error message if status is 'Failed'
        progress: Progress percentage (0-100)
        message: Status message

    Returns:
        True if successful, False otherwise
    """
    try:
        if is_local_dev():
            repo = get_task_table_repo()
            if not repo:
                logger.error("Task table repository not available")
                return False
            
            # Since we don't know the org_id, we need to query all entities and filter by RowKey
            # Azure Table Storage doesn't support startswith in OData queries
            entities = list(repo.query_entities())
            
            # Find the entity with matching RowKey (task_id)
            target_entity = None
            for entity in entities:
                if entity.get('RowKey') == task_id:
                    target_entity = entity
                    break
            
            if not target_entity:
                logger.warning(f"Task not found: {task_id}")
                return False
            
            # Update status and timestamps
            target_entity['Status'] = status
            if status == 'Running' and not target_entity.get('StartedAt'):
                target_entity['StartedAt'] = datetime.now().isoformat()
            elif status in ['Completed', 'Failed']:
                target_entity['CompletedAt'] = datetime.now().isoformat()
            if error_message:
                target_entity['ErrorMessage'] = error_message
            if progress is not None:
                target_entity['Progress'] = progress
            if message:
                target_entity['Message'] = message
            
            return repo.update_entity(target_entity)
        else:
            # Use TaskStatus service for production (consistent with local dev)
            from shared.task_status_service import get_task_status_service
            task_status_service = get_task_status_service()

            # Update task status using the task status service
            return task_status_service.update_task_status(
                task_id=task_id,
                status=status,
                progress=progress,
                message=message,
                error=error_message
            )

    except Exception as e:
        logger.error(f"Error updating task status: {str(e)}")
        return False


def get_latest_task_by_type(org_id: str, task_type: str, status: Optional[str] = None, execution_log_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Get the latest task of a specific type for an organization

    Args:
        org_id: Organization ID
        task_type: Task type
        status: Optional status filter
        execution_log_id: Optional execution log ID filter

    Returns:
        Task data dictionary or None
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_task_table_repo()
            if not repo:
                logger.error("Task table repository not available")
                return None

            # Build filter query - use correct PartitionKey format
            filter_parts = [f"PartitionKey eq 'task_{org_id}'", f"TaskType eq '{task_type}'"]
            
            if status:
                filter_parts.append(f"Status eq '{status}'")
            if execution_log_id:
                filter_parts.append(f"ExecutionLogId eq '{execution_log_id}'")
            
            filter_query = " and ".join(filter_parts)
            entities = list(repo.query_entities(filter_query))

            if entities:
                # Sort by CreatedAt descending and take the first one
                entities.sort(key=lambda x: x.get('CreatedAt', ''), reverse=True)
                entity = entities[0]
                return {
                    'task_id': entity.get('RowKey'),
                    'org_id': entity.get('OrgId'),
                    'task_type': entity.get('TaskType'),
                    'status': entity.get('Status'),
                    'priority': entity.get('Priority'),
                    'created_at': entity.get('CreatedAt'),
                    'created_by': entity.get('CreatedBy'),
                    'started_at': entity.get('StartedAt'),
                    'completed_at': entity.get('CompletedAt'),
                    'error_message': entity.get('ErrorMessage'),
                    'data': json.loads(entity.get('Params', '{}')) if entity.get('Params') else {},
                    'execution_log_id': entity.get('ExecutionLogId')
                }
            else:
                logger.warning(f"No tasks found for org_id={org_id}, task_type={task_type}")
                return None

        else:
            # Use SQL Database for production
            repo = get_task_sql_repo()
            if not repo:
                logger.error("Task SQL repository not available")
                return None

            # Build query with optional filters
            where_conditions = ["TaskType = ?", "OrgId = ?"]
            params = [task_type, org_id]
            
            if status:
                where_conditions.append("Status = ?")
                params.append(status)
            if execution_log_id:
                where_conditions.append("ExecutionLogId = ?")
                params.append(execution_log_id)
            
            where_clause = " AND ".join(where_conditions)

            query = f"""
            SELECT TOP 1 TaskId, OrgId, TaskType, Status, Priority, CreatedAt, CreatedBy,
                   StartedAt, CompletedAt, ErrorMessage, Data, ExecutionLogId
            FROM TaskStatus
            WHERE {where_clause}
            ORDER BY CreatedAt DESC
            """

            results = repo.execute_query(query, tuple(params))

            if results:
                row = results[0]
                return {
                    'task_id': row[0],
                    'org_id': row[1],
                    'task_type': row[2],
                    'status': row[3],
                    'priority': row[4],
                    'created_at': row[5].isoformat() if row[5] else None,
                    'created_by': row[6],
                    'started_at': row[7].isoformat() if row[7] else None,
                    'completed_at': row[8].isoformat() if row[8] else None,
                    'error_message': row[9],
                    'data': json.loads(row[10]) if row[10] else {},
                    'execution_log_id': row[11]
                }
            else:
                logger.warning(f"No tasks found for org_id={org_id}, task_type={task_type}")
                return None

    except Exception as e:
        logger.error(f"Error getting latest task by type: {str(e)}")
        return None


def get_task_by_execution_log_and_type(execution_log_id: str, task_type: str) -> Optional[Dict[str, Any]]:
    """
    Get task by execution log ID and task type for idempotency checking

    Args:
        execution_log_id: Execution log ID
        task_type: Task type

    Returns:
        Task data dictionary or None
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_task_table_repo()
            if not repo:
                logger.error("Task table repository not available")
                return None

            # Query all entities and filter in memory since we don't have org_id
            # Azure Table Storage doesn't support startswith in OData queries
            all_entities = list(repo.query_entities())
            
            # Find entities that match the criteria
            matching_entities = []
            for entity in all_entities:
                partition_key = entity.get('PartitionKey', '')
                entity_task_type = entity.get('TaskType', '')
                entity_execution_log_id = entity.get('ExecutionLogId', '')
                
                if (partition_key.startswith('task_') and 
                    entity_task_type == task_type and 
                    entity_execution_log_id == execution_log_id):
                    matching_entities.append(entity)

            if matching_entities:
                # Sort by CreatedAt descending and take the first one
                matching_entities.sort(key=lambda x: x.get('CreatedAt', ''), reverse=True)
                entity = matching_entities[0]
                return {
                    'task_id': entity.get('RowKey'),
                    'org_id': entity.get('OrgId'),
                    'task_type': entity.get('TaskType'),
                    'status': entity.get('Status'),
                    'priority': entity.get('Priority'),
                    'created_at': entity.get('CreatedAt'),
                    'created_by': entity.get('CreatedBy'),
                    'started_at': entity.get('StartedAt'),
                    'completed_at': entity.get('CompletedAt'),
                    'error_message': entity.get('ErrorMessage'),
                    'data': json.loads(entity.get('Params', '{}')) if entity.get('Params') else {},
                    'execution_log_id': entity.get('ExecutionLogId')
                }
            else:
                logger.debug(f"No tasks found for execution_log_id={execution_log_id}, task_type={task_type}")
                return None

        else:
            # Use SQL Database for production
            repo = get_task_sql_repo()
            if not repo:
                logger.error("Task SQL repository not available")
                return None

            query = """
            SELECT TOP 1 TaskId, OrgId, TaskType, Status, Priority, CreatedAt, CreatedBy,
                   StartedAt, CompletedAt, ErrorMessage, Data, ExecutionLogId
            FROM TaskStatus
            WHERE TaskType = ? AND ExecutionLogId = ?
            ORDER BY CreatedAt DESC
            """

            results = repo.execute_query(query, (task_type, execution_log_id))

            if results:
                row = results[0]
                return {
                    'task_id': row[0],
                    'org_id': row[1],
                    'task_type': row[2],
                    'status': row[3],
                    'priority': row[4],
                    'created_at': row[5].isoformat() if row[5] else None,
                    'created_by': row[6],
                    'started_at': row[7].isoformat() if row[7] else None,
                    'completed_at': row[8].isoformat() if row[8] else None,
                    'error_message': row[9],
                    'data': json.loads(row[10]) if row[10] else {},
                    'execution_log_id': row[11]
                }
            else:
                logger.debug(f"No tasks found for execution_log_id={execution_log_id}, task_type={task_type}")
                return None

    except Exception as e:
        logger.error(f"Error getting task by execution log and type: {str(e)}")
        return None


# Create blueprint
bp = func.Blueprint()

@bp.route(route="tasks", methods=["GET"])
def list_tasks(req: func.HttpRequest) -> func.HttpResponse:
    """List tasks with optional filtering"""
    try:
        # Get query parameters
        org_id = req.params.get('org_id')
        status = req.params.get('status')
        task_type = req.params.get('task_type')
        priority = req.params.get('priority')

        tasks = get_tasks(
            org_id=org_id,
            status=status,
            task_type=task_type,
            priority=priority
        )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": tasks
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error listing tasks: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks", methods=["POST"])
def create_task_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Create a new task"""
    try:
        # Parse request body
        try:
            task_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Invalid JSON in request body"
                }),
                mimetype="application/json",
                status_code=400
            )

        if not task_data:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Validate required fields
        required_fields = ['task_type', 'org_id']
        for field in required_fields:
            if field not in task_data:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"Missing required field: {field}"
                    }),
                    mimetype="application/json",
                    status_code=400
                )

        # Get user ID from request headers or use provided value
        if 'user_id' not in task_data:
            user_id = req.headers.get('X-User-Id') or req.headers.get('x-user-id') or "system"
            task_data['user_id'] = user_id
            logger.info(f"Creating task for user: {user_id}")

        # Check for internal header
        is_internal = req.headers.get('X-Internal-Task-Enqueue', '').lower() == 'true'

        # Create the task
        task_id = create_task(task_data, is_internal=is_internal)

        if task_id:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "task_id": task_id
                    }
                }),
                mimetype="application/json",
                status_code=201
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create task"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error creating task: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/{task_id}", methods=["GET"])
def get_task_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Get task by ID"""
    try:
        task_id = req.route_params.get('task_id')
        if not task_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Task ID is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        task = get_task_by_id(task_id)

        if task:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": task
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Task not found"
                }),
                mimetype="application/json",
                status_code=404
            )

    except Exception as e:
        logger.error(f"Error getting task: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/{task_id}/status", methods=["PUT"])
def update_task_status_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Update task status"""
    try:
        task_id = req.route_params.get('task_id')
        if not task_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "task_id is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Parse request body
        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Invalid JSON in request body"
                }),
                mimetype="application/json",
                status_code=400
            )

        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        status = req_body.get('status')
        if not status:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "status is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Update task status
        success = update_task_status(
            task_id=task_id,
            status=status,
            error_message=req_body.get('error'),
            progress=req_body.get('progress'),
            message=req_body.get('message')
        )

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": f"Task {task_id} status updated to {status}"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": f"Failed to update task {task_id} status"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error updating task status: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/latest", methods=["GET"])
def get_latest_task_by_type_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Get the latest task of a specific type for an organization"""
    try:
        org_id = req.params.get('org_id')
        task_type = req.params.get('task_type')
        status = req.params.get('status')
        execution_log_id = req.params.get('execution_log_id')

        if not org_id or not task_type:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id and task_type are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        task = get_latest_task_by_type(org_id, task_type, status, execution_log_id)

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": task
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting latest task by type: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="tasks/by-execution-log-and-type", methods=["GET"])
def get_task_by_execution_log_and_type_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Get task by execution log ID and task type for idempotency checking"""
    try:
        execution_log_id = req.params.get('execution_log_id')
        task_type = req.params.get('task_type')

        if not execution_log_id or not task_type:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "execution_log_id and task_type are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        task = get_task_by_execution_log_and_type(execution_log_id, task_type)

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": task
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting task by execution log and type: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )

