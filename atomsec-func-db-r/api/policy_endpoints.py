"""
Policy and Rule Management Endpoints

This module provides policy and rule management operations for the AtomSec application.
These endpoints support the policy-based task filtering system.

Endpoints:
- GET /api/policies - Get policies with optional filtering
- POST /api/policies - Store policy data
- GET /api/rules - Get rules with optional filtering  
- POST /api/rules - Store rule data
- GET /api/policy-rule-settings - Get policy rule settings
- POST /api/policy-rule-settings - Store policy rule setting
- GET /api/policy-rule-settings/enabled-tasks - Get enabled tasks for integration
"""

import logging
import json
import uuid
import azure.functions as func
from datetime import datetime
from typing import Dict, List, Any, Optional

from shared.common import is_local_dev
from shared.data_access import (
    get_table_storage_repository,
    get_sql_database_repository
)

# Configure module-level logger
logger = logging.getLogger(__name__)

# Table names
POLICY_TABLE_NAME = "Policy"
RULE_TABLE_NAME = "SecurityRule"
POLICY_RULE_SETTING_TABLE_NAME = "PolicyRuleSetting"

def get_policy_table_repo():
    """Get the Policy table repository for local/Azurite development"""
    return get_table_storage_repository(POLICY_TABLE_NAME)

def get_rule_table_repo():
    """Get the SecurityRule table repository for local/Azurite development"""
    return get_sql_database_repository("SecurityRule")

def get_policy_rule_setting_table_repo():
    """Get the PolicyRuleSetting table repository for local/Azurite development"""
    return get_table_storage_repository(POLICY_RULE_SETTING_TABLE_NAME)

def get_policy_sql_repo():
    """Get the Policy SQL repository for production"""
    repo = get_sql_database_repository("Policy")
    logger.info(f"Created Policy SQL repository: {type(repo).__name__}")
    return repo

def get_rule_sql_repo():
    """Get the Rule SQL repository for production"""
    repo = get_sql_database_repository("SecurityRule")
    logger.info(f"Created SecurityRule SQL repository: {type(repo).__name__}")
    return repo

def get_policy_rule_setting_sql_repo():
    """Get the PolicyRuleSetting SQL repository for production"""
    repo = get_sql_database_repository("PolicyRuleSetting")
    logger.info(f"Created PolicyRuleSetting SQL repository: {type(repo).__name__}")
    return repo

def store_policy_data(policy_data: Dict[str, Any]) -> bool:
    """
    Store policy data
    
    Args:
        policy_data: Policy data dictionary
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_policy_table_repo()
            if not repo:
                logger.error("Policy table repository not available")
                return False

            # Prepare entity for table storage
            entity = {
                "PartitionKey": policy_data.get("IntegrationId", "default"),
                "RowKey": policy_data.get("PolicyId", str(uuid.uuid4())),
                "PolicyId": policy_data.get("PolicyId"),
                "Name": policy_data.get("Name"),
                "UserId": policy_data.get("UserId"),
                "IntegrationId": policy_data.get("IntegrationId"),
                "CreatedAt": policy_data.get("CreatedAt", datetime.now().isoformat())
            }

            success = repo.insert_entity(entity)
            if success:
                logger.info(f"Stored policy {entity['PolicyId']} for integration {entity['IntegrationId']}")
            return success

        else:
            # Use SQL Database for production
            repo = get_policy_sql_repo()
            if not repo:
                logger.error("Policy SQL repository not available")
                return False

            # Check if the repository has the required methods
            logger.info(f"Repository type: {type(repo).__name__}")
            logger.info(f"Repository methods: {[method for method in dir(repo) if not method.startswith('_')]}")
            
            if not hasattr(repo, 'execute_non_query'):
                logger.error(f"Repository {type(repo).__name__} missing execute_non_query method")
                return False

            query = """
            INSERT INTO Policy (PolicyId, Name, UserId, IntegrationId, CreatedAt)
            VALUES (?, ?, ?, ?, ?)
            """
            params = (
                policy_data.get("PolicyId"),
                policy_data.get("Name"),
                policy_data.get("UserId"),
                policy_data.get("IntegrationId"),
                policy_data.get("CreatedAt", datetime.now())
            )

            logger.info(f"Executing SQL insert for policy {policy_data.get('PolicyId')} with query: {query}")
            logger.info(f"Parameters: {params}")

            success = repo.execute_non_query(query, params)
            if success:
                logger.info(f"Stored policy {policy_data.get('PolicyId')} for integration {policy_data.get('IntegrationId')}")
            else:
                logger.error(f"Failed to store policy {policy_data.get('PolicyId')} for integration {policy_data.get('IntegrationId')}")
            return success

    except Exception as e:
        logger.error(f"Error storing policy data: {str(e)}")
        return False

def get_policies_data(integration_id: Optional[str] = None, user_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get policies data with optional filtering
    
    Args:
        integration_id: Optional filter by integration ID
        user_id: Optional filter by user ID
        
    Returns:
        List of policy dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_policy_table_repo()
            if not repo:
                logger.error("Policy table repository not available")
                return []

            # Build filter query
            filter_parts = []
            if integration_id:
                filter_parts.append(f"IntegrationId eq '{integration_id}'")
            if user_id:
                filter_parts.append(f"UserId eq '{user_id}'")
            
            filter_query = " and ".join(filter_parts) if filter_parts else None
            entities = repo.query_entities(filter_query)

            policies = []
            for entity in entities:
                policy = {
                    "PolicyId": entity.get("PolicyId"),
                    "Name": entity.get("Name"),
                    "UserId": entity.get("UserId"),
                    "IntegrationId": entity.get("IntegrationId"),
                    "CreatedAt": entity.get("CreatedAt")
                }
                policies.append(policy)

            return policies

        else:
            # Use SQL Database for production
            repo = get_policy_sql_repo()
            if not repo:
                logger.error("Policy SQL repository not available")
                return []

            # Build query with filters
            query = "SELECT PolicyId, Name, UserId, IntegrationId, CreatedAt FROM Policy WHERE 1=1"
            params = []

            if integration_id:
                query += " AND IntegrationId = ?"
                params.append(integration_id)
            if user_id:
                query += " AND UserId = ?"
                params.append(user_id)

            query += " ORDER BY CreatedAt DESC"

            results = repo.execute_query(query, tuple(params))
            policies = []
            for row in results:
                policy = {
                    "PolicyId": row[0],
                    "Name": row[1],
                    "UserId": row[2],
                    "IntegrationId": row[3],
                    "CreatedAt": row[4].isoformat() if hasattr(row[4], 'isoformat') else str(row[4])
                }
                policies.append(policy)

            return policies

    except Exception as e:
        logger.error(f"Error getting policies data: {str(e)}")
        return []

def store_rule_data(rule_data: Dict[str, Any]) -> bool:
    """
    Store rule data
    
    Args:
        rule_data: Rule data dictionary
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_rule_table_repo()
            if not repo:
                logger.error("SecurityRule table repository not available")
                return False

            # Prepare entity for table storage
            entity = {
                "PartitionKey": rule_data.get("PolicyId", "default"),
                "RowKey": rule_data.get("RuleId", str(uuid.uuid4())),
                "RuleId": rule_data.get("RuleId"),
                "PolicyId": rule_data.get("PolicyId"),
                "TaskType": rule_data.get("TaskType"),
                "Enabled": int(rule_data.get("Enabled", 1)),
                "CreatedAt": rule_data.get("CreatedAt", datetime.now().isoformat())
            }

            success = repo.insert_entity(entity)
            if success:
                logger.info(f"Stored rule {entity['RuleId']} for policy {entity['PolicyId']}")
            return success

        else:
            # Use SQL Database for production
            repo = get_rule_sql_repo()
            if not repo:
                logger.error("Rule SQL repository not available")
                return False

            # Check if the repository has the required methods
            logger.info(f"Repository type: {type(repo).__name__} for SecurityRule table")
            logger.info(f"Repository methods: {[method for method in dir(repo) if not method.startswith('_')]}")
            
            if not hasattr(repo, 'execute_non_query'):
                logger.error(f"Repository {type(repo).__name__} missing execute_non_query method")
                return False

            query = """
            INSERT INTO SecurityRule (RuleId, PolicyId, TaskType, Enabled, CreatedAt)
            VALUES (?, ?, ?, ?, ?)
            """
            params = (
                rule_data.get("RuleId"),
                rule_data.get("PolicyId"),
                rule_data.get("TaskType"),
                int(rule_data.get("Enabled", 1)),
                rule_data.get("CreatedAt", datetime.now())
            )

            logger.info(f"Executing SQL insert for rule {rule_data.get('RuleId')} with query: {query}")
            logger.info(f"Parameters: {params}")

            success = repo.execute_non_query(query, params)
            if success:
                logger.info(f"Stored rule {rule_data.get('RuleId')} for policy {rule_data.get('PolicyId')}")
            else:
                logger.error(f"Failed to store rule {rule_data.get('RuleId')} for policy {rule_data.get('PolicyId')}")
            return success

    except Exception as e:
        logger.error(f"Error storing rule data: {str(e)}")
        return False

def get_rules_data(policy_id: Optional[str] = None, task_type: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get rules data with optional filtering
    
    Args:
        policy_id: Optional filter by policy ID
        task_type: Optional filter by task type
        
    Returns:
        List of rule dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_rule_table_repo()
            if not repo:
                logger.error("SecurityRule table repository not available")
                return []

            # Build filter query
            filter_parts = []
            if policy_id:
                filter_parts.append(f"PolicyId eq '{policy_id}'")
            if task_type:
                filter_parts.append(f"TaskType eq '{task_type}'")
            
            filter_query = " and ".join(filter_parts) if filter_parts else None
            entities = repo.query_entities(filter_query)

            rules = []
            for entity in entities:
                rule = {
                    "RuleId": entity.get("RuleId"),
                    "PolicyId": entity.get("PolicyId"),
                    "TaskType": entity.get("TaskType"),
                    "Enabled": bool(entity.get("Enabled", 1)),
                    "CreatedAt": entity.get("CreatedAt")
                }
                rules.append(rule)

            return rules

        else:
            # Use SQL Database for production
            repo = get_rule_sql_repo()
            if not repo:
                logger.error("Rule SQL repository not available")
                return []

            # Build query with filters
            query = "SELECT RuleId, PolicyId, TaskType, Enabled, CreatedAt FROM SecurityRule WHERE 1=1"
            params = []

            if policy_id:
                query += " AND PolicyId = ?"
                params.append(policy_id)
            if task_type:
                query += " AND TaskType = ?"
                params.append(task_type)

            query += " ORDER BY CreatedAt DESC"

            results = repo.execute_query(query, tuple(params))
            rules = []
            for row in results:
                rule = {
                    "RuleId": row[0],
                    "PolicyId": row[1],
                    "TaskType": row[2],
                    "Enabled": bool(row[3]),
                    "CreatedAt": row[4].isoformat() if hasattr(row[4], 'isoformat') else str(row[4])
                }
                rules.append(rule)

            return rules

    except Exception as e:
        logger.error(f"Error getting rules data: {str(e)}")
        return []

# Create blueprint
bp = func.Blueprint()

@bp.route(route="policies", methods=["GET"])
def list_policies(req: func.HttpRequest) -> func.HttpResponse:
    """Get policies with optional filtering"""
    try:
        # Get query parameters
        integration_id = req.params.get('integration_id')
        user_id = req.params.get('user_id')

        policies = get_policies_data(integration_id, user_id)

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": policies
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error getting policies: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="policies", methods=["POST"])
def store_policy(req: func.HttpRequest) -> func.HttpResponse:
    """Store policy data"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Validate required fields
        required_fields = ["PolicyId", "Name", "UserId", "IntegrationId"]
        for field in required_fields:
            if not req_body.get(field):
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"{field} is required"
                    }),
                    mimetype="application/json",
                    status_code=400
                )

        success = store_policy_data(req_body)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": "Policy stored successfully"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store policy"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error storing policy: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="rules", methods=["GET"])
def list_rules(req: func.HttpRequest) -> func.HttpResponse:
    """Get rules with optional filtering"""
    try:
        # Get query parameters
        policy_id = req.params.get('policy_id')
        task_type = req.params.get('task_type')

        rules = get_rules_data(policy_id, task_type)

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": rules
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error getting rules: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="rules", methods=["POST"])
def store_rule(req: func.HttpRequest) -> func.HttpResponse:
    """Store rule data"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Validate required fields
        required_fields = ["RuleId", "PolicyId", "TaskType"]
        for field in required_fields:
            if not req_body.get(field):
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"{field} is required"
                    }),
                    mimetype="application/json",
                    status_code=400
                )

        success = store_rule_data(req_body)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": "Rule stored successfully"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store rule"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error storing rule: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )

def store_policy_rule_setting_data(setting_data: Dict[str, Any]) -> bool:
    """
    Store policy rule setting data

    Args:
        setting_data: Policy rule setting data dictionary

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_policy_rule_setting_table_repo()
            if not repo:
                logger.error("PolicyRuleSetting table repository not available")
                return False

            # Prepare entity for table storage
            entity = {
                "PartitionKey": setting_data.get("PolicyId", "default"),
                "RowKey": f"{setting_data.get('UserId', '')}-{setting_data.get('IntegrationId', '')}-{setting_data.get('TaskType', '')}",
                "PolicyId": setting_data.get("PolicyId"),
                "UserId": setting_data.get("UserId"),
                "IntegrationId": setting_data.get("IntegrationId"),
                "TaskType": setting_data.get("TaskType"),
                "Enabled": int(setting_data.get("Enabled", 1)),
                "CreatedAt": setting_data.get("CreatedAt", datetime.now().isoformat())
            }

            success = repo.insert_entity(entity)
            if success:
                logger.info(f"Stored policy rule setting for task {entity['TaskType']}")
            return success

        else:
            # Use SQL Database for production
            repo = get_policy_rule_setting_sql_repo()
            if not repo:
                logger.error("PolicyRuleSetting SQL repository not available")
                return False

            query = """
            INSERT INTO PolicyRuleSetting (PolicyId, UserId, IntegrationId, TaskType, Enabled, CreatedAt)
            VALUES (?, ?, ?, ?, ?, ?)
            """
            params = (
                setting_data.get("PolicyId"),
                setting_data.get("UserId"),
                setting_data.get("IntegrationId"),
                setting_data.get("TaskType"),
                int(setting_data.get("Enabled", 1)),
                setting_data.get("CreatedAt", datetime.now())
            )

            success = repo.execute_non_query(query, params)
            if success:
                logger.info(f"Stored policy rule setting for task {setting_data.get('TaskType')}")
            return success

    except Exception as e:
        logger.error(f"Error storing policy rule setting data: {str(e)}")
        return False

def get_enabled_tasks_for_integration(integration_id: str, user_id: Optional[str] = None) -> List[str]:
    """
    Get list of enabled task types for an integration

    Args:
        integration_id: Integration ID
        user_id: Optional user ID filter

    Returns:
        List of enabled task type strings
    """
    try:
        # First get policies for this integration
        policies = get_policies_data(integration_id, user_id)
        if not policies:
            logger.info(f"No policies found for integration {integration_id}")
            return []

        enabled_tasks = set()

        # For each policy, get its rules and check which tasks are enabled
        for policy in policies:
            policy_id = policy.get("PolicyId")
            if not policy_id:
                continue

            rules = get_rules_data(policy_id)
            for rule in rules:
                if rule.get("Enabled", False):
                    task_type = rule.get("TaskType")
                    if task_type:
                        enabled_tasks.add(task_type)

        return list(enabled_tasks)

    except Exception as e:
        logger.error(f"Error getting enabled tasks for integration {integration_id}: {str(e)}")
        return []

@bp.route(route="policy-rule-settings", methods=["POST"])
def store_policy_rule_setting(req: func.HttpRequest) -> func.HttpResponse:
    """Store policy rule setting data"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Validate required fields
        required_fields = ["PolicyId", "UserId", "IntegrationId", "TaskType"]
        for field in required_fields:
            if not req_body.get(field):
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": f"{field} is required"
                    }),
                    mimetype="application/json",
                    status_code=400
                )

        success = store_policy_rule_setting_data(req_body)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": "Policy rule setting stored successfully"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store policy rule setting"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error storing policy rule setting: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="policy-rule-settings/enabled-tasks", methods=["GET"])
def get_enabled_tasks(req: func.HttpRequest) -> func.HttpResponse:
    """Get enabled tasks for an integration"""
    try:
        # Get query parameters
        integration_id = req.params.get('integration_id')
        user_id = req.params.get('user_id')

        if not integration_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "integration_id is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        enabled_tasks = get_enabled_tasks_for_integration(integration_id, user_id)

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": enabled_tasks
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error getting enabled tasks: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="policies/create-defaults", methods=["POST"])
def create_default_policies_and_rules(req: func.HttpRequest) -> func.HttpResponse:
    """Create default policies and rules for an integration"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Validate required fields
        user_id = req_body.get('user_id')
        integration_id = req_body.get('integration_id')

        if not user_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "user_id is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        if not integration_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "integration_id is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Import and call the policy creation function
        from shared.data_access import create_default_policies_and_rules_for_integration

        success = create_default_policies_and_rules_for_integration(user_id, integration_id)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": f"Default policies and rules created successfully for integration {integration_id}"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create default policies and rules"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error creating default policies and rules: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )
