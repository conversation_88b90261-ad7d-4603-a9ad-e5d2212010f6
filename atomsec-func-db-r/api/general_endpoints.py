"""
General Endpoints

This module provides general endpoints for common operations.
"""

import logging
import json
import azure.functions as func
from typing import Dict, Any, Optional, List
from azure.functions import Blueprint

from shared.azure_services import is_local_dev
from shared.data_access import get_table_storage_repository, get_sql_database_repository, create_default_policies_and_rules_for_integration

logger = logging.getLogger(__name__)
bp = Blueprint()

def get_stats_business_logic() -> Dict[str, Any]:
    """
    Business logic for getting database statistics

    Returns:
        Dictionary with database statistics
    """
    stats = {
        "users": 0,
        "accounts": 0,
        "organizations": 0,
        "integrations": 0,
        "tasks": 0,
        "security_checks": 0,
        "policies": 0
    }

    # Get repositories
    if is_local_dev():
        user_repo = get_table_storage_repository("users")
        account_repo = get_table_storage_repository("Account")
        org_repo = get_table_storage_repository("UserAccount")
        integration_repo = get_table_storage_repository("Integrations")
        task_repo = get_table_storage_repository("TaskStatus")
        security_repo = get_table_storage_repository("HealthCheck")
        policy_repo = get_table_storage_repository("Policy")
    else:
        user_repo = get_sql_database_repository("users")
        account_repo = get_sql_database_repository("Account")
        org_repo = get_sql_database_repository("UserAccount")
        integration_repo = get_sql_database_repository("Integrations")
        task_repo = get_sql_database_repository("TaskStatus")
        security_repo = get_sql_database_repository("HealthCheck")
        policy_repo = get_sql_database_repository("Policy")
        
    # Count records in each table
    try:
        if user_repo:
            stats["users"] = len(user_repo.query_entities())
    except Exception as e:
        logger.warning(f"Error counting users: {str(e)}")

    try:
        if account_repo:
            stats["accounts"] = len(account_repo.query_entities())
    except Exception as e:
        logger.warning(f"Error counting accounts: {str(e)}")

    try:
        if org_repo:
            stats["organizations"] = len(org_repo.query_entities())
    except Exception as e:
        logger.warning(f"Error counting organizations: {str(e)}")

    try:
        if integration_repo:
            stats["integrations"] = len(integration_repo.query_entities())
    except Exception as e:
        logger.warning(f"Error counting integrations: {str(e)}")

    try:
        if task_repo:
            stats["tasks"] = len(task_repo.query_entities())
    except Exception as e:
        logger.warning(f"Error counting tasks: {str(e)}")

    try:
        if security_repo:
            stats["security_checks"] = len(security_repo.query_entities())
    except Exception as e:
        logger.warning(f"Error counting security checks: {str(e)}")

    try:
        if policy_repo:
            stats["policies"] = len(policy_repo.query_entities())
    except Exception as e:
        logger.warning(f"Error counting policies: {str(e)}")
        
    return stats


@bp.route(route="stats", methods=["GET"])
def get_stats(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get database statistics

    Returns:
        JSON response with database statistics
    """
    try:
        # Authentication handled by APIM
        stats = get_stats_business_logic()

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": stats
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting stats: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to get stats: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

def create_backup_business_logic(request_data: dict, user_id: str = "system") -> dict:
    """
    Business logic for creating database backup

    Args:
        request_data: Dictionary containing backup request data
        user_id: User ID creating the backup

    Returns:
        Dictionary with backup result or error information
    """
    backup_type = request_data.get("type", "full")
    include_data = request_data.get("include_data", True)

    # Validate backup type
    valid_types = ["full", "incremental", "differential"]
    if backup_type not in valid_types:
        return {
            "success": False,
            "error": f"Invalid backup type: {backup_type}",
            "status_code": 400
        }

    # Create backup (simplified implementation)
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_id = f"backup_{backup_type}_{timestamp}"

    # Publish backup creation event
    try:
        from shared.service_bus_client import get_service_bus_client
        service_bus_client = get_service_bus_client()
        if service_bus_client:
            service_bus_client.publish_event(
                event_type="DatabaseBackupCreated",
                event_data={
                    "backup_id": backup_id,
                    "backup_type": backup_type,
                    "include_data": include_data,
                    "created_by": user_id
                },
                user_id=user_id
            )
    except Exception as e:
        logger.warning(f"Failed to publish backup event: {str(e)}")

    return {
        "success": True,
        "data": {
            "backup_id": backup_id,
            "backup_type": backup_type,
            "include_data": include_data,
            "created_by": user_id,
            "status": "initiated"
        },
        "message": "Backup creation initiated",
        "status_code": 202
    }


@bp.route(route="backup", methods=["POST"])
def create_backup(req: func.HttpRequest) -> func.HttpResponse:
    """
    Create database backup

    Returns:
        JSON response with backup status
    """
    try:
        # Get user ID from request headers (APIM will provide this)
        user_id = req.headers.get('X-User-Id') or req.headers.get('x-user-id') or "system"

        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON in request body"}),
                mimetype="application/json",
                status_code=400
            )

        # Call business logic
        result = create_backup_business_logic(request_data, user_id)

        if result["success"]:
            return func.HttpResponse(
                json.dumps({
                    "success": result["success"],
                    "data": result["data"],
                    "message": result["message"]
                }),
                mimetype="application/json",
                status_code=result["status_code"]
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": result["success"],
                    "error": result["error"]
                }),
                mimetype="application/json",
                status_code=result["status_code"]
            )

    except Exception as e:
        logger.error(f"Error creating backup: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to create backup: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

# cleanup_old_data function removed