-- =====================================================
-- AtomSec Database Schema Validation and Fix Script
-- =====================================================
-- This script checks existing tables and creates missing ones
-- Run this in your Azure SQL Database to fix schema issues

USE [sql-atomsec-dev];
GO

-- =====================================================
-- 1. CHECK EXISTING TABLES AND SCHEMAS
-- =====================================================

PRINT '=== CHECKING EXISTING TABLES ===';

-- Check if tables exist and their current schemas
IF OBJECT_ID('Account', 'U') IS NOT NULL
BEGIN
    PRINT '✓ Account table exists';
    
    -- Check columns
    IF COL_LENGTH('Account', 'IsActive') IS NULL
        PRINT '✗ Missing column: Account.IsActive (BIT)';
    ELSE
        PRINT '✓ Account.IsActive column exists';
        
    IF COL_LENGTH('Account', 'CreatedAt') IS NULL
        PRINT '✗ Missing column: Account.CreatedAt (DATETIME2)';
    ELSE
        PRINT '✓ Account.CreatedAt column exists';
        
    IF COL_LENGTH('Account', 'CreatedBy') IS NULL
        PRINT '✗ Missing column: Account.CreatedBy (NVARCHAR)';
    ELSE
        PRINT '✓ Account.CreatedBy column exists';
END
ELSE
BEGIN
    PRINT '✗ Account table does not exist';
END

IF OBJECT_ID('Integrations', 'U') IS NOT NULL
BEGIN
    PRINT '✓ Integrations table exists';
    
    -- Check key columns
    IF COL_LENGTH('Integrations', 'Id') IS NULL
        PRINT '✗ Missing column: Integrations.Id (NVARCHAR)';
    ELSE
        PRINT '✓ Integrations.Id column exists';
        
    IF COL_LENGTH('Integrations', 'IsActive') IS NULL
        PRINT '✗ Missing column: Integrations.IsActive (BIT)';
    ELSE
        PRINT '✓ Integrations.IsActive column exists';
END
ELSE
BEGIN
    PRINT '✗ Integrations table does not exist';
END

IF OBJECT_ID('UserAccount', 'U') IS NOT NULL
BEGIN
    PRINT '✓ UserAccount table exists';
    
    IF COL_LENGTH('UserAccount', 'Domain') IS NULL
        PRINT '✗ Missing column: UserAccount.Domain (NVARCHAR)';
    ELSE
        PRINT '✓ UserAccount.Domain column exists';
END
ELSE
BEGIN
    PRINT '✗ UserAccount table does not exist';
END

-- Check other tables
DECLARE @tables TABLE (table_name NVARCHAR(128));
INSERT INTO @tables VALUES 
    ('organizations'), ('App_Account'), ('App_User'), ('Credentials'), ('Policy'), ('SecurityRule'), ('PolicyRuleSetting'),
    ('App_HealthCheck'), ('App_PoliciesResult'), ('App_Overview'),
    ('App_PMDScans'), ('App_ProfilePermissions'), ('App_ProfileAssignmentCount'),
    ('TaskStatus'), ('UserLogin'), ('users');

DECLARE @table_name NVARCHAR(128);
DECLARE table_cursor CURSOR FOR SELECT table_name FROM @tables;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @table_name;

WHILE @@FETCH_STATUS = 0
BEGIN
    IF OBJECT_ID(@table_name, 'U') IS NOT NULL
        PRINT '✓ ' + @table_name + ' table exists';
    ELSE
        PRINT '✗ ' + @table_name + ' table does not exist';
    
    FETCH NEXT FROM table_cursor INTO @table_name;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '';

-- =====================================================
-- 2. CREATE MISSING TABLES AND COLUMNS
-- =====================================================

PRINT '=== CREATING MISSING TABLES AND COLUMNS ===';

-- Fix Account table
IF OBJECT_ID('Account', 'U') IS NOT NULL
BEGIN
    -- Add missing columns if they don't exist
    IF COL_LENGTH('Account', 'IsActive') IS NULL
    BEGIN
        ALTER TABLE Account ADD IsActive BIT DEFAULT 1;
        PRINT '✓ Added Account.IsActive column';
    END
    
    IF COL_LENGTH('Account', 'CreatedAt') IS NULL
    BEGIN
        ALTER TABLE Account ADD CreatedAt DATETIME2 DEFAULT GETDATE();
        PRINT '✓ Added Account.CreatedAt column';
    END
    
    IF COL_LENGTH('Account', 'CreatedBy') IS NULL
    BEGIN
        ALTER TABLE Account ADD CreatedBy NVARCHAR(255);
        PRINT '✓ Added Account.CreatedBy column';
    END
END
ELSE
BEGIN
    -- Create Account table if it doesn't exist
    CREATE TABLE Account (
        AccountId INT IDENTITY(1,1) PRIMARY KEY,
        Name NVARCHAR(500) NOT NULL,
        Description NVARCHAR(MAX),
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        CreatedBy NVARCHAR(255)
    );
    PRINT '✓ Created Account table';
END

-- Create App_Account table if it doesn't exist
IF OBJECT_ID('App_Account', 'U') IS NULL
BEGIN
    CREATE TABLE App_Account (
        AccountId INT IDENTITY(1,1) PRIMARY KEY,
        Name NVARCHAR(500) NOT NULL,
        Description NVARCHAR(MAX),
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        CreatedBy NVARCHAR(255)
    );
    PRINT '✓ Created App_Account table';
END
ELSE
BEGIN
    -- Add missing columns if they don't exist
    IF COL_LENGTH('App_Account', 'IsActive') IS NULL
    BEGIN
        ALTER TABLE App_Account ADD IsActive BIT DEFAULT 1;
        PRINT '✓ Added App_Account.IsActive column';
    END
    
    IF COL_LENGTH('App_Account', 'CreatedAt') IS NULL
    BEGIN
        ALTER TABLE App_Account ADD CreatedAt DATETIME2 DEFAULT GETDATE();
        PRINT '✓ Added App_Account.CreatedAt column';
    END
    
    IF COL_LENGTH('App_Account', 'CreatedBy') IS NULL
    BEGIN
        ALTER TABLE App_Account ADD CreatedBy NVARCHAR(255);
        PRINT '✓ Added App_Account.CreatedBy column';
    END
END

-- Create App_User table if it doesn't exist
IF OBJECT_ID('App_User', 'U') IS NULL
BEGIN
    CREATE TABLE App_User (
        UserId NVARCHAR(255) PRIMARY KEY,
        Name NVARCHAR(500) NOT NULL,
        Email NVARCHAR(255) NOT NULL,
        Phone NVARCHAR(50),
        AccountId NVARCHAR(255),
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        IsActive BIT DEFAULT 1
    );
    PRINT '✓ Created App_User table';
END
ELSE
BEGIN
    -- Add missing columns if they don't exist
    IF COL_LENGTH('App_User', 'CreatedAt') IS NULL
    BEGIN
        ALTER TABLE App_User ADD CreatedAt DATETIME2 DEFAULT GETDATE();
        PRINT '✓ Added App_User.CreatedAt column';
    END
    
    IF COL_LENGTH('App_User', 'IsActive') IS NULL
    BEGIN
        ALTER TABLE App_User ADD IsActive BIT DEFAULT 1;
        PRINT '✓ Added App_User.IsActive column';
    END
END

-- Fix Integrations table
IF OBJECT_ID('Integrations', 'U') IS NOT NULL
BEGIN
    -- Add missing columns if they don't exist
    IF COL_LENGTH('Integrations', 'Id') IS NULL
    BEGIN
        ALTER TABLE Integrations ADD Id NVARCHAR(255);
        PRINT '✓ Added Integrations.Id column';
    END
    
    IF COL_LENGTH('Integrations', 'IsActive') IS NULL
    BEGIN
        ALTER TABLE Integrations ADD IsActive BIT DEFAULT 1;
        PRINT '✓ Added Integrations.IsActive column';
    END
    
    IF COL_LENGTH('Integrations', 'LastScan') IS NULL
    BEGIN
        ALTER TABLE Integrations ADD LastScan NVARCHAR(255);
        PRINT '✓ Added Integrations.LastScan column';
    END
    
    IF COL_LENGTH('Integrations', 'LastScanAttempt') IS NULL
    BEGIN
        ALTER TABLE Integrations ADD LastScanAttempt NVARCHAR(255);
        PRINT '✓ Added Integrations.LastScanAttempt column';
    END
    
    IF COL_LENGTH('Integrations', 'HealthScore') IS NULL
    BEGIN
        ALTER TABLE Integrations ADD HealthScore FLOAT;
        PRINT '✓ Added Integrations.HealthScore column';
    END
    
    IF COL_LENGTH('Integrations', 'CreatedAt') IS NULL
    BEGIN
        ALTER TABLE Integrations ADD CreatedAt DATETIME2 DEFAULT GETDATE();
        PRINT '✓ Added Integrations.CreatedAt column';
    END
    
    IF COL_LENGTH('Integrations', 'UserEmail') IS NULL
    BEGIN
        ALTER TABLE Integrations ADD UserEmail NVARCHAR(255);
        PRINT '✓ Added Integrations.UserEmail column';
    END
    
    IF COL_LENGTH('Integrations', 'UserId') IS NULL
    BEGIN
        ALTER TABLE Integrations ADD UserId NVARCHAR(255);
        PRINT '✓ Added Integrations.UserId column';
    END
    
    IF COL_LENGTH('Integrations', 'AccountId') IS NULL
    BEGIN
        ALTER TABLE Integrations ADD AccountId NVARCHAR(255);
        PRINT '✓ Added Integrations.AccountId column';
    END
END
ELSE
BEGIN
    -- Create Integrations table if it doesn't exist
    CREATE TABLE Integrations (
        Id NVARCHAR(255) PRIMARY KEY,
        Name NVARCHAR(500) NOT NULL,
        TenantUrl NVARCHAR(1000),
        Type NVARCHAR(100),
        Description NVARCHAR(MAX),
        Environment NVARCHAR(50),
        IsActive BIT DEFAULT 1,
        LastScan NVARCHAR(255),
        LastScanAttempt NVARCHAR(255),
        HealthScore FLOAT,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        UserEmail NVARCHAR(255),
        UserId NVARCHAR(255),
        AccountId NVARCHAR(255)
    );
    PRINT '✓ Created Integrations table';
END

-- Create UserAccount table if it doesn't exist
IF OBJECT_ID('UserAccount', 'U') IS NULL
BEGIN
    CREATE TABLE UserAccount (
        Id NVARCHAR(255) PRIMARY KEY,
        Name NVARCHAR(500) NOT NULL,
        Description NVARCHAR(MAX),
        Domain NVARCHAR(255),
        ContactEmail NVARCHAR(255),
        CreatedAt DATETIME2 DEFAULT GETDATE()
    );
    PRINT '✓ Created UserAccount table';
END

-- Create organizations table if it doesn't exist
IF OBJECT_ID('organizations', 'U') IS NULL
BEGIN
    CREATE TABLE organizations (
        Id NVARCHAR(255) PRIMARY KEY,
        Name NVARCHAR(500) NOT NULL,
        Description NVARCHAR(MAX),
        Domain NVARCHAR(255),
        ContactEmail NVARCHAR(255),
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        CreatedBy NVARCHAR(255)
    );
    PRINT '✓ Created organizations table';
END
ELSE
BEGIN
    -- Add missing columns if they don't exist
    IF COL_LENGTH('organizations', 'IsActive') IS NULL
    BEGIN
        ALTER TABLE organizations ADD IsActive BIT DEFAULT 1;
        PRINT '✓ Added organizations.IsActive column';
    END
    
    IF COL_LENGTH('organizations', 'CreatedAt') IS NULL
    BEGIN
        ALTER TABLE organizations ADD CreatedAt DATETIME2 DEFAULT GETDATE();
        PRINT '✓ Added organizations.CreatedAt column';
    END
    
    IF COL_LENGTH('organizations', 'CreatedBy') IS NULL
    BEGIN
        ALTER TABLE organizations ADD CreatedBy NVARCHAR(255);
        PRINT '✓ Added organizations.CreatedBy column';
    END
END

-- Create Credentials table if it doesn't exist
IF OBJECT_ID('Credentials', 'U') IS NULL
BEGIN
    CREATE TABLE Credentials (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        OrgId NVARCHAR(255) NOT NULL,
        ClientId NVARCHAR(255),
        ClientSecret NVARCHAR(MAX),
        KeyVaultId NVARCHAR(255),
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        UpdatedAt DATETIME2 DEFAULT GETDATE()
    );
    PRINT '✓ Created Credentials table';
END

-- Create Policy table if it doesn't exist
IF OBJECT_ID('Policy', 'U') IS NULL
BEGIN
    CREATE TABLE Policy (
        PolicyId INT IDENTITY(1,1) PRIMARY KEY,
        Name NVARCHAR(500) NOT NULL,
        UserId NVARCHAR(255),
        IntegrationId NVARCHAR(255),
        CreatedAt DATETIME2 DEFAULT GETDATE()
    );
    PRINT '✓ Created Policy table';
END

-- Create SecurityRule table if it doesn't exist
IF OBJECT_ID('SecurityRule', 'U') IS NULL
BEGIN
    CREATE TABLE SecurityRule (
        RuleId INT IDENTITY(1,1) PRIMARY KEY,
        PolicyId INT,
        TaskType NVARCHAR(100),
        Enabled BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (PolicyId) REFERENCES Policy(PolicyId)
    );
    PRINT '✓ Created SecurityRule table';
END

-- Create PolicyRuleSetting table if it doesn't exist
IF OBJECT_ID('PolicyRuleSetting', 'U') IS NULL
BEGIN
    CREATE TABLE PolicyRuleSetting (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        PolicyId INT,
        UserId NVARCHAR(255),
        IntegrationId NVARCHAR(255),
        TaskType NVARCHAR(100),
        Enabled BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (PolicyId) REFERENCES Policy(PolicyId)
    );
    PRINT '✓ Created PolicyRuleSetting table';
END

-- Create App_HealthCheck table if it doesn't exist
IF OBJECT_ID('App_HealthCheck', 'U') IS NULL
BEGIN
    CREATE TABLE App_HealthCheck (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        OrgId NVARCHAR(255) NOT NULL,
        ExecutionLogId NVARCHAR(255),
        RiskType NVARCHAR(100),
        Setting NVARCHAR(500),
        SettingGroup NVARCHAR(100),
        OrgValue NVARCHAR(MAX),
        RiskLevel NVARCHAR(50),
        CreatedAt DATETIME2 DEFAULT GETDATE()
    );
    PRINT '✓ Created App_HealthCheck table';
END

-- Create App_PoliciesResult table if it doesn't exist
IF OBJECT_ID('App_PoliciesResult', 'U') IS NULL
BEGIN
    CREATE TABLE App_PoliciesResult (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        OrgId NVARCHAR(255) NOT NULL,
        ExecutionLogId NVARCHAR(255),
        ProfileName NVARCHAR(500),
        PermissionSetName NVARCHAR(500),
        Type NVARCHAR(100),
        OrgValue NVARCHAR(MAX),
        RiskLevel NVARCHAR(50),
        CreatedAt DATETIME2 DEFAULT GETDATE()
    );
    PRINT '✓ Created App_PoliciesResult table';
END

-- Create App_Overview table if it doesn't exist
IF OBJECT_ID('App_Overview', 'U') IS NULL
BEGIN
    CREATE TABLE App_Overview (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        OrgId NVARCHAR(255) NOT NULL,
        ExecutionLogId NVARCHAR(255),
        HealthScore FLOAT,
        TotalProfiles INT,
        TotalPermissionSets INT,
        CreatedAt DATETIME2 DEFAULT GETDATE()
    );
    PRINT '✓ Created App_Overview table';
END

-- Create App_PMDScans table if it doesn't exist
IF OBJECT_ID('App_PMDScans', 'U') IS NULL
BEGIN
    CREATE TABLE App_PMDScans (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        OrgId NVARCHAR(255) NOT NULL,
        TaskId NVARCHAR(255),
        ExecutionLogId NVARCHAR(255),
        FileName NVARCHAR(500),
        RuleName NVARCHAR(500),
        Category NVARCHAR(100),
        Priority NVARCHAR(50),
        LineNumber INT,
        Message NVARCHAR(MAX),
        CreatedAt DATETIME2 DEFAULT GETDATE()
    );
    PRINT '✓ Created App_PMDScans table';
END

-- Create App_ProfilePermissions table if it doesn't exist
IF OBJECT_ID('App_ProfilePermissions', 'U') IS NULL
BEGIN
    CREATE TABLE App_ProfilePermissions (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        OrgId NVARCHAR(255) NOT NULL,
        ExecutionLogId NVARCHAR(255),
        SalesforceId NVARCHAR(255),
        ProfileName NVARCHAR(500),
        PermissionName NVARCHAR(500),
        PermissionType NVARCHAR(100),
        CreatedAt DATETIME2 DEFAULT GETDATE()
    );
    PRINT '✓ Created App_ProfilePermissions table';
END

-- Create App_ProfileAssignmentCount table if it doesn't exist
IF OBJECT_ID('App_ProfileAssignmentCount', 'U') IS NULL
BEGIN
    CREATE TABLE App_ProfileAssignmentCount (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        OrgId NVARCHAR(255) NOT NULL,
        IntegrationId NVARCHAR(255),
        ProfileName NVARCHAR(500),
        AssignmentCount INT,
        CreatedAt DATETIME2 DEFAULT GETDATE()
    );
    PRINT '✓ Created App_ProfileAssignmentCount table';
END

-- Create TaskStatus table if it doesn't exist
IF OBJECT_ID('TaskStatus', 'U') IS NULL
BEGIN
    CREATE TABLE TaskStatus (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        TaskId NVARCHAR(255) NOT NULL,
        IntegrationId NVARCHAR(255),
        Status NVARCHAR(100),
        TaskType NVARCHAR(100),
        Progress INT DEFAULT 0,
        Message NVARCHAR(MAX),
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        UpdatedAt DATETIME2 DEFAULT GETDATE()
    );
    PRINT '✓ Created TaskStatus table';
END

-- Create UserLogin table if it doesn't exist
IF OBJECT_ID('UserLogin', 'U') IS NULL
BEGIN
    CREATE TABLE UserLogin (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        UserId NVARCHAR(255) NOT NULL,
        Email NVARCHAR(255),
        LoginTime DATETIME2 DEFAULT GETDATE(),
        IPAddress NVARCHAR(45),
        UserAgent NVARCHAR(MAX),
        Success BIT DEFAULT 1
    );
    PRINT '✓ Created UserLogin table';
END

-- Create users table if it doesn't exist
IF OBJECT_ID('users', 'U') IS NULL
BEGIN
    CREATE TABLE users (
        Id NVARCHAR(255) PRIMARY KEY,
        Email NVARCHAR(255) NOT NULL,
        Name NVARCHAR(500),
        Role NVARCHAR(100),
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        LastLogin DATETIME2
    );
    PRINT '✓ Created users table';
END
ELSE
BEGIN
    -- Add missing columns if they don't exist
    IF COL_LENGTH('users', 'CreatedAt') IS NULL
    BEGIN
        ALTER TABLE users ADD CreatedAt DATETIME2 DEFAULT GETDATE();
        PRINT '✓ Added users.CreatedAt column';
    END
    
    IF COL_LENGTH('users', 'IsActive') IS NULL
    BEGIN
        ALTER TABLE users ADD IsActive BIT DEFAULT 1;
        PRINT '✓ Added users.IsActive column';
    END
END

-- =====================================================
-- 3. FINAL VALIDATION
-- =====================================================

PRINT '';
PRINT '=== FINAL VALIDATION ===';

-- Check all tables exist
DECLARE @required_tables TABLE (table_name NVARCHAR(128));
INSERT INTO @required_tables VALUES 
    ('Account'), ('Integrations'), ('UserAccount'), ('organizations'), ('App_Account'), ('App_User'), ('Credentials'), ('Policy'), ('SecurityRule'), 
    ('PolicyRuleSetting'), ('App_HealthCheck'), ('App_PoliciesResult'), ('App_Overview'),
    ('App_PMDScans'), ('App_ProfilePermissions'), ('App_ProfileAssignmentCount'),
    ('TaskStatus'), ('UserLogin'), ('users');

DECLARE @missing_tables TABLE (table_name NVARCHAR(128));
INSERT INTO @missing_tables
SELECT rt.table_name 
FROM @required_tables rt
LEFT JOIN INFORMATION_SCHEMA.TABLES t ON t.TABLE_NAME = rt.table_name
WHERE t.TABLE_NAME IS NULL;

IF EXISTS (SELECT 1 FROM @missing_tables)
BEGIN
    PRINT '✗ Missing tables:';
    SELECT '  - ' + table_name FROM @missing_tables;
END
ELSE
BEGIN
    PRINT '✓ All required tables exist';
END

-- Check key columns in critical tables
IF OBJECT_ID('Account', 'U') IS NOT NULL AND 
   OBJECT_ID('Integrations', 'U') IS NOT NULL AND
   OBJECT_ID('UserAccount', 'U') IS NOT NULL AND
   COL_LENGTH('Account', 'IsActive') IS NOT NULL AND 
   COL_LENGTH('Integrations', 'Id') IS NOT NULL AND
   COL_LENGTH('UserAccount', 'Domain') IS NOT NULL
BEGIN
    PRINT '✓ All critical columns exist';
END
ELSE
BEGIN
    PRINT '✗ Some critical columns are missing';
END

PRINT '';
PRINT '=== SCHEMA FIX COMPLETE ===';
PRINT 'Please review the output above and address any remaining issues.';
PRINT 'If you see any errors, they may need manual intervention.';

GO
