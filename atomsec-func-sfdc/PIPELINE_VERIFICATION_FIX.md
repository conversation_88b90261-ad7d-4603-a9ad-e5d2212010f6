# Pipeline Verification Fix - Azure CLI Authentication Issue

## Problem Identified

The Azure DevOps pipeline was failing at the "Verify Production After Swap" step with the error:
```
ERROR: Please run 'az login' to setup account.
❌ Production verification failed after swap
```

## Root Cause

The verification step was using a `script` task instead of an `AzureCLI@2` task. When using `script` tasks, the Azure CLI commands don't have access to the service connection authentication context, causing the `az login` error.

## Fixes Applied

### 1. Fixed "Verify Production After Swap" Step

**Before (Problematic):**
```yaml
- script: |
    # ... verification logic ...
    az webapp show \
      --name $(functionAppName) \
      --resource-group $(resourceGroupName) \
      --query "{name:name,state:state,hostNames:defaultHostName}" \
      --output table
  displayName: 'Verify Production After Swap'
```

**After (Fixed):**
```yaml
- task: AzureCLI@2
  displayName: 'Verify Production After Swap'
  inputs:
    azureSubscription: '$(sfdcServiceConnection)'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      # ... same verification logic ...
      az webapp show \
        --name $(functionAppName) \
        --resource-group $(resourceGroupName) \
        --query "{name:name,state:state,hostNames:defaultHostName}" \
        --output table
```

### 2. Fixed "Cleanup Staging Slot" Step

**Before (Problematic):**
```yaml
- script: |
    az webapp stop --name $(functionAppName) --resource-group $(resourceGroupName) --slot $(stagingSlotName)
```

**After (Fixed):**
```yaml
- task: AzureCLI@2
  displayName: 'Cleanup Staging Slot'
  inputs:
    azureSubscription: '$(sfdcServiceConnection)'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      az webapp stop --name $(functionAppName) --resource-group $(resourceGroupName) --slot $(stagingSlotName)
```

## Key Changes Made

1. **Converted `script` tasks to `AzureCLI@2` tasks** for steps that use Azure CLI commands
2. **Added proper `azureSubscription` input** to reference the service connection
3. **Maintained the same script logic** while ensuring proper authentication context

## Why This Fixes the Issue

- **`AzureCLI@2` tasks** automatically authenticate using the specified service connection
- **`script` tasks** run in a basic shell environment without Azure authentication context
- **Service connection** provides the necessary credentials and subscription context for Azure CLI commands

## Verification Steps That Don't Need Azure CLI

The following verification steps correctly use `curl` commands and don't need Azure CLI authentication:
- Basic connectivity tests
- Health endpoint checks
- API endpoint testing

## Recommendations for Future Pipeline Development

### 1. Always Use AzureCLI@2 for Azure Commands

```yaml
# ✅ CORRECT - Use AzureCLI@2 for Azure CLI commands
- task: AzureCLI@2
  inputs:
    azureSubscription: '$(serviceConnection)'
    scriptType: 'bash'
    inlineScript: |
      az webapp show --name myapp --resource-group myrg

# ❌ INCORRECT - Don't use script for Azure CLI commands
- script: |
    az webapp show --name myapp --resource-group myrg
```

### 2. Use Script Tasks Only for Non-Azure Operations

```yaml
# ✅ CORRECT - Use script for system commands, curl, etc.
- script: |
    curl -f https://myapp.azurewebsites.net/api/health
    python -m pytest tests/

# ✅ CORRECT - Use script for file operations
- script: |
    ls -la
    cat config.json
```

### 3. Service Connection Best Practices

- Always reference the correct service connection variable
- Ensure service connections have appropriate permissions
- Use different service connections for different environments (dev, staging, prod)

## Testing the Fix

After applying these changes:

1. **Commit and push** the updated pipeline
2. **Trigger a new build** to verify the fix
3. **Monitor the verification steps** to ensure they complete successfully
4. **Check that Azure CLI commands** now have proper authentication context

## Expected Results

- ✅ Production verification should complete successfully
- ✅ Azure CLI commands should authenticate properly
- ✅ No more "az login" errors
- ✅ Pipeline should complete all stages successfully

## Additional Notes

- The function app itself was working correctly - this was purely a pipeline configuration issue
- No application changes were needed
- The fix maintains all existing verification logic while ensuring proper authentication
- Future deployments should now complete successfully through all verification stages


