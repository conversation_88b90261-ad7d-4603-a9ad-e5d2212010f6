# SFDC Service Deployment Strategy

## Overview

This document outlines the deployment strategy for the SFDC microservice in the separated architecture. The SFDC service is now deployed independently from the DB service, maintaining clear separation of concerns.

## Pipeline Configuration

### Current Pipeline: `pipeline-func-sfdc-dev.yml`

**Purpose**: Deploy only the SFDC Function App (`func-atomsec-sfdc-dev`)

**Triggers**:
- `dev` branch
- `feature/profiles-db` branch

**Target**: 
- Azure Function App: `func-atomsec-sfdc-dev`
- Resource Group: `atomsec-dev-backend`
- Runtime: Python 3.11

### Deployment Strategy

1. **Staging Deployment**: 
   - Deploys to staging slot first
   - Runs tests against staging environment
   - URL: `https://func-atomsec-sfdc-dev-stage.azurewebsites.net/`

2. **Production Swap**:
   - After successful testing, swaps staging to production
   - Zero-downtime deployment
   - URL: `https://func-atomsec-sfdc-dev.azurewebsites.net/`

3. **Artifact Publishing**:
   - Publishes build artifacts for rollback capability
   - Artifact name: `sfdc-drop`

## Separated Architecture

### SFDC Service Responsibilities
- Salesforce integration and authentication
- Metadata extraction and processing
- Task orchestration and background processing
- Policy-based task filtering
- Business logic for Salesforce operations

### DB Service (Separate Pipeline)
- Database operations (CRUD)
- User authentication and authorization
- Data storage and retrieval
- API endpoints for data access

### Communication
- SFDC service communicates with DB service via HTTP API calls
- Uses `shared/db_service_client.py` for all database operations
- No direct database access from SFDC service

## Key Changes from Previous Architecture

### Before (Monolithic)
- Single pipeline deployed both SFDC and DB services
- Direct database access from SFDC service
- Tightly coupled architecture

### After (Microservices)
- Separate pipelines for each service
- SFDC service routes all DB operations through DB service
- Loosely coupled, independently deployable services

## Environment Configuration

### Service Connections
- **SFDC Service**: `sc-atomsec-dev-backend`
- **Resource Group**: `atomsec-dev-backend`

### Runtime Configuration
- **Python Version**: 3.11
- **Functions Runtime**: ~4
- **Deployment Method**: zipDeploy

## Testing Strategy

### Automated Tests
- Runs pytest against staging environment
- Tests SFDC-specific functionality
- Validates API endpoints and business logic

### Test Environment
- **Staging URL**: `https://func-atomsec-sfdc-dev-stage.azurewebsites.net/`
- **Production URL**: `https://func-atomsec-sfdc-dev.azurewebsites.net/`

## Dependencies

### Build Dependencies
- Python 3.11
- ODBC drivers for SQL Server
- Python packages from `requirements.txt`

### Runtime Dependencies
- DB Service API (for database operations)
- Azure Storage (for blob and queue operations)
- Azure Service Bus (for task orchestration)
- Salesforce APIs (for metadata extraction)

## Deployment Process

1. **Code Push**: Push to `dev` or `feature/profiles-db` branch
2. **Build Trigger**: Pipeline automatically triggers
3. **Environment Setup**: Install Python 3.11 and dependencies
4. **Package Build**: Create deployment package
5. **Staging Deploy**: Deploy to staging slot
6. **Testing**: Run automated tests
7. **Production Swap**: Swap staging to production
8. **Artifact Storage**: Store deployment artifacts

## Monitoring and Rollback

### Monitoring
- Application Insights integration
- Function App logs and metrics
- Health check endpoints

### Rollback Strategy
- Use Azure portal slot swap for immediate rollback
- Deploy previous artifact version if needed
- Monitor application health after deployment

## Security Considerations

### Authentication
- System-assigned managed identity
- Azure AD integration
- JWT token validation

### Network Security
- HTTPS only communication
- CORS configuration for allowed origins
- IP restrictions if needed

### Secrets Management
- Azure Key Vault integration
- Environment variables for configuration
- No hardcoded secrets in code

## Best Practices

1. **Independent Deployment**: Each service can be deployed independently
2. **API Versioning**: Maintain API compatibility between services
3. **Error Handling**: Graceful degradation when DB service is unavailable
4. **Logging**: Comprehensive logging for debugging and monitoring
5. **Testing**: Automated tests for each deployment
6. **Documentation**: Keep deployment documentation up to date

## Troubleshooting

### Common Issues
1. **DB Service Unavailable**: SFDC service includes fallback mechanisms
2. **Authentication Failures**: Check Azure AD configuration
3. **Deployment Failures**: Verify service connections and permissions
4. **Test Failures**: Check staging environment configuration

### Debug Steps
1. Check Azure Function App logs
2. Verify service connections in Azure DevOps
3. Test API endpoints manually
4. Review Application Insights telemetry
5. Check resource group permissions

## Future Considerations

1. **Auto-scaling**: Configure based on load patterns
2. **Multi-region**: Consider deployment to multiple regions
3. **Blue-Green Deployment**: Enhanced deployment strategy
4. **Container Deployment**: Consider containerization for consistency
5. **Infrastructure as Code**: ARM templates or Terraform for infrastructure

This deployment strategy ensures reliable, independent deployment of the SFDC microservice while maintaining the benefits of the separated architecture.
