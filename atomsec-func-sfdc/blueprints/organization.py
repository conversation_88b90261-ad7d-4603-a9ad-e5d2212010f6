"""
Organization Management Blueprint

This module provides endpoints for managing Salesforce organizations:
- Fetching all organizations
- Connecting new organizations
- Disconnecting organizations
- Rescanning organizations
- Viewing organization details

Best practices implemented:
- Proper error handling and logging
- Input validation
- Centralized configuration
- Reusable utility functions
"""

import logging
import azure.functions as func
import json
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

# Import shared modules
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.azure_services import is_local_dev
from shared.utils import create_json_response, handle_exception, get_salesforce_access_token, execute_salesforce_query
from shared.auth_utils import get_current_user, require_auth
from shared.cors_middleware import cors_middleware

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Lazy-initialized repositories
_org_table_repo = None
_org_sql_repo = None

def get_org_table_repo():
    """Lazy initialize the organization table repository"""
    global _org_table_repo
    if _org_table_repo is None:
        try:
            _org_table_repo = TableStorageRepository(table_name="Organizations")
            logger.info("Initialized organization table repository")
        except Exception as e:
            logger.error(f"Failed to initialize organization table repository: {str(e)}")
            _org_table_repo = None
    return _org_table_repo

def get_org_sql_repo():
    """Lazy initialize the organization SQL repository"""
    global _org_sql_repo
    if _org_sql_repo is None and not is_local_dev():
        try:
            _org_sql_repo = SqlDatabaseRepository(table_name="UserAccount")
            logger.info("Initialized organization SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize organization SQL repository: {str(e)}")
            _org_sql_repo = None
    return _org_sql_repo

def get_all_organizations(include_inactive: bool = False, user_email: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get all organizations

    Args:
        include_inactive: Whether to include inactive organizations
        user_email: Filter by user email

    Returns:
        List[Dict[str, Any]]: List of organizations
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            org_repo = get_org_table_repo()
            if not org_repo:
                logger.error("Organization table repository not available")
                return []

            # Build filter query
            filter_parts = []
            if not include_inactive:
                filter_parts.append("IsActive eq true")
            if user_email:
                filter_parts.append(f"UserEmail eq '{user_email}'")

            filter_query = " and ".join(filter_parts) if filter_parts else None

            # Query organizations
            entities = org_repo.query_entities(filter_query)

            # Convert to list of dictionaries
            orgs = []
            for entity in entities:
                org = {
                    "id": entity.get("RowKey"),
                    "name": entity.get("Name", ""),
                    "instanceUrl": entity.get("InstanceUrl", ""),
                    "type": entity.get("Type", "Salesforce"),
                    "description": entity.get("Description", ""),
                    "isActive": entity.get("IsActive", True),
                    "lastScan": entity.get("LastScan", ""),
                    "createdAt": entity.get("CreatedAt", ""),
                    "userEmail": entity.get("UserEmail", "")
                }
                orgs.append(org)

            return orgs
        else:
            # Use SQL Database for production
            org_repo = get_org_sql_repo()
            if not org_repo:
                logger.error("Organization SQL repository not available")
                return []

            # Build SQL query
            query = "SELECT Id, Name, InstanceUrl, Type, Description, IsActive, LastScan, CreatedAt, UserEmail FROM UserAccount"
            where_clauses = []
            params = []

            if not include_inactive:
                where_clauses.append("IsActive = ?")
                params.append(True)

            if user_email:
                where_clauses.append("UserEmail = ?")
                params.append(user_email)

            if where_clauses:
                query += " WHERE " + " AND ".join(where_clauses)

            # Execute query
            results = org_repo.execute_query(query, tuple(params))

            # Convert to list of dictionaries
            orgs = []
            for row in results:
                org = {
                    "id": row[0],
                    "name": row[1],
                    "instanceUrl": row[2],
                    "type": row[3],
                    "description": row[4],
                    "isActive": row[5],
                    "lastScan": row[6],
                    "createdAt": row[7],
                    "userEmail": row[8]
                }
                orgs.append(org)

            return orgs
    except Exception as e:
        logger.error(f"Error getting organizations: {str(e)}")
        return []

def create_organization(name: str, instance_url: str, org_type: str, description: str, user_email: str) -> Optional[str]:
    """
    Create a new organization

    Args:
        name: Organization name
        instance_url: Salesforce instance URL
        org_type: Organization type (e.g., Salesforce)
        description: Organization description
        user_email: User email

    Returns:
        str: Organization ID if successful, None otherwise
    """
    try:
        # Generate organization ID
        org_id = f"org_{datetime.now().strftime('%Y%m%d%H%M%S')}"

        if is_local_dev():
            # Use Azure Table Storage for local development
            org_repo = get_org_table_repo()
            if not org_repo:
                logger.error("Organization table repository not available")
                return None

            # Create organization entity
            org_entity = {
                "PartitionKey": "organization",
                "RowKey": org_id,
                "Name": name,
                "InstanceUrl": instance_url,
                "Type": org_type,
                "Description": description,
                "IsActive": True,
                "LastScan": "",
                "CreatedAt": datetime.now().isoformat(),
                "UserEmail": user_email
            }

            # Insert organization
            success = org_repo.insert_entity(org_entity)

            return org_id if success else None
        else:
            # Use SQL Database for production
            org_repo = get_org_sql_repo()
            if not org_repo:
                logger.error("Organization SQL repository not available")
                return None

            # Insert organization
            query = """
            INSERT INTO UserAccount (Id, Name, InstanceUrl, Type, Description, IsActive, LastScan, CreatedAt, UserEmail)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                org_id,
                name,
                instance_url,
                org_type,
                description,
                True,
                "",
                datetime.now().isoformat(),
                user_email
            )

            success = org_repo.execute_non_query(query, params)

            return org_id if success else None
    except Exception as e:
        logger.error(f"Error creating organization: {str(e)}")
        return None

def update_organization_status(org_id: str, is_active: bool) -> bool:
    """
    Update organization status

    Args:
        org_id: Organization ID
        is_active: Whether the organization is active

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            org_repo = get_org_table_repo()
            if not org_repo:
                logger.error("Organization table repository not available")
                return False

            # Get organization entity
            filter_query = f"RowKey eq '{org_id}'"
            entities = org_repo.query_entities(filter_query)

            if not entities:
                logger.warning(f"Organization not found: {org_id}")
                return False

            org_entity = entities[0]

            # Update status
            org_entity["IsActive"] = is_active

            return org_repo.update_entity(org_entity)
        else:
            # Use SQL Database for production
            org_repo = get_org_sql_repo()
            if not org_repo:
                logger.error("Organization SQL repository not available")
                return False

            # Update status
            query = "UPDATE UserAccount SET IsActive = ? WHERE Id = ?"
            params = (is_active, org_id)

            return org_repo.execute_non_query(query, params)
    except Exception as e:
        logger.error(f"Error updating organization status: {str(e)}")
        return False

def update_organization_last_scan(org_id: str) -> bool:
    """
    Update organization last scan timestamp

    Args:
        org_id: Organization ID

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            org_repo = get_org_table_repo()
            if not org_repo:
                logger.error("Organization table repository not available")
                return False

            # Get organization entity
            filter_query = f"RowKey eq '{org_id}'"
            entities = org_repo.query_entities(filter_query)

            if not entities:
                logger.warning(f"Organization not found: {org_id}")
                return False

            org_entity = entities[0]

            # Update last scan
            org_entity["LastScan"] = datetime.now().isoformat()

            return org_repo.update_entity(org_entity)
        else:
            # Use SQL Database for production
            org_repo = get_org_sql_repo()
            if not org_repo:
                logger.error("Organization SQL repository not available")
                return False

            # Update last scan
            query = "UPDATE UserAccount SET LastScan = ? WHERE Id = ?"
            params = (datetime.now().isoformat(), org_id)

            return org_repo.execute_non_query(query, params)
    except Exception as e:
        logger.error(f"Error updating organization last scan: {str(e)}")
        return False

def get_organization_by_instance_url(instance_url: str) -> Optional[Dict[str, Any]]:
    """
    Get organization by instance URL

    Args:
        instance_url: Salesforce instance URL

    Returns:
        Dict[str, Any]: Organization data or None if not found
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            org_repo = get_org_table_repo()
            if not org_repo:
                logger.error("Organization table repository not available")
                return None

            # Query organizations
            filter_query = f"InstanceUrl eq '{instance_url}'"
            entities = org_repo.query_entities(filter_query)

            if not entities:
                logger.warning(f"Organization not found for instance URL: {instance_url}")
                return None

            # Convert to dictionary
            org_entity = entities[0]
            org = {
                "id": org_entity.get("RowKey"),
                "name": org_entity.get("Name", ""),
                "instanceUrl": org_entity.get("InstanceUrl", ""),
                "type": org_entity.get("Type", "Salesforce"),
                "description": org_entity.get("Description", ""),
                "isActive": org_entity.get("IsActive", True),
                "lastScan": org_entity.get("LastScan", ""),
                "createdAt": org_entity.get("CreatedAt", ""),
                "userEmail": org_entity.get("UserEmail", "")
            }

            return org
        else:
            # Use SQL Database for production
            org_repo = get_org_sql_repo()
            if not org_repo:
                logger.error("Organization SQL repository not available")
                return None

            # Build SQL query
            query = "SELECT Id, Name, InstanceUrl, Type, Description, IsActive, LastScan, CreatedAt, UserEmail FROM UserAccount WHERE InstanceUrl = ?"
            params = (instance_url,)

            # Execute query
            results = org_repo.execute_query(query, params)

            if not results:
                logger.warning(f"Organization not found for instance URL: {instance_url}")
                return None

            # Convert to dictionary
            row = results[0]
            org = {
                "id": row[0],
                "name": row[1],
                "instanceUrl": row[2],
                "type": row[3],
                "description": row[4],
                "isActive": row[5],
                "lastScan": row[6],
                "createdAt": row[7],
                "userEmail": row[8]
            }

            return org
    except Exception as e:
        logger.error(f"Error getting organization by instance URL: {str(e)}")
        return None

async def fetch_organization_data(instance_url: str, access_token: str) -> Dict[str, Any]:
    """
    Fetch organization data from Salesforce

    Args:
        instance_url: Salesforce instance URL
        access_token: Salesforce access token

    Returns:
        Dict[str, Any]: Organization data
    """
    try:
        # Fetch organization information
        org_query = "SELECT Id, Name, OrganizationType, InstanceName, IsSandbox FROM Organization LIMIT 1"
        org_result = await execute_salesforce_query(org_query, access_token, instance_url)

        # Fetch user information
        user_query = "SELECT Id, Name, Email, Profile.Name FROM User WHERE IsActive = true LIMIT 100"
        user_result = await execute_salesforce_query(user_query, access_token, instance_url)

        # Fetch profile information
        profile_query = "SELECT Id, Name FROM Profile LIMIT 100"
        profile_result = await execute_salesforce_query(profile_query, access_token, instance_url)

        # Fetch permission set information
        permission_set_query = "SELECT Id, Name, IsCustom FROM PermissionSet WHERE IsCustom = true LIMIT 100"
        permission_set_result = await execute_salesforce_query(permission_set_query, access_token, instance_url)

        # Fetch security health check risks
        security_query = "SELECT Id, RiskType, Setting, SettingGroup, OrgValue, StandardValue,SettingRiskCategory FROM SecurityHealthCheckRisks LIMIT 100"
        security_result = await execute_salesforce_query(security_query, access_token, instance_url)

        # Compile results
        org_data = {
            "organization": org_result.get("records", [{}])[0] if org_result and "records" in org_result else {},
            "users": user_result.get("records", []) if user_result and "records" in user_result else [],
            "profiles": profile_result.get("records", []) if profile_result and "records" in profile_result else [],
            "permissionSets": permission_set_result.get("records", []) if permission_set_result and "records" in permission_set_result else [],
            "securityRisks": security_result.get("records", []) if security_result and "records" in security_result else [],
            "timestamp": datetime.now().isoformat()
        }

        return org_data
    except Exception as e:
        logger.error(f"Error fetching organization data: {str(e)}")
        return {
            "organization": {},
            "users": [],
            "profiles": [],
            "permissionSets": [],
            "securityRisks": [],
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@bp.route(route="orgs", methods=["GET", "OPTIONS"])
def get_orgs(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get all organizations endpoint

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response with organizations
    """
    logger.info('Processing get organizations request...')

    # Handle OPTIONS request
    if req.method == "OPTIONS":
        # Define allowed origins
        allowed_origins = ["http://localhost:3000"]

        # Get the origin from the request
        origin = req.headers.get("Origin", "")

        # Return CORS headers
        return func.HttpResponse(
            status_code=204,
            headers={
                "Access-Control-Allow-Origin": origin if origin in allowed_origins else "",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
                "Access-Control-Allow-Credentials": "true",
                "Access-Control-Max-Age": "86400"
            }
        )

    try:
        # Get current user
        current_user = get_current_user(req)
        if not current_user:
            # Define allowed origins
            allowed_origins = ["http://localhost:3000"]

            # Get the origin from the request
            origin = req.headers.get("Origin", "")

            # Return unauthorized response with CORS headers
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401,
                headers={
                    "Access-Control-Allow-Origin": origin if origin in allowed_origins else "",
                    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
                    "Access-Control-Allow-Credentials": "true"
                }
            )

        # Get query parameters
        include_inactive = req.params.get("includeInactive", "false").lower() == "true"

        # Get organizations for the current user
        organizations = get_all_organizations(include_inactive, current_user["email"])

        # Define allowed origins
        allowed_origins = ["http://localhost:3000"]

        # Get the origin from the request
        origin = req.headers.get("Origin", "")

        # Return response with CORS headers
        return func.HttpResponse(
            json.dumps(create_json_response(organizations)),
            mimetype="application/json",
            status_code=200,
            headers={
                "Access-Control-Allow-Origin": origin if origin in allowed_origins else "",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
                "Access-Control-Allow-Credentials": "true"
            }
        )
    except Exception as e:
        error_response = handle_exception(e, "get_orgs")

        # Define allowed origins
        allowed_origins = ["http://localhost:3000"]

        # Get the origin from the request
        origin = req.headers.get("Origin", "")

        # Return error response with CORS headers
        return func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": origin if origin in allowed_origins else "",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
                "Access-Control-Allow-Credentials": "true"
            }
        )

@bp.route(route="connect-org", methods=["POST"])
def connect_org(req: func.HttpRequest) -> func.HttpResponse:
    """
    Connect a new organization endpoint

    Args:
        req: HTTP request with organization data

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing connect organization request...')

    try:
        # Get current user
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Parse request body
        req_body = req.get_json()

        # Validate required fields
        if not req_body or not all(k in req_body for k in ["name", "instanceUrl"]):
            return func.HttpResponse(
                json.dumps(create_json_response("Name and instanceUrl are required", 400)),
                mimetype="application/json",
                status_code=400
            )

        name = req_body.get("name")
        instance_url = req_body.get("instanceUrl")
        org_type = req_body.get("type", "Salesforce")
        description = req_body.get("description", "")

        # Create organization
        org_id = create_organization(name, instance_url, org_type, description, current_user["email"])
        if not org_id:
            return func.HttpResponse(
                json.dumps(create_json_response("Failed to connect organization", 500)),
                mimetype="application/json",
                status_code=500
            )

        # Return organization ID
        response_data = {
            "id": org_id,
            "name": name,
            "instanceUrl": instance_url,
            "type": org_type,
            "description": description,
            "isActive": True,
            "lastScan": "",
            "createdAt": datetime.now().isoformat(),
            "userEmail": current_user["email"]
        }

        return func.HttpResponse(
            json.dumps(create_json_response(response_data)),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        error_response = handle_exception(e, "connect_org")
        return func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="disconnect-org", methods=["POST"])
def disconnect_org(req: func.HttpRequest) -> func.HttpResponse:
    """
    Disconnect an organization endpoint

    Args:
        req: HTTP request with organization ID

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing disconnect organization request...')

    try:
        # Get current user
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Parse request body
        req_body = req.get_json()

        # Validate required fields
        if not req_body or "id" not in req_body:
            return func.HttpResponse(
                json.dumps(create_json_response("Organization ID is required", 400)),
                mimetype="application/json",
                status_code=400
            )

        org_id = req_body.get("id")

        # Update organization status
        success = update_organization_status(org_id, False)
        if not success:
            return func.HttpResponse(
                json.dumps(create_json_response("Failed to disconnect organization", 500)),
                mimetype="application/json",
                status_code=500
            )

        return func.HttpResponse(
            json.dumps(create_json_response({"id": org_id, "status": "disconnected"})),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        error_response = handle_exception(e, "disconnect_org")
        return func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="org-details", methods=["GET", "OPTIONS"])
async def get_org_details(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get organization details endpoint

    Args:
        req: HTTP request with instance URL

    Returns:
        func.HttpResponse: HTTP response with organization details
    """
    logger.info('Processing get organization details request...')

    # Handle OPTIONS request
    if req.method == "OPTIONS":
        return cors_middleware(req, func.HttpResponse(status_code=204))

    try:
        # Get current user
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Get instance URL from query parameters
        instance_url = req.params.get("instanceUrl")
        if not instance_url:
            return func.HttpResponse(
                json.dumps(create_json_response("Instance URL is required", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Get organization from database
        org = get_organization_by_instance_url(instance_url)

        # Initialize response data
        response_data = {
            "organization": org if org else {"instanceUrl": instance_url},
            "needsSync": True,
            "timestamp": datetime.now().isoformat()
        }

        # Get Salesforce access token
        access_token, _ = get_salesforce_access_token()

        # If we have a valid access token, try to fetch data from Salesforce
        if access_token:
            try:
                # Fetch data from Salesforce using the organization's instance URL
                # Make sure to use the instance URL from the request, not the hardcoded one
                org_data = await fetch_organization_data(instance_url, access_token)

                # Update response data
                response_data.update({
                    "salesforceData": org_data,
                    "needsSync": False
                })

                # If organization exists in database, update last scan timestamp
                if org and "id" in org:
                    update_organization_last_scan(org["id"])
            except Exception as e:
                logger.error(f"Error fetching Salesforce data: {str(e)}")
                response_data["error"] = f"Error fetching Salesforce data: {str(e)}"

        # Return response
        response = func.HttpResponse(
            json.dumps(create_json_response(response_data)),
            mimetype="application/json",
            status_code=200
        )

        return cors_middleware(req, response)
    except Exception as e:
        error_response = handle_exception(e, "get_org_details")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        return cors_middleware(req, response)

@bp.route(route="integration/scan/{integration_id}", methods=["POST"])
@require_auth
def scan_integration_direct(req: func.HttpRequest) -> func.HttpResponse:
    """
    Direct integration scan endpoint (like dev branch approach)
    This performs the health check directly and creates proper task records
    """
    try:
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Integration ID is required"}),
                mimetype="application/json",
                status_code=400
            )

        logger.info(f"Direct scan for integration ID: {integration_id}")

        # Get current user
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Unauthorized"}),
                mimetype="application/json",
                status_code=401
            )

        # Generate a single execution_log_id for the entire scan
        execution_log_id = str(uuid.uuid4())

        # Create execution log record for the scan
        from shared.db_service_client import get_db_client
        db_client = get_db_client()
        try:
            db_client.create_execution_log({
                'ExecutionLogId': execution_log_id,
                'OrgId': integration_id,
                'ExecutionType': 'Integration_Scan',
                'Status': 'Pending',
                'Priority': 'High',
                'StartTime': datetime.now(),
                'ExecutedBy': current_user.get("id", "system"),
                'CreatedAt': datetime.now(),
                'UpdatedAt': datetime.now()
            })
            logger.info(f"Created execution log record: {execution_log_id}")
        except Exception as e:
            logger.warning(f"Failed to create execution log record: {e}, continuing with scan")

        # Create a task record for tracking (but don't enqueue for processing)
        from shared.background_processor import BackgroundProcessor
        from shared.db_service_client import get_db_client

        # Generate task ID
        task_id = str(uuid.uuid4())

        # Create task record directly via database service (for tracking only)
        db_client = get_db_client()
        task_data = {
            "task_type": "health_check",
            "org_id": integration_id,
            "user_id": str(current_user.get("id", "system")),
            "status": "running",
            "priority": "high",
            "progress": 0,
            "message": "Direct scan in progress",
            "execution_log_id": execution_log_id,
            "params": json.dumps({"integration_id": integration_id, "execution_log_id": execution_log_id})
        }

        try:
            task_created = db_client.create_task(task_data)
            logger.info(f"Created task record for tracking: {task_created}")
        except Exception as e:
            logger.warning(f"Failed to create task record for integration {integration_id}: {e}, continuing with scan")
            task_created = task_id  # Use a fallback task ID

        # Get integration details from DB service
        from shared.db_service_client import get_db_client
        db_client = get_db_client()
        integration = db_client.get_integration_by_id(integration_id)

        if not integration:
            return func.HttpResponse(
                json.dumps({"success": False, "error": f"Integration {integration_id} not found"}),
                mimetype="application/json",
                status_code=404
            )

        if not integration.get("is_active", False):
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Integration is not active"}),
                mimetype="application/json",
                status_code=400
            )

        # Get Salesforce access token using integration credentials
        from shared.auth_service import authenticate_salesforce_integration

        # Get credentials for this integration from DB service
        credentials = db_client.get_integration_credentials(integration_id)
        if not credentials:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Integration credentials not found"}),
                mimetype="application/json",
                status_code=404
            )

        # Extract required parameters from integration and credentials
        tenant_url = integration.get("tenant_url")
        environment = integration.get("environment", "production")

        # Handle different credential storage formats
        from shared.common import is_local_dev
        if is_local_dev():
            # Local dev stores credentials as separate entities
            client_id_data = credentials.get("client-id", {})
            client_secret_data = credentials.get("client-secret", {})
            private_key_data = credentials.get("private-key", {})
            username_data = credentials.get("username", {})

            client_id = client_id_data.get("value") if client_id_data else None
            client_secret = client_secret_data.get("value") if client_secret_data else None
            private_key = private_key_data.get("value") if private_key_data else None
            jwt_username = username_data.get("value") if username_data else None
            auth_flow = client_id_data.get("auth_flow", "client_credentials") if client_id_data else "client_credentials"
        else:
            # Production stores credentials in a single record
            client_id = credentials.get("client_id")
            client_secret = credentials.get("client_secret")
            private_key = credentials.get("private_key")
            jwt_username = credentials.get("username")
            auth_flow = credentials.get("auth_flow", "client_credentials")

        if not tenant_url or not client_id:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Missing required integration parameters (tenant_url, client_id)"}),
                mimetype="application/json",
                status_code=400
            )

        # Authenticate with Salesforce using the centralized auth service
        auth_success, auth_error_message, sf_instance, connection_details = authenticate_salesforce_integration(
            auth_flow_type=auth_flow,
            tenant_url=tenant_url,
            environment=environment,
            client_id=client_id,
            client_secret=client_secret,
            jwt_username=jwt_username,
            private_key=private_key
        )

        if not auth_success or not connection_details:
            return func.HttpResponse(
                json.dumps({"success": False, "error": f"Failed to authenticate with Salesforce: {auth_error_message}"}),
                mimetype="application/json",
                status_code=500
            )

        # Extract access token and instance URL from connection details
        access_token = connection_details.get("access_token")
        instance_url = connection_details.get("instance_url")

        # Execute health check directly (like dev branch)
        try:
            # Import the security analysis functions
            from blueprints.security_analysis import (
                fetch_security_health_check_risks,
                calculate_health_score,
                process_and_store_policies_results
            )

            # Fetch security health check risks directly
            import asyncio
            loop = asyncio.get_event_loop()

            if loop.is_running():
                # If loop is already running, create a new one in a thread
                import concurrent.futures

                def run_health_check():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        risks = new_loop.run_until_complete(
                            fetch_security_health_check_risks(access_token, instance_url)
                        )
                        health_score = new_loop.run_until_complete(
                            calculate_health_score(risks, access_token, instance_url)
                        )
                        return risks, health_score
                    finally:
                        new_loop.close()

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_health_check)
                    risks, health_score = future.result()
            else:
                # If loop is not running, use it directly
                risks = loop.run_until_complete(
                    fetch_security_health_check_risks(access_token, instance_url)
                )
                health_score = loop.run_until_complete(
                    calculate_health_score(risks, access_token, instance_url)
                )

            # Process and store results in PoliciesResult table using the same execution_log_id
            process_and_store_policies_results(risks, integration_id, execution_log_id)

            logger.info(f"Health check completed for {integration_id}. Score: {health_score}, Risks: {len(risks)}")

            # Also trigger profiles and permission sets tasks using the same execution_log_id
            try:
                # Create profiles_permission_sets task
                profiles_task_id = str(uuid.uuid4())
                profiles_task_data = {
                    "task_type": "profiles_permission_sets",
                    "org_id": integration_id,
                    "user_id": str(current_user.get("id", "system")),
                    "status": "pending",
                    "priority": "medium",
                    "progress": 0,
                    "message": "Profiles and permission sets task queued",
                    "execution_log_id": execution_log_id,
                    "params": json.dumps({
                        "integration_id": integration_id,
                        "execution_log_id": execution_log_id,
                        "access_token": access_token,
                        "instance_url": instance_url
                    })
                }
                profiles_task_created = db_client.create_task(profiles_task_data)
                logger.info(f"Created profiles task: {profiles_task_created}")

                # Create permission_sets task
                permission_sets_task_id = str(uuid.uuid4())
                permission_sets_task_data = {
                    "task_type": "permission_sets",
                    "org_id": integration_id,
                    "user_id": str(current_user.get("id", "system")),
                    "status": "pending",
                    "priority": "medium",
                    "progress": 0,
                    "message": "Permission sets task queued",
                    "execution_log_id": execution_log_id,
                    "params": json.dumps({
                        "integration_id": integration_id,
                        "execution_log_id": execution_log_id,
                        "access_token": access_token,
                        "instance_url": instance_url
                    })
                }
                permission_sets_task_created = db_client.create_task(permission_sets_task_data)
                logger.info(f"Created permission sets task: {permission_sets_task_created}")

            except Exception as task_error:
                logger.warning(f"Failed to create additional tasks for {integration_id}: {task_error}")

            # Update integration last scan timestamp via DB service
            from datetime import datetime
            update_data = {
                "last_scan": datetime.now().isoformat(),
                "health_score": health_score
            }
            db_client.update_integration(integration_id, update_data)

            # Update task status to completed if task was created
            if task_created:
                try:
                    db_client.update_task_status(
                        task_id=task_created,
                        status="completed",
                        progress=100,
                        message=f"Health check completed. Score: {health_score}, Risks: {len(risks)}"
                    )
                    logger.info(f"Updated task status to completed for task: {task_created}")
                except Exception as e:
                    logger.warning(f"Failed to update task status: {e}")

            # Return success response with health score
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": "Scan completed successfully",
                    "healthScore": health_score,
                    "totalRisks": len(risks),
                    "executionLogId": execution_log_id,
                    "taskId": task_created if task_created else None
                }),
                mimetype="application/json",
                status_code=200
            )

        except Exception as health_check_error:
            logger.error(f"Health check failed for {integration_id}: {str(health_check_error)}")

            # Update task status to failed if task was created
            if task_created:
                try:
                    db_client.update_task_status(
                        task_id=task_created,
                        status="failed",
                        progress=100,
                        message=f"Health check failed: {str(health_check_error)}"
                    )
                    logger.info(f"Updated task status to failed for task: {task_created}")
                except Exception as e:
                    logger.warning(f"Failed to update task status: {e}")

            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": f"Health check failed: {str(health_check_error)}"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error in scan_integration_direct: {str(e)}")
        return func.HttpResponse(
            json.dumps({"success": False, "error": str(e)}),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="rescan-org", methods=["POST"])
def rescan_org(req: func.HttpRequest) -> func.HttpResponse:
    """
    Rescan an organization endpoint

    This endpoint triggers a comprehensive scan of the Salesforce organization,
    including overview, health check, profiles, guest user risks, and PMD issues.
    All data is fetched and stored in a single operation.

    Args:
        req: HTTP request with organization ID or instance URL

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing rescan organization request...')

    try:
        # Get current user
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Parse request body
        req_body = req.get_json()

        # Validate required fields - accept either id or instanceUrl
        if not req_body or ("id" not in req_body and "instanceUrl" not in req_body):
            return func.HttpResponse(
                json.dumps(create_json_response("Organization ID or Instance URL is required", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Get organization ID and instance URL
        org_id = req_body.get("id")
        instance_url = req_body.get("instanceUrl")

        # If we have instance URL but no org ID, look up the org ID
        if not org_id and instance_url:
            org = get_organization_by_instance_url(instance_url)
            if not org:
                return func.HttpResponse(
                    json.dumps(create_json_response(f"Organization not found for instance URL: {instance_url}", 404)),
                    mimetype="application/json",
                    status_code=404
                )
            org_id = org.get("id")

        # Get organization details
        if is_local_dev():
            org_repo = get_org_table_repo()
            filter_query = f"RowKey eq '{org_id}'"
            entities = org_repo.query_entities(filter_query)

            if not entities:
                return func.HttpResponse(
                    json.dumps(create_json_response("Organization not found", 404)),
                    mimetype="application/json",
                    status_code=404
                )

            org = entities[0]
            instance_url = org.get("InstanceUrl", "")
        else:
            org_repo = get_org_sql_repo()
            query = "SELECT Id, InstanceUrl FROM UserAccount WHERE Id = ?"
            params = (org_id,)

            results = org_repo.execute_query(query, params)

            if not results:
                return func.HttpResponse(
                    json.dumps(create_json_response("Organization not found", 404)),
                    mimetype="application/json",
                    status_code=404
                )

            instance_url = results[0][1]

        # Get Salesforce access token
        access_token, _ = get_salesforce_access_token()
        if not access_token:
            return func.HttpResponse(
                json.dumps(create_json_response("Failed to obtain Salesforce access token", 500)),
                mimetype="application/json",
                status_code=500
            )

        # Generate a single execution_log_id for the entire scan
        execution_log_id = str(uuid.uuid4())

        # Create execution log record for the scan
        from shared.db_service_client import get_db_client
        db_client = get_db_client()
        try:
            db_client.create_execution_log({
                'ExecutionLogId': execution_log_id,
                'OrgId': org_id,
                'ExecutionType': 'Integration_Scan',
                'Status': 'Pending',
                'Priority': 'High',
                'StartTime': datetime.now(),
                'ExecutedBy': current_user.get("id", "system"),
                'CreatedAt': datetime.now(),
                'UpdatedAt': datetime.now()
            })
            logger.info(f"Created execution log record: {execution_log_id}")
        except Exception as e:
            logger.warning(f"Failed to create execution log record: {e}, continuing with scan")

        # No need for background processor - execute directly like dev branch

        # Execute health check directly (like dev branch) instead of queuing tasks
        try:
            # Import the security analysis functions
            from blueprints.security_analysis import (
                fetch_security_health_check_risks,
                calculate_health_score,
                process_and_store_policies_results
            )

            # Fetch security health check risks directly
            import asyncio
            loop = asyncio.get_event_loop()

            if loop.is_running():
                # If loop is already running, create a new one in a thread
                import concurrent.futures
                import threading

                def run_health_check():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        risks = new_loop.run_until_complete(
                            fetch_security_health_check_risks(access_token, instance_url)
                        )
                        health_score = new_loop.run_until_complete(
                            calculate_health_score(risks, access_token, instance_url)
                        )
                        return risks, health_score
                    finally:
                        new_loop.close()

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_health_check)
                    risks, health_score = future.result()
            else:
                # If loop is not running, use it directly
                risks = loop.run_until_complete(
                    fetch_security_health_check_risks(access_token, instance_url)
                )
                health_score = loop.run_until_complete(
                    calculate_health_score(risks, access_token, instance_url)
                )

            # Process and store results in PoliciesResult table using the same execution_log_id
            process_and_store_policies_results(risks, org_id, execution_log_id)

            logger.info(f"Health check completed for {org_id}. Score: {health_score}, Risks: {len(risks)}")

            # Return success response with health score
            response_data = {
                "success": True,
                "message": "Rescan completed successfully",
                "healthScore": health_score,
                "totalRisks": len(risks),
                "executionLogId": execution_log_id
            }

        except Exception as health_check_error:
            logger.error(f"Health check failed for {org_id}: {str(health_check_error)}")
            response_data = {
                "success": False,
                "message": f"Health check failed: {str(health_check_error)}"
            }

        # Update organization last scan timestamp
        success = update_organization_last_scan(org_id)
        if not success:
            logger.warning(f"Failed to update organization last scan timestamp for org ID: {org_id}")

        # Return response with health check results
        return func.HttpResponse(
            json.dumps(create_json_response(response_data)),
            mimetype="application/json",
            status_code=200 if response_data.get("success") else 500
        )
    except Exception as e:
        error_response = handle_exception(e, "rescan_org")
        return func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )
