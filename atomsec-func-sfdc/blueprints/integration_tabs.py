"""
Integration Tabs Blueprint

This module provides endpoints for fetching and syncing data for integration tabs:
- Overview
- Health Check
- Profiles and Permission Sets
- Guest User Profile Risks
"""

import json
import logging
import azure.functions as func
from typing import Dict, Any, Optional, List
from datetime import datetime
import asyncio

# Import shared modules
from shared.utils import create_json_response, handle_exception
from shared.cors_middleware import cors_middleware
from shared.auth_utils import get_current_user, require_auth
from shared.salesforce_utils import test_salesforce_connection
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.common import is_local_dev
from shared.azure_services import get_secret
from shared.background_processor import (
    BackgroundProcessor,
    TASK_STATUS_PENDING,
    TASK_STATUS_RUNNING,
    TASK_STATUS_COMPLETED,
    TASK_STATUS_FAILED,
    TASK_TYPE_HEALTH_CHECK,
    TASK_TYPE_PROFILES,
    TASK_PRIORITY_HIGH,
    TASK_PRIORITY_MEDIUM,
    TASK_PRIORITY_LOW
)

# Import task processor functions
from task_processor import get_integration_by_id

# Import profile system permissions module
from blueprints.profile_system_permissions import (
    fetch_profile_system_permissions,
    fetch_permission_set_system_permissions,
    save_profile_system_permissions,
    save_permission_set_system_permissions,
    get_profile_system_permissions,
    get_permission_set_system_permissions
)

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Lazy-initialized repositories
_integration_table_repo = None
_integration_sql_repo = None
_execution_log_repo = None
_overview_repo = None
_health_check_repo = None
_profile_permissions_repo = None

def get_integration_table_repo():
    """Lazy initialize the integration table repository"""
    global _integration_table_repo
    if _integration_table_repo is None:
        try:
            _integration_table_repo = TableStorageRepository(table_name="Integrations")
            logger.info("Initialized integration table repository")
        except Exception as e:
            logger.error(f"Failed to initialize integration table repository: {str(e)}")
            _integration_table_repo = None
    return _integration_table_repo

def get_integration_sql_repo():
    """Lazy initialize the integration SQL repository"""
    global _integration_sql_repo
    if _integration_sql_repo is None and not is_local_dev():
        try:
            _integration_sql_repo = SqlDatabaseRepository(table_name="UserAccount")
            logger.info("Initialized integration SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize integration SQL repository: {str(e)}")
            _integration_sql_repo = None
    return _integration_sql_repo

def get_execution_log_repo():
    """Lazy initialize the execution log repository"""
    global _execution_log_repo
    if _execution_log_repo is None and not is_local_dev():
        try:
            _execution_log_repo = SqlDatabaseRepository(table_name="App_ExecutionLog")
            logger.info("Initialized execution log repository")
        except Exception as e:
            logger.error(f"Failed to initialize execution log repository: {str(e)}")
            _execution_log_repo = None
    return _execution_log_repo

def get_overview_repo():
    """Lazy initialize the overview repository"""
    global _overview_repo
    if _overview_repo is None and not is_local_dev():
        try:
            _overview_repo = SqlDatabaseRepository(table_name="App_Overview")
            logger.info("Initialized overview repository")
        except Exception as e:
            logger.error(f"Failed to initialize overview repository: {str(e)}")
            _overview_repo = None
    return _overview_repo

def get_health_check_repo():
    """Lazy initialize the health check repository"""
    global _health_check_repo
    if _health_check_repo is None and not is_local_dev():
        try:
            _health_check_repo = SqlDatabaseRepository(table_name="App_HealthCheck")
            logger.info("Initialized health check repository")
        except Exception as e:
            logger.error(f"Failed to initialize health check repository: {str(e)}")
            _health_check_repo = None
    return _health_check_repo

def get_profile_permissions_repo():
    """Lazy initialize the profile permissions repository"""
    global _profile_permissions_repo
    if _profile_permissions_repo is None and not is_local_dev():
        try:
            _profile_permissions_repo = SqlDatabaseRepository(table_name="App_ProfilePermissions")
            logger.info("Initialized profile permissions repository")
        except Exception as e:
            logger.error(f"Failed to initialize profile permissions repository: {str(e)}")
            _profile_permissions_repo = None
    return _profile_permissions_repo

def create_execution_log(org_id: str, execution_type: str, user_id: str) -> Optional[str]:
    """
    Create an execution log entry

    Args:
        org_id: Organization ID
        execution_type: Type of execution (Overview, HealthCheck, ProfilePermissions, GuestUserRisks)
        user_id: User ID

    Returns:
        str: Execution log ID if successful, None otherwise
    """
    try:
        if is_local_dev():
            # For local development, just return a dummy ID
            return "local-execution-log-id"
        else:
            # Use SQL Database for production
            execution_log_repo = get_execution_log_repo()
            if not execution_log_repo:
                logger.error("Execution log repository not available")
                return None

            # Insert execution log
            query = """
            INSERT INTO App_ExecutionLog (OrgId, ExecutionType, Status, StartTime, ExecutedBy)
            VALUES (?, ?, ?, ?, ?)
            """
            params = (
                org_id,
                execution_type,
                "Pending",
                datetime.now().isoformat(),
                user_id
            )

            success = execution_log_repo.execute_non_query(query, params)

            if success:
                # Get the ID of the inserted execution log
                query = """
                SELECT TOP 1 Id FROM App_ExecutionLog
                WHERE OrgId = ? AND ExecutionType = ? AND ExecutedBy = ?
                ORDER BY StartTime DESC
                """
                results = execution_log_repo.execute_query(query, (org_id, execution_type, user_id))

                if results and len(results) > 0:
                    return str(results[0][0])

            return None
    except Exception as e:
        logger.error(f"Error creating execution log: {str(e)}")
        return None

def update_execution_log_status(execution_log_id: str, status: str) -> bool:
    """
    Update execution log status

    Args:
        execution_log_id: Execution log ID
        status: New status (Completed, Failed)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # For local development, just return True
            return True
        else:
            # Use SQL Database for production
            execution_log_repo = get_execution_log_repo()
            if not execution_log_repo:
                logger.error("Execution log repository not available")
                return False

            # Update execution log
            query = """
            UPDATE App_ExecutionLog
            SET Status = ?, EndTime = ?
            WHERE Id = ?
            """
            params = (
                status,
                datetime.now().isoformat(),
                execution_log_id
            )

            return execution_log_repo.execute_non_query(query, params)
    except Exception as e:
        logger.error(f"Error updating execution log status: {str(e)}")
        return False

async def get_salesforce_access_token(integration_id: str, tenant_url: str, environment: str) -> Optional[Dict[str, Any]]:
    """
    Get Salesforce access token

    Args:
        integration_id: Integration ID
        tenant_url: Tenant URL
        environment: Environment (production or sandbox)

    Returns:
        Dict[str, Any]: Access token and instance URL if successful, None otherwise
    """
    try:
        # Get client credentials from Key Vault or local storage
        service_name = f"salesforce-{integration_id}"
        client_id = get_secret(f"{service_name}-client-id")
        client_secret = get_secret(f"{service_name}-client-secret")

        if not client_id or not client_secret:
            logger.error(f"Failed to retrieve client credentials for integration {integration_id}")

            # For local development, try to retrieve from the Credentials table directly
            if is_local_dev():
                try:
                    # Get table service client
                    from azure.data.tables import TableServiceClient
                    from shared.config import get_storage_connection_string

                    connection_string = get_storage_connection_string()
                    table_service = TableServiceClient.from_connection_string(connection_string)

                    # Get credentials table
                    credentials_table_name = "Credentials"
                    table_client = table_service.get_table_client(credentials_table_name)

                    # Query for the client ID
                    try:
                        client_id_entity = table_client.get_entity(service_name, "client-id")
                        client_id = client_id_entity.get("Value")
                        logger.info(f"Successfully retrieved client ID for {service_name} from local storage")
                    except Exception as e:
                        logger.warning(f"Client ID not found in local storage: {str(e)}")

                    # Query for the client secret
                    try:
                        client_secret_entity = table_client.get_entity(service_name, "client-secret")
                        client_secret = client_secret_entity.get("Value")
                        logger.info(f"Successfully retrieved client secret for {service_name} from local storage")
                    except Exception as e:
                        logger.warning(f"Client secret not found in local storage: {str(e)}")
                except Exception as local_e:
                    logger.error(f"Error retrieving credentials from local storage: {str(local_e)}")

            # If still not found, return None
            if not client_id or not client_secret:
                return None

        # Make client credentials call to Salesforce
        is_sandbox = environment.lower() == "sandbox"
        success, error_message, connection_details = test_salesforce_connection(
            client_id=client_id,
            client_secret=client_secret,
            tenant_url=tenant_url,
            is_sandbox=is_sandbox
        )

        if not success:
            logger.error(f"Failed to authenticate with Salesforce: {error_message}")
            return None

        # Get access token from connection details
        access_token = connection_details.get("access_token")
        instance_url = connection_details.get("instance_url")

        if not access_token or not instance_url:
            logger.error("Failed to obtain access token from Salesforce")
            return None

        return {
            "access_token": access_token,
            "instance_url": instance_url
        }
    except Exception as e:
        logger.error(f"Error getting Salesforce access token: {str(e)}")
        return None

@bp.route(route="api/integration/{integration_id}/overview", methods=["GET"])
@require_auth
def get_integration_overview(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get integration overview

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info("Getting integration overview...")
    logger.info(f"Request URL: {req.url}")
    logger.info(f"Request method: {req.method}")
    logger.info(f"Request route params: {req.route_params}")
    logger.info(f"Request query params: {req.params}")

    try:
        # Get current user
        current_user = get_current_user(req)
        if not current_user:
            logger.warning("Unauthorized request - no current user")
            return func.HttpResponse(
                json.dumps({"success": False, "statusCode": 401, "error": "Unauthorized"}),
                mimetype="application/json",
                status_code=401
            )

        logger.info(f"Current user: {current_user.get('email')}")

        # Get integration ID from route
        integration_id = req.route_params.get("integration_id")
        if not integration_id:
            logger.warning("Missing integration ID in route params")
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": "Integration ID is required"
                }, 400)),
                mimetype="application/json",
                status_code=400
            )

        logger.info(f"Integration ID: {integration_id}")

        # Get integration by ID
        integration = get_integration_by_id(integration_id)
        if not integration:
            logger.warning(f"Integration with ID {integration_id} not found")

            # For debugging, let's try to find all integrations
            if is_local_dev():
                try:
                    integration_repo = get_integration_table_repo()
                    if integration_repo:
                        all_integrations = integration_repo.query_entities("PartitionKey eq 'integration'")
                        logger.info(f"Found {len(all_integrations)} integrations in total")
                        for idx, integ in enumerate(all_integrations):
                            logger.info(f"Integration {idx+1}: ID={integ.get('RowKey')}, Name={integ.get('Name')}, TenantUrl={integ.get('TenantUrl')}")
                except Exception as e:
                    logger.error(f"Error listing all integrations: {str(e)}")

            # Return 404 response
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": f"Integration with ID {integration_id} not found"
                }, 404)),
                mimetype="application/json",
                status_code=404
            )

        logger.info(f"Found integration: ID={integration.get('id')}, Name={integration.get('name')}")

        # Check if integration is active
        if not integration.get("isActive", False):
            logger.warning(f"Integration {integration.get('id')} is not active")
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": "Integration is not active"
                }, 400)),
                mimetype="application/json",
                status_code=400
            )

        # Check if we should force refresh the data
        force_refresh = req.params.get("refresh", "false").lower() == "true"
        logger.info(f"Force refresh: {force_refresh}")

        # Try to get data from database first
        if not force_refresh:
            # Get integration ID
            integration_id = integration.get("id")
            logger.info(f"Looking for existing data for integration ID: {integration_id}")

            # Query the database for overview data
            if is_local_dev():
                logger.info("Using Azure Table Storage for local development")
                # For local development, try to get data from Azure Table Storage
                try:
                    # Initialize the Overview table repository
                    overview_repo = TableStorageRepository(table_name="Overview")
                    logger.info("Initialized Overview table repository")

                    # Query for the latest overview data for this organization
                    filter_query = f"PartitionKey eq '{integration_id}'"
                    logger.info(f"Querying Overview table with filter: {filter_query}")
                    entities = overview_repo.query_entities(filter_query)
                    logger.info(f"Found {len(entities) if entities else 0} overview entities")

                    if entities and len(entities) > 0:
                        # Sort entities by timestamp (RowKey) to get the latest
                        sorted_entities = sorted(entities, key=lambda x: x.get('RowKey', ''), reverse=True)
                        latest_entity = sorted_entities[0]
                        logger.info(f"Latest entity RowKey: {latest_entity.get('RowKey')}")

                        # Create the response data from the entity
                        overview_data = {
                            "dataStatus": "available",
                            "healthScore": latest_entity.get('HealthScore', 0),
                            "totalProfiles": latest_entity.get('TotalProfiles', 0),
                            "totalPermissionSets": latest_entity.get('TotalPermissionSets', 0),
                            "totalRisks": latest_entity.get('TotalRisks', 0),
                            "highRisks": latest_entity.get('HighRisks', 0),
                            "mediumRisks": latest_entity.get('MediumRisks', 0),
                            "lowRisks": latest_entity.get('LowRisks', 0),
                            "lastUpdated": latest_entity.get('LastUpdated', datetime.now().isoformat())
                        }
                        logger.info(f"Retrieved overview data from Azure Table Storage for organization {integration_id}")
                        logger.info(f"Overview data: {overview_data}")
                    else:
                        # No data found, return a response indicating data needs to be fetched
                        logger.info(f"No overview data found for integration {integration_id}")
                        return func.HttpResponse(
                            json.dumps(create_json_response({
                                "dataStatus": "empty",
                                "message": "No overview data available. Please sync to fetch data.",
                                "timestamp": datetime.now().isoformat(),
                                "tenantUrl": integration.get("tenantUrl", "")
                            })),
                            mimetype="application/json",
                            status_code=200
                        )
                except Exception as e:
                    logger.error(f"Error retrieving overview data from Azure Table Storage: {str(e)}")
                    # Return empty data response instead of using placeholder data
                    logger.info("Returning empty data response due to error")
                    return func.HttpResponse(
                        json.dumps(create_json_response({
                            "dataStatus": "empty",
                            "message": "No overview data available. Please sync to fetch data.",
                            "timestamp": datetime.now().isoformat(),
                            "tenantUrl": integration.get("tenantUrl", "")
                        })),
                        mimetype="application/json",
                        status_code=200
                    )
            else:
                # Use SQL Database for production
                logger.info("Using SQL Database for production")
                overview_repo = get_overview_repo()
                if not overview_repo:
                    logger.error("Overview repository not available")
                    return func.HttpResponse(
                        json.dumps(create_json_response({
                            "success": False,
                            "message": "Overview repository not available"
                        }, 500)),
                        mimetype="application/json",
                        status_code=500
                    )

                # Query for the latest overview data
                query = """
                SELECT o.HealthScore, o.TotalProfiles, o.TotalPermissionSets, o.TotalRisks,
                       o.HighRisks, o.MediumRisks, o.LowRisks, o.LastUpdated
                FROM App_Overview o
                INNER JOIN App_ExecutionLog e ON o.ExecutionLogId = e.Id
                WHERE o.OrgId = ? AND e.Status = 'Completed'
                ORDER BY e.StartTime DESC
                """
                logger.info(f"Executing SQL query: {query} with params: ({integration_id},)")
                results = overview_repo.execute_query(query, (integration_id,))
                logger.info(f"Query returned {len(results) if results else 0} results")

                if not results or len(results) == 0:
                    # No data found, return a response indicating data needs to be fetched
                    logger.info(f"No overview data found for integration {integration_id}")
                    return func.HttpResponse(
                        json.dumps(create_json_response({
                            "dataStatus": "empty",
                            "message": "No overview data available. Please sync to fetch data.",
                            "timestamp": datetime.now().isoformat(),
                            "tenantUrl": integration.get("tenantUrl", "")
                        })),
                        mimetype="application/json",
                        status_code=200
                    )

                # Extract data from the query results
                health_score, total_profiles, total_permission_sets, total_risks, high_risks, medium_risks, low_risks, last_updated = results[0]
                logger.info(f"Extracted data: HealthScore={health_score}, TotalProfiles={total_profiles}, etc.")

                # Create the response data
                overview_data = {
                    "dataStatus": "available",
                    "healthScore": health_score,
                    "totalProfiles": total_profiles,
                    "totalPermissionSets": total_permission_sets,
                    "totalRisks": total_risks,
                    "highRisks": high_risks,
                    "mediumRisks": medium_risks,
                    "lowRisks": low_risks,
                    "lastUpdated": last_updated.isoformat() if isinstance(last_updated, datetime) else last_updated
                }
                logger.info(f"Created overview data response")

            # Return the overview data
            logger.info("Returning overview data response")
            return func.HttpResponse(
                json.dumps(create_json_response(overview_data)),
                mimetype="application/json",
                status_code=200
            )

        # If force refresh or no data in database, trigger a background fetch
        logger.info("Triggering background fetch for overview data")

        # Create a background processor
        # processor = BackgroundProcessor()
        # logger.info("Created BackgroundProcessor instance")

        # Enqueue a task
        # logger.info(f"Enqueueing task: type={TASK_TYPE_OVERVIEW}, org_id={integration.get('id')}, user_id={current_user.get('id')}")
        # task_id = processor.enqueue_task(
        #     task_type=TASK_TYPE_OVERVIEW,
        #     org_id=integration.get("id"),
        #     user_id=current_user.get("id")
        # )

        # if not task_id:
        #     logger.error("Failed to enqueue task")
        #     return func.HttpResponse(
        #         json.dumps(create_json_response({
        #             "success": False,
        #             "message": "Failed to enqueue task"
        #         }, 500)),
        #         mimetype="application/json",
        #         status_code=500
        #     )

        # logger.info(f"Task enqueued successfully with ID: {task_id}")

        # Return pending status
        response_data = {
            # "dataStatus": "pending",
            # "message": "The results will be available shortly. Please refresh to check if data is available.",
            "dataStatus": "empty",
            "message": "Overview data is not available. Please perform a full rescan if needed.",
            "timestamp": datetime.now().isoformat(),
            "tenantUrl": integration.get("tenantUrl", ""),
            # "taskId": task_id
             "taskId": None # Or an empty string
        }
        logger.info(f"Returning pending status response: {response_data}")

        return func.HttpResponse(
            json.dumps(create_json_response(response_data)),
            mimetype="application/json",
            status_code=202  # Accepted
        )
    except Exception as e:
        logger.error(f"Error in get_integration_overview: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        error_response = handle_exception(e, "get_integration_overview")
        return func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="api/integration/{integration_id}/health-check", methods=["GET"])
@require_auth
def get_integration_health_check(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get integration health check

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info("Getting integration health check...")

    try:
        # Get current user from the JWT token (already validated by @require_auth)
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps({"success": False, "statusCode": 401, "error": "Unauthorized"}),
                mimetype="application/json",
                status_code=401
            )

        # Get integration ID from route
        integration_id = req.route_params.get("integration_id")
        if not integration_id:
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": "Integration ID is required"
                }, 400)),
                mimetype="application/json",
                status_code=400
            )

        # Get integration by ID
        integration = get_integration_by_id(integration_id)
        if not integration:
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": f"Integration with ID {integration_id} not found"
                }, 404)),
                mimetype="application/json",
                status_code=404
            )

        # Check if integration is active
        if not integration.get("isActive", False):
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": "Integration is not active"
                }, 400)),
                mimetype="application/json",
                status_code=400
            )

        # Check if we should force refresh the data
        force_refresh = req.params.get("refresh", "false").lower() == "true"

        # Try to get data from database first
        if not force_refresh:
            # Get integration ID
            integration_id = integration.get("id")

            # Query the database for health check data
            if is_local_dev():
                # For local development, try to get data from Azure Table Storage
                try:
                    logger.info(f"Fetching health check data for integration {integration_id} from Azure Table Storage")

                    # Initialize the HealthCheck table repository
                    health_check_repo = TableStorageRepository(table_name="HealthCheck")
                    logger.info("Initialized HealthCheck table repository")

                    # Get health score from the integration record
                    integration_repo = get_integration_table_repo()
                    if integration_repo:
                        integration_record = integration_repo.get_entity("integration", integration_id)
                        health_score = int(integration_record.get("HealthScore", 0)) if integration_record and "HealthScore" in integration_record else 0
                        logger.info(f"Retrieved health score from integration record: {health_score}")
                    else:
                        health_score = 0
                        logger.warning("Could not retrieve health score from integration record")

                    # Query for health check risks for this organization
                    filter_query = f"PartitionKey eq '{integration_id}'"
                    logger.info(f"Querying HealthCheck table with filter: {filter_query}")
                    entities = health_check_repo.query_entities(filter_query)
                    logger.info(f"Found {len(entities) if entities else 0} health check entities")

                    # If we only found one record, let's check if there are more records in other tables
                    if entities and len(entities) == 1:
                        logger.info("Only found one health check record, checking SecurityHealthCheckRisks table")

                        # Try to query the SecurityHealthCheckRisks table as well
                        try:
                            security_health_check_repo = TableStorageRepository(table_name="SecurityHealthCheckRisks")
                            security_entities = security_health_check_repo.query_entities(filter_query)
                            logger.info(f"Found {len(security_entities) if security_entities else 0} security health check entities")

                            if security_entities and len(security_entities) > 0:
                                # Add these entities to our list
                                entities.extend(security_entities)
                                logger.info(f"Combined total of {len(entities)} health check entities")
                        except Exception as sec_e:
                            logger.warning(f"Error querying SecurityHealthCheckRisks table: {str(sec_e)}")
                            import traceback
                            logger.warning(f"Traceback: {traceback.format_exc()}")

                    # If we don't have any records, return a response indicating data needs to be fetched
                    if not entities or len(entities) == 0:
                        logger.info(f"No health check records found for integration {integration_id}, triggering a refresh")

                    if not entities or len(entities) == 0:
                        # No data found, return a response indicating data needs to be fetched
                        logger.info(f"No health check data found for integration {integration_id}")
                        return func.HttpResponse(
                            json.dumps(create_json_response({
                                "dataStatus": "empty",
                                "message": "No health check data available. Please sync to fetch data.",
                                "timestamp": datetime.now().isoformat(),
                                "tenantUrl": integration.get("tenantUrl", "")
                            })),
                            mimetype="application/json",
                            status_code=200
                        )

                    # Extract risks from the entities
                    risks = []
                    last_updated = None

                    for entity in entities:
                        risk = {
                            "riskType": entity.get("RiskType", "UNKNOWN_RISK"),
                            "setting": entity.get("Setting", "Unknown Setting"),
                            "settingGroup": entity.get("SettingGroup", "Unknown Group"),
                            "orgValue": entity.get("OrgValue", "Unknown"),
                            "standardValue": entity.get("StandardValue", "Unknown")
                        }
                        risks.append(risk)

                        # Track the latest update time
                        entity_timestamp = entity.get("Timestamp")
                        if entity_timestamp and (last_updated is None or entity_timestamp > last_updated):
                            last_updated = entity_timestamp

                    # Create the response data
                    health_check_data = {
                        "dataStatus": "available",
                        "healthScore": health_score,
                        "risks": risks,
                        "lastUpdated": last_updated.isoformat() if isinstance(last_updated, datetime) else (datetime.now().isoformat() if last_updated is None else last_updated)
                    }
                    logger.info(f"Created health check data response with {len(risks)} risks")

                except Exception as e:
                    logger.error(f"Error retrieving health check data from Azure Table Storage: {str(e)}")
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")

                    # Return an error response indicating data needs to be refreshed
                    return func.HttpResponse(
                        json.dumps(create_json_response({
                            "dataStatus": "error",
                            "message": "Error retrieving health check data. Please try refreshing the data.",
                            "timestamp": datetime.now().isoformat(),
                            "tenantUrl": integration.get("tenantUrl", "")
                        })),
                        mimetype="application/json",
                        status_code=500
                    )

            else:
                # Use SQL Database for production
                health_check_repo = get_health_check_repo()
                if not health_check_repo:
                    logger.error("Health check repository not available")
                    return func.HttpResponse(
                        json.dumps(create_json_response({
                            "success": False,
                            "message": "Health check repository not available"
                        }, 500)),
                        mimetype="application/json",
                        status_code=500
                    )

                # Get health score from UserAccount
                org_query = """
                SELECT HealthScore
                FROM UserAccount
                WHERE Id = ?
                """
                org_results = health_check_repo.execute_query(org_query, (integration_id,))

                health_score = org_results[0][0] if org_results and org_results[0][0] else 0

                # Query for the latest health check data
                query = """
                SELECT h.RiskType, h.Setting, h.SettingGroup, h.OrgValue, h.StandardValue, h.LastUpdated
                FROM App_HealthCheck h
                INNER JOIN App_ExecutionLog e ON h.ExecutionLogId = e.Id
                WHERE h.OrgId = ? AND e.Status = 'Completed'
                ORDER BY e.StartTime DESC
                """
                results = health_check_repo.execute_query(query, (integration_id,))

                if not results or len(results) == 0:
                    # No data found, return a response indicating data needs to be fetched
                    return func.HttpResponse(
                        json.dumps(create_json_response({
                            "dataStatus": "empty",
                            "message": "No health check data available. Please sync to fetch data.",
                            "timestamp": datetime.now().isoformat(),
                            "tenantUrl": integration.get("tenantUrl", "")
                        })),
                        mimetype="application/json",
                        status_code=200
                    )

                # Extract data from the query results
                risks = []
                last_updated = None

                for row in results:
                    risk_type, setting, setting_group, org_value, standard_value, updated_at = row

                    risks.append({
                        "riskType": risk_type,
                        "setting": setting,
                        "settingGroup": setting_group,
                        "orgValue": org_value,
                        "standardValue": standard_value
                    })

                    # Use the most recent last updated date
                    if last_updated is None or (updated_at and updated_at > last_updated):
                        last_updated = updated_at

                # Create the response data
                health_check_data = {
                    "dataStatus": "available",
                    "healthScore": health_score,
                    "risks": risks,
                    "lastUpdated": last_updated.isoformat() if isinstance(last_updated, datetime) else (last_updated or datetime.now().isoformat())
                }

            return func.HttpResponse(
                json.dumps(create_json_response(health_check_data)),
                mimetype="application/json",
                status_code=200
            )

        # If force refresh or no data in database, trigger a background fetch
        logger.info(f"Force refresh requested for health check data. Integration ID: {integration.get('id')}")

        try:
            # Create a background processor
            processor = BackgroundProcessor()

            # Get the integration ID
            integration_id = integration.get("id")
            if not integration_id:
                logger.error("Integration ID is missing")
                return func.HttpResponse(
                    json.dumps(create_json_response({
                        "success": False,
                        "message": "Integration ID is missing"
                    }, 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Queue a task to fetch health check data
            # Check if the processor has a queue_task method
            if hasattr(processor, 'queue_task'):
                task_id = processor.queue_task(
                    task_type=TASK_TYPE_HEALTH_CHECK,
                    org_id=integration_id,
                    user_id=current_user.get("id", "system"),
                    priority=TASK_PRIORITY_HIGH,
                    params={}
                )
            else:
                # Fall back to enqueue_task if queue_task is not available
                task_id = processor.enqueue_task(
                    task_type=TASK_TYPE_HEALTH_CHECK,
                    org_id=integration_id,
                    user_id=current_user.get("id", "system")
                )

            logger.info(f"Queued health check task with ID: {task_id}")

            if not task_id:
                logger.error("Failed to queue health check task")
                return func.HttpResponse(
                    json.dumps(create_json_response({
                        "success": False,
                        "message": "Failed to queue task"
                    }, 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Return pending status
            response_data = {
                "dataStatus": "pending",
                "message": "Health check data is being fetched in the background. Please check back in a few moments.",
                "timestamp": datetime.now().isoformat(),
                "tenantUrl": integration.get("tenantUrl", ""),
                "taskId": task_id
            }

            logger.info(f"Returning pending status response: {response_data}")

            return func.HttpResponse(
                json.dumps(create_json_response(response_data)),
                mimetype="application/json",
                status_code=202  # Accepted
            )
        except Exception as e:
            logger.error(f"Error queueing health check task: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": f"Error queueing health check task: {str(e)}"
                }, 500)),
                mimetype="application/json",
                status_code=500
            )
    except Exception as e:
        error_response = handle_exception(e, "get_integration_health_check")
        return func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="api/integration/{integration_id}/profiles", methods=["GET"])
@require_auth
def get_integration_profiles(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get integration profiles and permission sets

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info("Getting integration profiles and permission sets...")

    try:
        # Get current user
        current_user = get_current_user(req)
        if not current_user:
            return func.HttpResponse(
                json.dumps({"success": False, "statusCode": 401, "error": "Unauthorized"}),
                mimetype="application/json",
                status_code=401
            )

        # Get integration ID from route
        integration_id = req.route_params.get("integration_id")
        if not integration_id:
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": "Integration ID is required"
                }, 400)),
                mimetype="application/json",
                status_code=400
            )

        # Get integration by ID
        integration = get_integration_by_id(integration_id)
        if not integration:
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": f"Integration with ID {integration_id} not found"
                }, 404)),
                mimetype="application/json",
                status_code=404
            )

        # Check if integration is active
        if not integration.get("isActive", False):
            return func.HttpResponse(
                json.dumps(create_json_response({
                    "success": False,
                    "message": "Integration is not active"
                }, 400)),
                mimetype="application/json",
                status_code=400
            )

        # Check if we should force refresh the data
        force_refresh = req.params.get("refresh", "false").lower() == "true"

        # Try to get data from database first
        if not force_refresh:
            # Get integration ID
            integration_id = integration.get("id")

            # Query the database for profiles data using the new system permissions module
            logger.info("Fetching system permissions data for profiles and permission sets")

            # Get profile and permission set system permissions
            profiles = get_profile_system_permissions(integration_id)
            permission_sets = get_permission_set_system_permissions(integration_id)

            logger.info(f"Found {len(profiles)} profiles and {len(permission_sets)} permission sets")

            if not profiles and not permission_sets:
                # No data found, return a response indicating data needs to be fetched
                return func.HttpResponse(
                    json.dumps(create_json_response({
                        "dataStatus": "empty",
                        "message": "No profiles data available. Please sync to fetch data.",
                        "timestamp": datetime.now().isoformat(),
                        "tenantUrl": integration.get("tenantUrl", "")
                    })),
                    mimetype="application/json",
                    status_code=200
                )

            # Process profiles data
            processed_profiles = []
            for profile in profiles:
                # Determine security risk level based on permissions
                risk_level = "LOW_RISK"
                if profile.get("modifyAllData") or profile.get("manageUsers") or profile.get("manageProfilesPermissionsets") or profile.get("manageRoles"):
                    risk_level = "HIGH_RISK"
                elif profile.get("viewAllData") or profile.get("resetPasswords") or profile.get("dataExport") or profile.get("manageSharing"):
                    risk_level = "MEDIUM_RISK"

                # Create profile object
                processed_profile = {
                    "name": profile.get("name", ""),
                    "description": f"{profile.get('name', '')} profile",
                    "securityRisk": risk_level,
                    "systemPermissions": {
                        "modifyAllData": profile.get("modifyAllData", False),
                        "viewAllData": profile.get("viewAllData", False),
                        "resetPasswords": profile.get("resetPasswords", False),
                        "dataExport": profile.get("dataExport", False),
                        "manageUsers": profile.get("manageUsers", False),
                        "manageProfilesPermissionsets": profile.get("manageProfilesPermissionsets", False),
                        "manageRoles": profile.get("manageRoles", False),
                        "manageSharing": profile.get("manageSharing", False),
                        "editReadonlyFields": profile.get("editReadonlyFields", False),
                        "viewSetup": profile.get("viewSetup", False),
                        "authorApex": profile.get("authorApex", False),
                        "viewAllUsers": profile.get("viewAllUsers", False),
                        "manageEncryptionKeys": profile.get("manageEncryptionKeys", False),
                        "manageTwoFactor": profile.get("manageTwoFactor", False),
                        "modifyMetadata": profile.get("modifyMetadata", False),
                        "customizeApplication": profile.get("customizeApplication", False),
                        "manageIpAddresses": profile.get("manageIpAddresses", False)
                    }
                }
                processed_profiles.append(processed_profile)

            # Process permission sets data
            processed_permission_sets = []
            for perm_set in permission_sets:
                # Determine security risk level based on permissions
                risk_level = "LOW_RISK"
                if perm_set.get("modifyAllData") or perm_set.get("manageUsers") or perm_set.get("manageProfilesPermissionsets") or perm_set.get("manageRoles"):
                    risk_level = "HIGH_RISK"
                elif perm_set.get("viewAllData") or perm_set.get("resetPasswords") or perm_set.get("dataExport") or perm_set.get("manageSharing"):
                    risk_level = "MEDIUM_RISK"

                # Create permission set object
                processed_perm_set = {
                    "name": perm_set.get("name", ""),
                    "description": f"{perm_set.get('name', '')} permission set",
                    "securityRisk": risk_level,
                    "systemPermissions": {
                        "modifyAllData": perm_set.get("modifyAllData", False),
                        "viewAllData": perm_set.get("viewAllData", False),
                        "resetPasswords": perm_set.get("resetPasswords", False),
                        "dataExport": perm_set.get("dataExport", False),
                        "manageUsers": perm_set.get("manageUsers", False),
                        "manageProfilesPermissionsets": perm_set.get("manageProfilesPermissionsets", False),
                        "manageRoles": perm_set.get("manageRoles", False),
                        "manageSharing": perm_set.get("manageSharing", False),
                        "editReadonlyFields": perm_set.get("editReadonlyFields", False),
                        "viewSetup": perm_set.get("viewSetup", False),
                        "authorApex": perm_set.get("authorApex", False),
                        "viewAllUsers": perm_set.get("viewAllUsers", False),
                        "manageEncryptionKeys": perm_set.get("manageEncryptionKeys", False),
                        "manageTwoFactor": perm_set.get("manageTwoFactor", False),
                        "modifyMetadata": perm_set.get("modifyMetadata", False),
                        "customizeApplication": perm_set.get("customizeApplication", False),
                        "manageIpAddresses": perm_set.get("manageIpAddresses", False)
                    }
                }
                processed_permission_sets.append(processed_perm_set)

            # Create the response data
            profiles_data = {
                "dataStatus": "available",
                "profiles": processed_profiles,
                "permissionSets": processed_permission_sets,
                "lastUpdated": datetime.now().isoformat()
            }

            logger.info(f"Returning profiles data with {len(processed_profiles)} profiles and {len(processed_permission_sets)} permission sets")

            # Return the response
            return func.HttpResponse(
                json.dumps(create_json_response(profiles_data)),
                mimetype="application/json",
                status_code=200
            )
        else:
            # If force refresh or no data in database, trigger a background fetch
            # Create a background processor
            # processor = BackgroundProcessor()

            # Enqueue a task
            # task_id = processor.enqueue_task(
            #     task_type=TASK_TYPE_PROFILES,
            #     org_id=integration.get("id"),
            #     user_id=current_user.get("id")
            # )

            # if not task_id:
            #     return func.HttpResponse(
            #         json.dumps(create_json_response({
            #             "success": False,
            #             "message": "Failed to enqueue task"
            #         }, 500)),
            #         mimetype="application/json",
            #         status_code=500
            #     )

            # Return pending status
            # response_data = {
            #     "dataStatus": "pending",
            #     "message": "The results will be available shortly. Please refresh to check if data is available.",
            #     "timestamp": datetime.now().isoformat(),
            #     "tenantUrl": integration.get("tenantUrl", ""),
            #     "taskId": task_id
            # }
            response_data = {
                "dataStatus": "empty",
                "message": "Development in progress.", # User requested message
                "timestamp": datetime.now().isoformat(),
                "tenantUrl": integration.get("tenantUrl", ""),
                "taskId": None # Or an empty string
            }

        return func.HttpResponse(
            json.dumps(create_json_response(response_data)),
            mimetype="application/json",
            status_code=202  # Accepted
        )
    except Exception as e:
        error_response = handle_exception(e, "get_integration_profiles")
        return func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

# NOTE: The task-status endpoint has been moved to task_management.py
# to avoid route conflicts and provide better functionality.
# The get_task_status_for_integration function has been removed.
# Use the task_status_api function in task_management.py instead.
