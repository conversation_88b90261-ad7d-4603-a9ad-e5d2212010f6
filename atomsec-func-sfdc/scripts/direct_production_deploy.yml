# Alternative: Direct Production Deployment (Bypass Staging Slot Issues)
# Use this if staging slot configuration continues to have app settings issues

- stage: DirectProductionDeploy
  displayName: 'Direct Production Deployment (Staging Bypass)'
  condition: eq(variables['USE_DIRECT_PRODUCTION'], 'true')
  dependsOn: Test
  jobs:
  - job: DirectDeployJob
    displayName: 'Deploy Directly to Production'
    steps:
    - task: DownloadBuildArtifacts@1
      inputs:
        buildType: 'current'
        downloadType: 'single'
        artifactName: 'sfdc-drop'
        downloadPath: '$(System.ArtifactsDirectory)'
      displayName: 'Download Build Artifacts'

    - task: AzureFunctionApp@2
      displayName: 'Deploy Azure Function App Directly'
      inputs:
        connectedServiceNameARM: '$(sfdcServiceConnection)'
        appType: 'functionAppLinux'
        appName: '$(functionAppName)'
        package: '$(System.ArtifactsDirectory)/sfdc-drop/sfdc-build-$(Build.BuildNumber).zip'
        runtimeStack: 'PYTHON|3.11'
        deploymentMethod: 'zipDeploy'
        # Deploy directly to production (no staging)
        deployToSlotOrASE: false
        
    - task: AzureCLI@2
      displayName: 'Configure Production App Settings'
      inputs:
        azureSubscription: '$(sfdcServiceConnection)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo "=== CONFIGURING PRODUCTION APP SETTINGS ==="
          
          RG="$(resourceGroupName)"
          APP="$(functionAppName)"
          
          echo "Setting core Azure Functions settings..."
          az webapp config appsettings set -g "$RG" -n "$APP" --settings \
            "FUNCTIONS_WORKER_RUNTIME=python" \
            "FUNCTIONS_EXTENSION_VERSION=~4" \
            "WEBSITE_RUN_FROM_PACKAGE=1" \
            "AzureWebJobsFeatureFlags=EnableWorkerIndexing" \
            "PYTHON_ISOLATE_WORKER_DEPENDENCIES=0" \
            || { echo "❌ Failed to set core settings"; exit 1; }
          
          echo "Setting storage configuration..."
          az webapp config appsettings set -g "$RG" -n "$APP" --settings \
            "AzureWebJobsStorage__accountName=statomsecdbconnectdev02" \
            "AzureWebJobsStorage__blobServiceUri=https://statomsecdbconnectdev02.blob.core.windows.net" \
            "AzureWebJobsStorage__queueServiceUri=https://statomsecdbconnectdev02.queue.core.windows.net" \
            "AzureWebJobsStorage__tableServiceUri=https://statomsecdbconnectdev02.table.core.windows.net" \
            "AzureWebJobsStorage__credential=managedidentity" \
            || { echo "❌ Failed to set storage settings"; exit 1; }
          
          echo "Setting application settings..."
          az webapp config appsettings set -g "$RG" -n "$APP" --settings \
            "KEY_VAULT_URL=https://akv-atomsec-dev.vault.azure.net/" \
            "DB_SERVICE_URL=https://func-atomsec-dbconnect-dev02.azurewebsites.net/api/db" \
            "DB_SERVICE_TIMEOUT=30" \
            "DB_SERVICE_RETRY_ATTEMPTS=3" \
            "DB_SERVICE_RETRY_DELAY=1" \
            "FRONTEND_URL=https://app-atomsec-dev01.azurewebsites.net" \
            "PMD_ENABLED=true" \
            || { echo "❌ Failed to set application settings"; exit 1; }
          
          echo "✅ All settings applied to production"
          
          echo "Restarting function app to apply settings..."
          az webapp restart -g "$RG" -n "$APP" || { echo "Failed to restart app"; exit 1; }
          
          echo "Waiting for production warmup..."
          sleep 120
          
          echo "=== PRODUCTION FUNCTIONAL VALIDATION ==="
          PROD_URL="https://$APP.azurewebsites.net"
          echo "Production URL: $PROD_URL"
          
          # Test endpoints
          endpoints_to_test=(
            "/api/test:Test endpoint"
            "/api/health:Health check" 
            "/api/info:Service info"
            "/api/status:Status check"
          )
          
          successful_endpoints=0
          
          for endpoint_def in "${endpoints_to_test[@]}"; do
            IFS=':' read -r endpoint_path endpoint_name <<< "$endpoint_def"
            test_url="$PROD_URL$endpoint_path"
            
            echo "Testing $endpoint_name ($endpoint_path)..."
            test_status=$(curl -s -o /dev/null -w "%{http_code}" "$test_url" --max-time 20 || echo "000")
            
            if [ "$test_status" = "200" ]; then
              echo "✅ $endpoint_name: SUCCESS (200)"
              successful_endpoints=$((successful_endpoints + 1))
            else
              echo "❌ $endpoint_name: Status $test_status"
            fi
          done
          
          if [ $successful_endpoints -ge 1 ]; then
            echo "✅ PRODUCTION DEPLOYMENT SUCCESSFUL!"
          else
            echo "❌ PRODUCTION DEPLOYMENT FAILED"
            exit 1
          fi
