#!/bin/bash

# Test Staging Endpoints Script
# This script replicates the functional validation approach used in the pipeline
# Use this to test your staging slot manually without reading app configuration

set -e

# Configuration
STAGING_URL="${1:-https://func-atomsec-sfdc-dev02-stage.azurewebsites.net}"
TIMEOUT=15
WARMUP_RETRY_DELAY=20

echo "=== FUNCTIONAL ENDPOINT TESTING ==="
echo "Testing staging URL: $STAGING_URL"
echo "Timeout per request: ${TIMEOUT}s"
echo

# Define endpoints to test
endpoints_to_test=(
    "/api/test:Test endpoint"
    "/api/health:Health check" 
    "/api/info:Service info"
    "/api/status:Status check"
    "/api/debug/version:Debug info"
)

successful_endpoints=0
total_endpoints=${#endpoints_to_test[@]}

# Test each endpoint
for endpoint_def in "${endpoints_to_test[@]}"; do
    IFS=':' read -r endpoint_path endpoint_name <<< "$endpoint_def"
    test_url="$STAGING_URL$endpoint_path"
    
    echo "Testing $endpoint_name ($endpoint_path)..."
    
    # Test endpoint with timeout
    test_status=$(curl -s -o /dev/null -w "%{http_code}" "$test_url" --max-time $TIMEOUT || echo "000")
    
    if [ "$test_status" = "200" ]; then
        echo "✅ $endpoint_name: SUCCESS (200)"
        successful_endpoints=$((successful_endpoints + 1))
        
        # Get a preview of the response for verification
        echo "   Response preview:"
        curl -s "$test_url" --max-time $TIMEOUT | head -100 | sed 's/^/   /' || echo "   Unable to fetch response preview"
        echo
        
    else
        echo "❌ $endpoint_name: Status $test_status"
        
        # For 404s, try warmup retry
        if [ "$test_status" = "404" ]; then
            echo "   → Attempting warmup retry for $endpoint_name..."
            sleep $WARMUP_RETRY_DELAY
            retry_status=$(curl -s -o /dev/null -w "%{http_code}" "$test_url" --max-time $TIMEOUT || echo "000")
            if [ "$retry_status" = "200" ]; then
                echo "✅ $endpoint_name: SUCCESS after warmup (200)"
                successful_endpoints=$((successful_endpoints + 1))
            else
                echo "❌ $endpoint_name: Still failing after warmup ($retry_status)"
            fi
        fi
        echo
    fi
done

echo "=== VALIDATION RESULTS ==="
echo "Successful endpoints: $successful_endpoints / $total_endpoints"

if [ $successful_endpoints -ge 1 ]; then
    echo "✅ FUNCTION APP IS WORKING!"
    echo "At least one endpoint is responding correctly"
    echo "Deployment validation: PASSED"
    exit 0
else
    echo "❌ FUNCTION APP ISSUES DETECTED"
    echo "No endpoints are responding correctly"
    echo "Deployment validation: FAILED"
    
    echo
    echo "=== DIAGNOSTIC INFORMATION ==="
    echo "Host admin endpoint test:"
    admin_status=$(curl -s -o /dev/null -w "%{http_code}" "$STAGING_URL/admin/host/status" --max-time 10 || echo "000")
    echo "Admin endpoint status: $admin_status"
    
    if [ "$admin_status" = "200" ]; then
        echo "Host is running but functions may not be registered correctly"
    else
        echo "Host is not responding - likely startup or configuration issue"
    fi
    
    exit 1
fi
