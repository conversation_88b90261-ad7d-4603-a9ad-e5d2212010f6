#!/bin/bash

# Manual Staging Slot Configuration Script
# This script applies all critical settings manually via Azure CLI
# Aligned with Azure DevOps pipeline deployment process

set -e

echo "=== MANUAL STAGING SLOT CONFIGURATION ==="
echo "Based on Azure DevOps pipeline deployment configuration"
echo

# Configuration
RG="atomsec-dev-backend"
APP="func-atomsec-sfdc-dev02"
SLOT="stage"

echo "Resource Group: $RG"
echo "Function App: $APP"
echo "Slot: $SLOT"
echo

# Step 1: Reset staging slot configuration (matching pipeline behavior)
echo "Step 1: Resetting staging slot configuration..."
echo "This matches the pipeline behavior of clearing slot settings before deployment"
az webapp config appsettings delete \
  --name "$APP" \
  --resource-group "$RG" \
  --slot "$SLOT" \
  --setting-names \
    "APPLICATIONINSIGHTS_AUTHENTICATION_STRING" \
    "APPLICATIONINSIGHTS_CONNECTION_STRING" \
    "AzureWebJobsStorage__credential" \
    "AzureWebJobsStorage__blobServiceUri" \
    "AzureWebJobsStorage__queueServiceUri" \
    "AzureWebJobsStorage__tableServiceUri" \
    "AzureWebJobsFeatureFlags" \
    "SCM_DO_BUILD_DURING_DEPLOYMENT" \
    "KEY_VAULT_URL" \
    "DB_SERVICE_URL" \
    "DB_SERVICE_TIMEOUT" \
    "DB_SERVICE_RETRY_ATTEMPTS" \
    "DB_SERVICE_RETRY_DELAY" \
    "PMD_ENABLED" \
    "FRONTEND_URL" \
    "ENABLE_ORYX_BUILD" \
    "BUILD_FLAGS" \
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE" \
    "WEBSITE_ENABLE_SYNC_UPDATE_SITE" \
    "WEBSITE_WEBSOCKETS_ENABLED" \
    "WEBSITE_ENABLE_APP_SERVICE_STORAGE" \
  --output none 2>/dev/null || echo "Note: Some settings may not exist to delete"

echo "✅ Staging slot configuration reset"
echo

# Step 2: Apply critical Azure Functions settings
echo "Step 2: Applying critical Azure Functions settings..."
az webapp config appsettings set \
  --name "$APP" \
  --resource-group "$RG" \
  --slot "$SLOT" \
  --settings \
    "FUNCTIONS_WORKER_RUNTIME=python" \
    "FUNCTIONS_EXTENSION_VERSION=~4" \
    "WEBSITE_RUN_FROM_PACKAGE=1" \
    "AzureWebJobsFeatureFlags=EnableWorkerIndexing" \
    "PYTHON_ISOLATE_WORKER_DEPENDENCIES=0"

echo "✅ Core settings applied"
echo

# Step 3: Apply storage configuration (matching pipeline deployment)
echo "Step 3: Applying storage configuration..."
az webapp config appsettings set \
  --name "$APP" \
  --resource-group "$RG" \
  --slot "$SLOT" \
  --settings \
    "AzureWebJobsStorage__accountName=statomsecdbconnectdev02" \
    "AzureWebJobsStorage__blobServiceUri=https://statomsecdbconnectdev02.blob.core.windows.net" \
    "AzureWebJobsStorage__queueServiceUri=https://statomsecdbconnectdev02.queue.core.windows.net" \
    "AzureWebJobsStorage__tableServiceUri=https://statomsecdbconnectdev02.table.core.windows.net" \
    "AzureWebJobsStorage__credential=managedidentity"

echo "✅ Storage settings applied"
echo

# Step 4: Apply application-specific settings
echo "Step 4: Applying application-specific settings..."
az webapp config appsettings set \
  --name "$APP" \
  --resource-group "$RG" \
  --slot "$SLOT" \
  --settings \
    "KEY_VAULT_URL=https://akv-atomsec-dev.vault.azure.net/" \
    "DB_SERVICE_URL=https://func-atomsec-dbconnect-dev02.azurewebsites.net/api/db" \
    "DB_SERVICE_TIMEOUT=30" \
    "DB_SERVICE_RETRY_ATTEMPTS=3" \
    "DB_SERVICE_RETRY_DELAY=1" \
    "FRONTEND_URL=https://app-atomsec-dev01.azurewebsites.net" \
    "PMD_ENABLED=true"

echo "✅ Application settings applied"
echo

# Step 5: Apply deployment-specific settings (from pipeline logs)
echo "Step 5: Applying deployment-specific settings..."
az webapp config appsettings set \
  --name "$APP" \
  --resource-group "$RG" \
  --slot "$SLOT" \
  --settings \
    "SCM_DO_BUILD_DURING_DEPLOYMENT=false" \
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE=false" \
    "WEBSITE_ENABLE_SYNC_UPDATE_SITE=false" \
    "WEBSITE_WEBSOCKETS_ENABLED=false" \
    "WEBSITE_ENABLE_APP_SERVICE_STORAGE=false"

echo "✅ Deployment settings applied"
echo

# Step 6: Verify configuration
echo "Step 6: Verifying configuration..."
echo "Current app settings for staging slot:"
az webapp config appsettings list \
  --name "$APP" \
  --resource-group "$RG" \
  --slot "$SLOT" \
  --query "[?slotSetting==false].{name:name, value:value}" \
  --output table

echo
echo "✅ Configuration verification complete"
echo

# Step 7: Restart staging slot
echo "Step 7: Restarting staging slot to apply settings..."
az webapp restart \
  --name "$APP" \
  --resource-group "$RG" \
  --slot "$SLOT"

echo "✅ Staging slot restarted"
echo

# Step 8: Get the correct staging URL
echo "Step 8: Getting staging slot URL..."
STAGING_HOST=$(az webapp show \
  --name "$APP" \
  --resource-group "$RG" \
  --slot "$SLOT" \
  --query "defaultHostName" \
  --output tsv)

STAGING_URL="https://$STAGING_HOST"
echo "Staging URL: $STAGING_URL"
echo

# Step 9: Wait for warmup (increased for Python cold start)
echo "Step 9: Waiting for warmup (120 seconds)..."
echo "Note: Python Azure Functions need extended time for cold start..."
echo "Progress: ["
for i in {1..12}; do
    sleep 10
    echo -n "="
done
echo "] Done"
echo

# Step 10: Test endpoints with improved error handling
echo "Step 10: Testing endpoints..."
endpoints_to_test=(
    "/api/test:Test endpoint"
    "/api/health:Health check" 
    "/api/info:Service info"
    "/api/status:Status check"
)

successful_endpoints=0
total_endpoints=${#endpoints_to_test[@]}

for endpoint_def in "${endpoints_to_test[@]}"; do
    IFS=':' read -r endpoint_path endpoint_name <<< "$endpoint_def"
    test_url="$STAGING_URL$endpoint_path"
    
    echo "Testing $endpoint_name ($endpoint_path)..."
    
    # First attempt
    test_status=$(curl -s -o /dev/null -w "%{http_code}" "$test_url" --max-time 30 --connect-timeout 10 || echo "000")
    
    if [ "$test_status" = "200" ]; then
        echo "✅ $endpoint_name: SUCCESS (200)"
        successful_endpoints=$((successful_endpoints + 1))
        
        # Show response preview for successful endpoints
        echo "   Response preview:"
        curl -s "$test_url" --max-time 15 | head -100 | sed 's/^/   /' || echo "   Unable to fetch preview"
        echo
    else
        echo "❌ $endpoint_name: Status $test_status"
        
        # Try warmup retry for 404s and 503s (common cold start responses)
        if [[ "$test_status" =~ ^(404|503|000)$ ]]; then
            echo "   → Attempting warmup retry (30 second wait)..."
            sleep 30
            retry_status=$(curl -s -o /dev/null -w "%{http_code}" "$test_url" --max-time 30 --connect-timeout 10 || echo "000")
            if [ "$retry_status" = "200" ]; then
                echo "✅ $endpoint_name: SUCCESS after warmup (200)"
                successful_endpoints=$((successful_endpoints + 1))
            else
                echo "❌ $endpoint_name: Still failing after warmup ($retry_status)"
            fi
        fi
        echo
    fi
done

# Step 11: Results summary with enhanced reporting
echo "=== CONFIGURATION RESULTS ==="
echo "Successful endpoints: $successful_endpoints / $total_endpoints"

if [ $successful_endpoints -eq $total_endpoints ]; then
    echo "🎉 EXCELLENT: All endpoints are responding correctly!"
    echo "✅ Manual configuration was completely successful"
elif [ $successful_endpoints -ge 1 ]; then
    echo "✅ GOOD: Function app is partially responding"
    echo "⚠️  Some endpoints may need additional configuration"
else
    echo "❌ CONFIGURATION FAILED"
    echo "No endpoints are responding correctly"
fi

echo
echo "Your endpoints are available at:"
for endpoint_def in "${endpoints_to_test[@]}"; do
    IFS=':' read -r endpoint_path endpoint_name <<< "$endpoint_def"
    echo "  $STAGING_URL$endpoint_path"
done

echo
if [ $successful_endpoints -lt $total_endpoints ]; then
    echo "Additional diagnostic commands:"
    echo "  # View real-time logs:"
    echo "  az webapp log tail --name $APP --resource-group $RG --slot $SLOT"
    echo "  # Check function host status:"
    echo "  curl -v $STAGING_URL/admin/host/status"
    echo "  # Check slot configuration:"
    echo "  az webapp config appsettings list --name $APP --resource-group $RG --slot $SLOT"
    echo "  # Check slot status:"
    echo "  az webapp show --name $APP --resource-group $RG --slot $SLOT --query '{state:state,status:status}'
fi

echo
echo "=== MANUAL CONFIGURATION COMPLETE ==="
echo "Script execution time: $(date)"
