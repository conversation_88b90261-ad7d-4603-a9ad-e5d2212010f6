"""
PMD Repository

This module provides database operations for PMD-related data.
"""

import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from .base_repository import BaseRepository

logger = logging.getLogger(__name__)

class PMDRepository(BaseRepository):
    """Repository for PMD-related database operations"""
    
    def __init__(self):
        """Initialize PMD repository"""
        super().__init__()
    
    def get_enabled_subtasks_for_integration(self, integration_id: str) -> List[Dict[str, Any]]:
        """
        Get enabled PMD subtasks for a specific integration
        
        Args:
            integration_id: Integration ID
            
        Returns:
            List of enabled PMD subtasks
        """
        try:
            # Find PMD policy for this integration
            policy_query = """
                SELECT PolicyId FROM Policies 
                WHERE IntegrationId = ? AND Name = 'Static Code Analysis (PMD)'
            """
            policies = self.execute_query(policy_query, (integration_id,))
            
            if not policies:
                logger.warning(f"No PMD policy found for integration {integration_id}")
                return []
            
            policy_id = policies[0][0]
            
            # Find PMD rule for this policy
            rule_query = """
                SELECT RuleId FROM Rules 
                WHERE PolicyId = ? AND TaskType = 'pmd_apex_security' AND Enabled = 1
            """
            rules = self.execute_query(rule_query, (policy_id,))
            
            if not rules:
                logger.warning(f"No enabled PMD rule found for policy {policy_id}")
                return []
            
            rule_id = rules[0][0]
            
            # Get enabled subtasks for this rule
            subtask_query = """
                SELECT SubtaskId, SubtaskName, SubtaskDescription, Enabled, CreatedAt
                FROM PMDSubtasks 
                WHERE RuleId = ? AND Enabled = 1
            """
            subtasks = self.execute_query(subtask_query, (rule_id,))
            
            # Convert to list of dictionaries
            result = []
            for subtask in subtasks:
                result.append({
                    "SubtaskId": subtask[0],
                    "SubtaskName": subtask[1],
                    "SubtaskDescription": subtask[2],
                    "Enabled": bool(subtask[3]),
                    "CreatedAt": subtask[4]
                })
            
            logger.info(f"Found {len(result)} enabled PMD subtasks for integration {integration_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error getting enabled PMD subtasks for integration {integration_id}: {e}")
            return []
    
    def get_enabled_rules_for_subtask(self, subtask_id: str) -> List[Dict[str, Any]]:
        """
        Get enabled individual PMD rules for a specific subtask
        
        Args:
            subtask_id: Subtask ID
            
        Returns:
            List of enabled individual PMD rules
        """
        try:
            query = """
                SELECT IndividualRuleId, RuleName, RuleDescription, Enabled, CreatedAt
                FROM PMDIndividualRules 
                WHERE SubtaskId = ? AND Enabled = 1
            """
            rules = self.execute_query(query, (subtask_id,))
            
            # Convert to list of dictionaries
            result = []
            for rule in rules:
                result.append({
                    "IndividualRuleId": rule[0],
                    "RuleName": rule[1],
                    "RuleDescription": rule[2],
                    "Enabled": bool(rule[3]),
                    "CreatedAt": rule[4]
                })
            
            logger.info(f"Found {len(result)} enabled individual PMD rules for subtask {subtask_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error getting enabled individual PMD rules for subtask {subtask_id}: {e}")
            return []
    
    def get_complete_configuration_for_integration(self, integration_id: str) -> Dict[str, Any]:
        """
        Get complete PMD configuration for a specific integration
        
        Args:
            integration_id: Integration ID
            
        Returns:
            Complete PMD configuration with subtasks and individual rules
        """
        try:
            # Find PMD policy for this integration
            policy_query = """
                SELECT PolicyId FROM Policies 
                WHERE IntegrationId = ? AND Name = 'Static Code Analysis (PMD)'
            """
            policies = self.execute_query(policy_query, (integration_id,))
            
            if not policies:
                logger.warning(f"No PMD policy found for integration {integration_id}")
                return {}
            
            policy_id = policies[0][0]
            
            # Find PMD rule for this policy
            rule_query = """
                SELECT RuleId, Enabled FROM Rules 
                WHERE PolicyId = ? AND TaskType = 'pmd_apex_security'
            """
            rules = self.execute_query(rule_query, (policy_id,))
            
            if not rules:
                logger.warning(f"No PMD rule found for policy {policy_id}")
                return {}
            
            rule_id = rules[0][0]
            rule_enabled = bool(rules[0][1])
            
            # Get all subtasks for this rule
            subtask_query = """
                SELECT SubtaskId, SubtaskName, SubtaskDescription, Enabled, CreatedAt
                FROM PMDSubtasks 
                WHERE RuleId = ?
            """
            subtasks = self.execute_query(subtask_query, (rule_id,))
            
            # Build complete configuration
            configuration = {
                "integration_id": integration_id,
                "policy_id": policy_id,
                "rule_id": rule_id,
                "pmd_rule_enabled": rule_enabled,
                "subtasks": []
            }
            
            for subtask in subtasks:
                subtask_id = subtask[0]
                subtask_config = {
                    "subtask_id": subtask_id,
                    "name": subtask[1],
                    "description": subtask[2],
                    "enabled": bool(subtask[3]),
                    "created_at": subtask[4],
                    "individual_rules": []
                }
                
                # Get individual rules for this subtask
                individual_rules = self.get_enabled_rules_for_subtask(subtask_id)
                subtask_config["individual_rules"] = individual_rules
                
                configuration["subtasks"].append(subtask_config)
            
            # Add summary statistics
            total_subtasks = len(configuration["subtasks"])
            enabled_subtasks = len([s for s in configuration["subtasks"] if s["enabled"]])
            total_individual_rules = sum(len(s["individual_rules"]) for s in configuration["subtasks"])
            enabled_individual_rules = sum(len([r for r in s["individual_rules"] if r["Enabled"]]) for s in configuration["subtasks"])
            
            configuration.update({
                "summary": {
                    "total_subtasks": total_subtasks,
                    "enabled_subtasks": enabled_subtasks,
                    "total_individual_rules": total_individual_rules,
                    "enabled_individual_rules": enabled_individual_rules
                }
            })
            
            logger.info(f"Retrieved complete PMD configuration for integration {integration_id}: {enabled_subtasks}/{total_subtasks} subtasks, {enabled_individual_rules}/{total_individual_rules} individual rules")
            return configuration
            
        except Exception as e:
            logger.error(f"Error getting complete PMD configuration for integration {integration_id}: {e}")
            return {}
    
    def store_findings(self, findings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Store PMD scan findings in the database
        
        Args:
            findings: List of PMD findings to store
            
        Returns:
            Dictionary containing processing results
        """
        try:
            processed_count = 0
            error_count = 0
            
            for finding in findings:
                try:
                    # Insert finding into PMDScans table
                    insert_query = """
                        INSERT INTO PMDScans (
                            IntegrationId, TaskId, ExecutionLogId, BlobPrefix,
                            ScanType, Language, Tool, PolicyName, RuleName,
                            Status, Severity, FileName, FilePath, LineNumber,
                            Package, IssueType, IssueCategory, IssueDescription,
                            IssuePriority, PMDRuleSet, PMDProblem, WeaknessType,
                            WeaknessTypeDescription, ComponentType, CreatedAt
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    # Map finding fields to database columns
                    params = (
                        finding.get('integration_id'),
                        finding.get('task_id'),
                        finding.get('execution_log_id'),
                        finding.get('blob_prefix', ''),
                        'PMD',  # ScanType
                        'Apex',  # Language
                        'PMD',  # Tool
                        f"Static Code Analysis (PMD) - {finding.get('category', 'Code Quality')}",  # PolicyName
                        finding.get('rule_name'),
                        finding.get('status', 'failed'),
                        finding.get('severity', 'Medium'),
                        finding.get('file_name'),
                        finding.get('file_path', ''),
                        finding.get('line_number'),
                        finding.get('package', ''),
                        finding.get('rule_name'),  # IssueType
                        finding.get('category', 'Code Quality'),  # IssueCategory
                        finding.get('description', ''),  # IssueDescription
                        finding.get('priority', ''),  # IssuePriority
                        finding.get('rule_set', ''),  # PMDRuleSet
                        finding.get('problem', ''),  # PMDProblem
                        self._get_weakness_type(finding.get('rule_name', ''), finding.get('category', ''))[0],  # WeaknessType
                        self._get_weakness_type(finding.get('rule_name', ''), finding.get('category', ''))[1],  # WeaknessTypeDescription
                        finding.get('component_type', ''),  # ComponentType
                        datetime.now().isoformat()  # CreatedAt
                    )
                    
                    self.execute_non_query(insert_query, params)
                    processed_count += 1
                    
                except Exception as e:
                    logger.error(f"Error storing finding: {e}")
                    error_count += 1
                    continue
            
            logger.info(f"Successfully processed {processed_count} findings, {error_count} errors")
            
            return {
                "processed_count": processed_count,
                "error_count": error_count,
                "total_findings": len(findings)
            }
            
        except Exception as e:
            logger.error(f"Error storing PMD findings: {e}")
            raise
    
    def get_findings_for_integration(self, 
                                   integration_id: str, 
                                   limit: int = 100, 
                                   offset: int = 0,
                                   severity: Optional[str] = None,
                                   category: Optional[str] = None) -> Dict[str, Any]:
        """
        Get PMD findings for a specific integration
        
        Args:
            integration_id: Integration ID
            limit: Maximum number of findings to return
            offset: Number of findings to skip
            severity: Filter by severity
            category: Filter by category
            
        Returns:
            Dictionary containing PMD findings and metadata
        """
        try:
            # Build WHERE clause
            where_conditions = ["IntegrationId = ?"]
            params = [integration_id]
            
            if severity:
                where_conditions.append("Severity = ?")
                params.append(severity)
            
            if category:
                where_conditions.append("IssueCategory = ?")
                params.append(category)
            
            where_clause = " AND ".join(where_conditions)
            
            # Get total count
            count_query = f"SELECT COUNT(*) FROM PMDScans WHERE {where_clause}"
            count_result = self.execute_query(count_query, params)
            total_count = count_result[0][0] if count_result else 0
            
            # Get findings with pagination
            query = f"""
                SELECT IntegrationId, TaskId, ExecutionLogId, BlobPrefix,
                       ScanType, Language, Tool, PolicyName, RuleName,
                       Status, Severity, FileName, FilePath, LineNumber,
                       Package, IssueType, IssueCategory, IssueDescription,
                       IssuePriority, PMDRuleSet, PMDProblem, WeaknessType,
                       WeaknessTypeDescription, CreatedAt
                FROM PMDScans 
                WHERE {where_clause}
                ORDER BY CreatedAt DESC
                OFFSET ? ROWS FETCH NEXT ? ROWS ONLY
            """
            
            params.extend([offset, limit])
            findings = self.execute_query(query, params)
            
            # Convert to list of dictionaries
            result_findings = []
            for finding in findings:
                result_findings.append({
                    "integration_id": finding[0],
                    "task_id": finding[1],
                    "execution_log_id": finding[2],
                    "blob_prefix": finding[3],
                    "scan_type": finding[4],
                    "language": finding[5],
                    "tool": finding[6],
                    "policy_name": finding[7],
                    "rule_name": finding[8],
                    "status": finding[9],
                    "severity": finding[10],
                    "file_name": finding[11],
                    "file_path": finding[12],
                    "line_number": finding[13],
                    "package": finding[14],
                    "issue_type": finding[15],
                    "issue_category": finding[16],
                    "issue_description": finding[17],
                    "issue_priority": finding[18],
                    "pmd_rule_set": finding[19],
                    "pmd_problem": finding[20],
                    "weakness_type": finding[21],
                    "weakness_type_description": finding[22],
                    "created_at": finding[23]
                })
            
            return {
                "findings": result_findings,
                "total_count": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": (offset + limit) < total_count
            }
            
        except Exception as e:
            logger.error(f"Error getting PMD findings for integration {integration_id}: {e}")
            return {
                "findings": [],
                "total_count": 0,
                "limit": limit,
                "offset": offset,
                "has_more": False
            }
    
    def _get_weakness_type(self, rule_name: str, category: str) -> tuple[str, str]:
        """
        Map PMD rule name and category to a standard weakness type and description
        
        Args:
            rule_name: PMD rule name
            category: PMD category
            
        Returns:
            Tuple of (weakness_type, description)
        """
        # Security-related rules
        security_rules = {
            'ApexBadCrypto': ('Security Best Practice', 'Cryptographic vulnerabilities and insecure encryption practices'),
            'ApexCRUDViolation': ('Security Best Practice', 'CRUD permission violations and unauthorized data access'),
            'ApexDangerousMethods': ('Security Best Practice', 'Use of dangerous methods that can lead to security vulnerabilities'),
            'ApexInsecureEndpoint': ('Security Best Practice', 'Insecure endpoint configurations and API security issues'),
            'ApexOpenRedirect': ('Security Best Practice', 'Open redirect vulnerabilities that can be exploited for phishing'),
            'ApexSharingViolations': ('Security Best Practice', 'Sharing and security violations in data access patterns'),
            'ApexSOQLInjection': ('Security Best Practice', 'SOQL injection vulnerabilities from unvalidated user input'),
            'ApexSuggestUsingNamedCred': ('Security Best Practice', 'Recommendations for using named credentials for secure authentication'),
            'ApexXSSFromEscapeFalse': ('Security Best Practice', 'Cross-site scripting vulnerabilities from unescaped output'),
            'ApexXSSFromURLParam': ('Security Best Practice', 'Cross-site scripting vulnerabilities from URL parameters')
        }
        
        # Performance-related rules
        performance_rules = {
            'ApexSOQLInLoop': ('Performance Best Practice', 'SOQL queries inside loops causing performance degradation'),
            'ApexDMLInLoop': ('Performance Best Practice', 'DML operations inside loops causing governor limit issues'),
            'ApexCPUtimeLimit': ('Performance Best Practice', 'CPU time limit violations and inefficient processing'),
            'ApexHeapSizeLimit': ('Performance Best Practice', 'Heap size limit violations and memory management issues'),
            'ApexExcessiveParameterList': ('Performance Best Practice', 'Methods with too many parameters affecting maintainability'),
            'ApexExcessiveClassLength': ('Performance Best Practice', 'Classes that are too long and violate single responsibility principle'),
            'ApexExcessiveMethodLength': ('Performance Best Practice', 'Methods that are too long and difficult to maintain'),
            'ApexExcessivePublicCount': ('Performance Best Practice', 'Classes with too many public methods violating encapsulation')
        }
        
        # Check specific rule names first
        if rule_name in security_rules:
            return security_rules[rule_name]
        elif rule_name in performance_rules:
            return performance_rules[rule_name]
        
        # Fallback to category-based mapping
        category_lower = category.lower()
        if 'security' in category_lower:
            return ('Security Best Practice', 'Security-related issues that could lead to vulnerabilities or unauthorized access')
        elif 'performance' in category_lower:
            return ('Performance Best Practice', 'Performance-related issues that could affect system efficiency and governor limits')
        else:
            return ('Code Quality', 'General code quality issues that should be addressed for better maintainability') 