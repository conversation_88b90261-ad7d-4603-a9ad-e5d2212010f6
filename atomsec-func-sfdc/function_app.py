"""
AtomSec SFDC Function App - Deployment-Optimized Version

A simplified Azure Function App for Salesforce integration and security scanning.
Optimized for reliable deployment and runtime stability.
"""

import logging
import json
import os
import sys
from datetime import datetime, timezone
from typing import Dict, Any, Optional

import azure.functions as func

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create the Function App with basic configuration
app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)

# Handler functions for testing
def handle_test_request(req: func.HttpRequest) -> func.HttpResponse:
    """
    Test endpoint handler - can be called directly in tests
    """
    try:
        logger.info("Test endpoint called")
        
        response_data = {
            "message": "AtomSec SFDC Function App is running",
            "status": "success",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": "atomsec-func-sfdc",
            "version": "2.0.0",
            "runtime": {
                "python": sys.version.split()[0],
                "functions_worker": os.getenv('FUNCTIONS_WORKER_RUNTIME_VERSION', 'Unknown')
            }
        }
        
        return func.HttpResponse(
            json.dumps(response_data, indent=2),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Test endpoint error: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "message": "Test endpoint error",
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }),
            mimetype="application/json",
            status_code=500
        )

@app.route(route="test", methods=["GET"])
def test_function(req: func.HttpRequest) -> func.HttpResponse:
    """Test endpoint"""
    return handle_test_request(req)

def handle_health_request(req: func.HttpRequest) -> func.HttpResponse:
    """
    Health check handler - comprehensive but JSON-safe
    """
    try:
        # Use timezone-aware datetime (fixes Python 3.12+ deprecation warning)
        timestamp = datetime.now(timezone.utc).isoformat()
        
        # Get safe environment values (avoid potential mock objects)
        python_version = str(sys.version.split()[0]) if sys.version else "Unknown"
        functions_version = str(os.getenv('FUNCTIONS_WORKER_RUNTIME_VERSION', 'Unknown'))
        deployment_mode = str(os.getenv('DEPLOYMENT_MODE', 'production'))
        site_name = str(os.getenv('WEBSITE_SITE_NAME', 'Unknown'))
        
        health_data = {
            "status": "healthy",
            "service": "atomsec-func-sfdc",
            "timestamp": timestamp,
            "version": "2.0.0",
            "scope": "salesforce_integration_and_security_scanning",
            "environment": {
                "python_version": python_version,
                "functions_version": functions_version,
                "deployment_mode": deployment_mode,
                "site_name": site_name
            },
            "checks": {
                "function_app": "healthy",
                "runtime": "healthy",
                "blueprints": "registered",
                "endpoints": "available"
            },
            "dependencies": {
                "db_service": "external - atomsec-func-db-r",
                "authentication": "external - atomsec-func-db-r"
            },
            "notes": {
                "authentication_removed": "All auth endpoints moved to DB service",
                "user_management_removed": "All user management moved to DB service"
            }
        }
        
        return func.HttpResponse(
            json.dumps(health_data, indent=2),
            mimetype="application/json",
            status_code=200,
            headers={
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0"
            }
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        
        try:
            error_timestamp = datetime.now(timezone.utc).isoformat()
        except Exception:
            error_timestamp = "timestamp_error"
        
        return func.HttpResponse(
            json.dumps({
                "status": "unhealthy",
                "service": "atomsec-func-sfdc",
                "error": str(e),
                "timestamp": error_timestamp,
                "version": "2.0.0"
            }),
            mimetype="application/json",
            status_code=503,
            headers={
                "Cache-Control": "no-cache, no-store, must-revalidate"
            }
        )

@app.route(route="health", methods=["GET"])
def health_check(req: func.HttpRequest) -> func.HttpResponse:
    """Health check endpoint"""
    return handle_health_request(req)

def handle_info_request(req: func.HttpRequest) -> func.HttpResponse:
    """
    Service information handler - can be called directly in tests
    """
    try:
        info_data = {
            "service": "atomsec-func-sfdc",
            "version": "2.0.0",
            "description": "AtomSec SFDC Service - Salesforce integration and security scanning",
            "status": "active",
            "deployment_optimized": True,
            "scope": "Salesforce integration and security scanning only",
            "user_management_note": "User management is handled by atomsec-func-db-r service",
            "authentication_note": "Authentication is handled by atomsec-func-db-r service", 
            "endpoints": [
                {
                    "path": "/api/test",
                    "method": "GET",
                    "description": "Test endpoint to verify service is running"
                },
                {
                    "path": "/api/health",
                    "method": "GET",
                    "description": "Health check endpoint"
                },
                {
                    "path": "/api/info",
                    "method": "GET",
                    "description": "Service information endpoint"
                },
                {
                    "path": "/api/accounts",
                    "method": "GET",
                    "description": "Salesforce account management endpoint"
                },
                {
                    "path": "/api/integrations",
                    "method": "GET",
                    "description": "Salesforce integration management"
                },
                {
                    "path": "/api/security/*",
                    "method": "GET/POST",
                    "description": "Security scanning and analysis endpoints"
                },
                {
                    "path": "/api/pmd/*",
                    "method": "GET/POST",
                    "description": "PMD code analysis endpoints"
                }
            ],
            "documentation": {
                "azure_functions": "https://learn.microsoft.com/en-us/azure/azure-functions/",
                "api_docs": "/api/docs"
            }
        }
        
        return func.HttpResponse(
            json.dumps(info_data, indent=2),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Info endpoint error: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }),
            mimetype="application/json",
            status_code=500
        )

@app.route(route="info", methods=["GET"])
def info(req: func.HttpRequest) -> func.HttpResponse:
    """Service information endpoint"""
    return handle_info_request(req)

# Import and register API blueprints safely
def register_blueprints():
    """
    Register API blueprints with proper error handling
    """
    blueprints_to_register = [
        # REMOVED auth_endpoints - authentication is handled by DB service
        # REMOVED user_endpoints - user management is handled by DB service
        # 
        # SFDC service focuses on Salesforce integration and security scanning:
        ('api.account_endpoints', 'account_bp'),         # Salesforce account management
        ('api.organization_endpoints', 'organization_bp'), # Salesforce org management
        ('api.integration_endpoints', 'integration_bp'),  # SFDC integration management
        ('api.security_endpoints', 'security_bp'),       # Security scanning functions
        ('api.task_endpoints', 'task_bp'),               # Task/job management
        ('api.policy_endpoints', 'policy_bp'),           # Security policy management
        ('api.cors_handler', 'cors_bp'),                 # CORS handling
        ('api.user_profile_endpoints', 'user_profile_bp'), # User profile (not user management)
        ('api.key_vault_endpoints', 'key_vault_bp'),     # Azure Key Vault integration
        ('api.pmd_endpoints', 'pmd_bp')                  # PMD code analysis
    ]
    
    registered_count = 0
    for module_name, blueprint_name in blueprints_to_register:
        try:
            module = __import__(module_name, fromlist=[blueprint_name])
            blueprint = getattr(module, 'bp', None)
            
            if blueprint:
                app.register_functions(blueprint)
                logger.info(f"Successfully registered blueprint: {module_name}")
                registered_count += 1
            else:
                logger.warning(f"Blueprint 'bp' not found in module: {module_name}")
                
        except ImportError as e:
            logger.warning(f"Could not import module {module_name}: {str(e)}")
        except Exception as e:
            logger.warning(f"Error registering blueprint {module_name}: {str(e)}")
    
    logger.info(f"Registered {registered_count} blueprints successfully")
    return registered_count

# Register blueprints at module level (safer for deployment)
# Note: Core functions (test, health, info, status, debug) are always available
# Blueprint registration is optional and graceful
try:
    blueprint_count = register_blueprints()
    logger.info(f"AtomSec SFDC Function App initialized with {blueprint_count} blueprints")
except Exception as e:
    logger.error(f"Error during blueprint registration: {str(e)}")
    logger.info("Function App initialized with basic endpoints only")
    # Continue execution - core endpoints are still available

# Additional utility endpoints for debugging and monitoring
def handle_debug_version_request(req: func.HttpRequest) -> func.HttpResponse:
    """
    Debug version handler - can be called directly in tests
    """
    try:
        debug_info = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": "atomsec-func-sfdc",
            "version": "2.0.0",
            "python_version": sys.version,
            "platform": sys.platform,
            "environment_variables": {
                key: value for key, value in os.environ.items()
                if key.startswith(('AZURE_', 'FUNCTIONS_', 'WEBSITE_', 'SCM_'))
            },
            "deployment_info": {
                "site_name": os.getenv('WEBSITE_SITE_NAME', 'Unknown'),
                "resource_group": os.getenv('WEBSITE_RESOURCE_GROUP', 'Unknown'),
                "subscription_id": os.getenv('WEBSITE_OWNER_NAME', 'Unknown'),
                "deployment_id": os.getenv('WEBSITE_DEPLOYMENT_ID', 'Unknown')
            }
        }
        
        return func.HttpResponse(
            json.dumps(debug_info, indent=2),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Debug version endpoint error: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }),
            mimetype="application/json",
            status_code=500
        )

@app.route(route="debug/version", methods=["GET"])
def debug_version(req: func.HttpRequest) -> func.HttpResponse:
    """Debug endpoint to show version and environment information"""
    return handle_debug_version_request(req)

def handle_status_request(req: func.HttpRequest) -> func.HttpResponse:
    """
    Status handler - can be called directly in tests
    """
    try:
        status_data = {
            "status": "online",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "uptime": "running",
            "service": "atomsec-func-sfdc"
        }
        
        return func.HttpResponse(
            json.dumps(status_data),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Status endpoint error: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }),
            mimetype="application/json",
            status_code=500
        )

@app.route(route="status", methods=["GET"])
def status(req: func.HttpRequest) -> func.HttpResponse:
    """Simple status endpoint"""
    return handle_status_request(req)

# Log successful initialization with deployment context
deployment_context = {
    "module_loaded": True,
    "core_functions": ["test_function", "health_check", "info", "status", "debug_version"],
    "blueprint_registration": "attempted",
    "azure_environment": {
        "site_name": os.getenv('WEBSITE_SITE_NAME', 'local'),
        "deployment_id": os.getenv('WEBSITE_DEPLOYMENT_ID', 'unknown'),
        "functions_version": os.getenv('FUNCTIONS_EXTENSION_VERSION', 'unknown')
    }
}

logger.info("AtomSec SFDC Function App module loaded successfully")
logger.info(f"Deployment context: {deployment_context}")

# Azure Functions discovery validation
logger.info("Available function endpoints:")
logger.info("  GET /api/test - Test endpoint")
logger.info("  GET /api/health - Health check endpoint") 
logger.info("  GET /api/info - Service information endpoint")
logger.info("  GET /api/status - Simple status endpoint")
logger.info("  GET /api/debug/version - Debug and system information")
logger.info("Function App ready for Azure Functions host discovery")