#!/usr/bin/env python3
"""
Quick Deployment Fix for AtomSec SFDC Function App

This script provides immediate fixes for the container health check failures.
Run this before deploying to resolve the issues.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_prerequisites():
    """Check if required tools are available"""
    print("🔍 Checking prerequisites...")
    
    # Check Azure CLI
    try:
        result = subprocess.run(["az", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Azure CLI available")
        else:
            print("❌ Azure CLI not working")
            return False
    except FileNotFoundError:
        print("❌ Azure CLI not found")
        return False
    
    # Check Azure Functions Core Tools
    try:
        result = subprocess.run(["func", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Azure Functions Core Tools available")
        else:
            print("❌ Azure Functions Core Tools not working")
            return False
    except FileNotFoundError:
        print("❌ Azure Functions Core Tools not found")
        return False
    
    return True

def set_deployment_environment():
    """Set environment variables for deployment"""
    print("\n🚀 Setting deployment environment...")
    
    # Set deployment mode
    os.environ['DEPLOYMENT_MODE'] = 'deployment'
    os.environ['AZURE_FUNCTIONS_ENVIRONMENT'] = 'Development'
    
    print("✅ Environment variables set:")
    print(f"  DEPLOYMENT_MODE: {os.environ.get('DEPLOYMENT_MODE')}")
    print(f"  AZURE_FUNCTIONS_ENVIRONMENT: {os.environ.get('AZURE_FUNCTIONS_ENVIRONMENT')}")

def deploy_with_fixes():
    """Deploy the function app with the fixes applied"""
    print("\n📦 Deploying function app with fixes...")
    
    try:
        # Deploy using Azure Functions Core Tools
        cmd = [
            "func", "azure", "functionapp", "publish",
            "func-atomsec-sfdc-dev02",
            "--build", "remote",
            "--python"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        print("⏳ This may take several minutes...")
        
        # Run deployment with current environment
        result = subprocess.run(cmd, env=os.environ)
        
        if result.returncode == 0:
            print("✅ Deployment completed successfully!")
            return True
        else:
            print(f"❌ Deployment failed with return code: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Deployment error: {str(e)}")
        return False

def verify_deployment():
    """Verify the deployment is working"""
    print("\n🧪 Verifying deployment...")
    
    try:
        # Test the function app
        test_url = "https://func-atomsec-sfdc-dev02-cydhasanfhdmdgar.eastus-01.azurewebsites.net/api/test"
        print(f"Testing endpoint: {test_url}")
        
        # Use curl if available, otherwise provide manual instructions
        try:
            result = subprocess.run(["curl", "-s", "-o", "/dev/null", "-w", "%{http_code}", test_url], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                status_code = result.stdout.strip()
                print(f"Response status: {status_code}")
                
                if status_code == "200":
                    print("✅ Function app is responding correctly!")
                    return True
                else:
                    print(f"⚠️  Function app responded with status {status_code}")
                    return False
            else:
                print("⚠️  Could not test endpoint automatically")
                print("Please manually test the endpoint:")
                print(f"  curl {test_url}")
                return True
                
        except FileNotFoundError:
            print("⚠️  curl not available - please test manually:")
            print(f"  curl {test_url}")
            return True
            
    except Exception as e:
        print(f"❌ Verification failed: {str(e)}")
        return False

def main():
    """Main function"""
    print("🚀 AtomSec SFDC Function App - Quick Deployment Fix")
    print("=" * 60)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please install required tools.")
        sys.exit(1)
    
    # Set deployment environment
    set_deployment_environment()
    
    # Deploy with fixes
    if not deploy_with_fixes():
        print("\n❌ Deployment failed!")
        print("Please check the error messages above and try again.")
        sys.exit(1)
    
    # Verify deployment
    if not verify_deployment():
        print("\n⚠️  Deployment verification incomplete")
        print("Please check the function app manually.")
    
    print("\n🎉 Quick deployment fix completed!")
    print("\nNext steps:")
    print("1. Verify the function app is working correctly")
    print("2. Test the endpoints manually")
    print("3. Once stable, remove DEPLOYMENT_MODE for full functionality")

if __name__ == "__main__":
    main()
