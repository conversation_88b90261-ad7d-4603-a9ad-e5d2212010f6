"""
Test SFDC Service Scope and Service Separation

This module tests that the SFDC service correctly focuses on Salesforce 
integration and security scanning, while authentication and user management
are handled by the DB service.
"""

import pytest
import json
from datetime import datetime
from unittest.mock import patch, MagicMock
import azure.functions as func


class TestSfdcServiceScope:
    """Test cases to verify SFDC service focuses on its intended scope"""

    def test_service_scope_in_info_endpoint(self):
        """Test that the info endpoint clearly defines the service scope"""
        from function_app import handle_info_request
        
        # Create mock request
        req = MagicMock(spec=func.HttpRequest)
        
        # Call the handler function
        response = handle_info_request(req)
        
        # Verify response
        assert response.status_code == 200
        
        # Parse response data
        response_data = json.loads(response.get_body().decode())
        
        # Check service scope is clearly defined
        assert "scope" in response_data
        assert "Salesforce integration and security scanning only" in response_data["scope"]
        
        # Check that user management note is present
        assert "user_management_note" in response_data
        assert "atomsec-func-db-r" in response_data["user_management_note"]
        
        # Check that authentication note is present
        assert "authentication_note" in response_data
        assert "atomsec-func-db-r" in response_data["authentication_note"]
        
        # Verify service focuses on SFDC-related endpoints
        endpoints = response_data.get("endpoints", [])
        sfdc_focused_paths = [
            "/api/accounts",         # Salesforce account management
            "/api/integrations",     # Salesforce integration management
            "/api/security/*",       # Security scanning
            "/api/pmd/*"            # PMD code analysis
        ]
        
        endpoint_paths = [ep.get("path") for ep in endpoints if ep.get("path")]
        
        # Check that SFDC-focused endpoints are documented
        for path in sfdc_focused_paths:
            assert any(path in ep_path for ep_path in endpoint_paths), f"Expected SFDC endpoint {path} not found"
    
    def test_no_authentication_endpoints_registered(self):
        """Test that authentication endpoints are not registered in the SFDC service"""
        # Import the blueprint registration function
        from function_app import register_blueprints
        
        # Mock the import and registration process at the builtins level
        with patch('builtins.__import__') as mock_import:
            with patch('function_app.logger') as mock_logger:
                # Mock successful import but track what gets imported
                mock_import.side_effect = lambda name, *args, **kwargs: __import__(name, *args, **kwargs)
                
                # Call blueprint registration
                count = register_blueprints()
                
                # Verify that auth-related modules are not imported
                import_calls = [call[0][0] for call in mock_import.call_args_list if mock_import.call_args_list]
                
                # These should NOT be in the import calls
                forbidden_modules = [
                    'api.auth_endpoints',
                    'api.user_endpoints'  # Contains user login functions
                ]
                
                for forbidden_module in forbidden_modules:
                    assert forbidden_module not in import_calls, f"Forbidden module {forbidden_module} was imported"
    
    def test_service_version_is_updated(self):
        """Test that the service version reflects the deployment optimization changes"""
        from function_app import handle_test_request, handle_health_request, handle_info_request
        
        # Create mock request
        req = MagicMock(spec=func.HttpRequest)
        
        # Test version in multiple endpoints
        endpoints_to_test = [
            ('handle_test_request', handle_test_request),
            ('handle_health_request', handle_health_request), 
            ('handle_info_request', handle_info_request)
        ]
        
        for endpoint_name, endpoint_func in endpoints_to_test:
            response = endpoint_func(req)
            assert response.status_code == 200, f"{endpoint_name} failed"
            
            response_data = json.loads(response.get_body().decode())
            assert "version" in response_data, f"Version missing in {endpoint_name}"
            assert response_data["version"] == "2.0.0", f"Wrong version in {endpoint_name}"
    
    def test_deployment_optimized_flag(self):
        """Test that the service indicates it's deployment optimized"""
        from function_app import handle_info_request
        
        # Create mock request
        req = MagicMock(spec=func.HttpRequest)
        
        # Call the handler function
        response = handle_info_request(req)
        
        # Parse response data
        response_data = json.loads(response.get_body().decode())
        
        # Check deployment optimized flag is present and True
        assert "deployment_optimized" in response_data
        assert response_data["deployment_optimized"] is True
    
    def test_service_description_reflects_scope(self):
        """Test that service description clearly indicates SFDC focus"""
        from function_app import handle_info_request
        
        # Create mock request
        req = MagicMock(spec=func.HttpRequest)
        
        # Call the handler function
        response = handle_info_request(req)
        
        # Parse response data
        response_data = json.loads(response.get_body().decode())
        
        # Check description focuses on SFDC
        description = response_data.get("description", "")
        assert "Salesforce integration" in description
        assert "security scanning" in description
        
        # Service name should clearly indicate SFDC focus
        assert response_data.get("service") == "atomsec-func-sfdc"


class TestRemovedEndpoints:
    """Test cases to ensure authentication endpoints are properly removed"""
    
    def test_authentication_blueprints_not_registered(self):
        """Test that authentication blueprints are not registered"""
        # This test simply verifies that the authentication modules are not in our 
        # blueprints_to_register list by checking the actual registration logic
        from function_app import register_blueprints
        
        # Check what actually gets registered by inspecting the blueprint registration
        with patch('function_app.logger') as mock_logger:
            count = register_blueprints()
            
            # Check the log messages to see what blueprints were registered
            registered_blueprints = []
            for call in mock_logger.info.call_args_list:
                message = call[0][0] if call[0] else ""
                if "Successfully registered blueprint:" in message:
                    blueprint_name = message.split(": ")[-1]
                    registered_blueprints.append(blueprint_name)
            
            # Verify that forbidden modules are NOT registered
            forbidden_modules = ['api.auth_endpoints', 'api.user_endpoints']
            for forbidden_module in forbidden_modules:
                assert forbidden_module not in registered_blueprints, \
                    f"Authentication module {forbidden_module} should not be registered"
    
    def test_user_management_blueprints_not_registered(self):
        """Test that user management blueprints are not registered""" 
        # This is covered in the authentication test above since user_endpoints
        # contains both user management AND authentication functions
        pass


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
