"""
Integration tests for the AtomSec SFDC Function App

This module contains integration tests that verify the overall functionality
of the function app without requiring external dependencies.
"""

import pytest
import json
import os
from unittest.mock import patch, MagicMock
import azure.functions as func


class TestFunctionAppIntegration:
    """Integration tests for the main function app"""

    def test_function_app_initialization(self):
        """Test that the function app initializes correctly"""
        try:
            # Import the function app
            import function_app
            
            # Verify the app exists
            assert hasattr(function_app, 'app')
            assert function_app.app is not None
            
        except ImportError:
            pytest.fail("Could not import function_app module")
        except Exception as e:
            pytest.fail(f"Function app initialization failed: {str(e)}")

    @patch('function_app.logger')
    def test_blueprint_registration_graceful_failure(self, mock_logger):
        """Test that blueprint registration fails gracefully"""
        # This test verifies that if blueprints fail to load, the app still works
        with patch('function_app.register_blueprints') as mock_register:
            mock_register.side_effect = Exception("Blueprint loading failed")
            
            try:
                # Re-import to trigger blueprint registration
                import importlib
                import function_app
                importlib.reload(function_app)
                
                # Should not crash
                assert function_app.app is not None
                
            except Exception:
                # Even if this fails, the basic endpoints should work
                pass

    def test_basic_endpoints_available(self):
        """Test that basic endpoints are available"""
        from function_app import handle_test_request, handle_health_request, handle_info_request
        
        # Verify handler functions exist and are callable
        assert callable(handle_test_request)
        assert callable(handle_health_request)
        assert callable(handle_info_request)

    def test_endpoint_response_format_consistency(self):
        """Test that all endpoints return consistent JSON response format"""
        from function_app import handle_test_request, handle_health_request, handle_info_request
        
        # Create mock request
        req = MagicMock(spec=func.HttpRequest)
        
        # Test each endpoint handler
        endpoints = [
            ("test", handle_test_request),
            ("health", handle_health_request),
            ("info", handle_info_request)
        ]
        
        for endpoint_name, endpoint_func in endpoints:
            with patch('function_app.datetime') as mock_datetime:
                # Mock the new timezone-aware datetime pattern
                mock_now = MagicMock()
                mock_now.isoformat.return_value = "2024-01-01T12:00:00+00:00"
                mock_datetime.now.return_value = mock_now
                
                response = endpoint_func(req)
                
                # Verify response format
                assert response.status_code in [200, 201, 400, 404, 500, 503]
                assert response.mimetype == "application/json"
                
                # Verify JSON is valid
                try:
                    response_data = json.loads(response.get_body().decode())
                    assert isinstance(response_data, dict)
                except json.JSONDecodeError:
                    pytest.fail(f"Endpoint {endpoint_name} returned invalid JSON")

    def test_task_endpoint_data_processing(self):
        """Test that task endpoint processes data correctly"""
        # Note: handle_test_task_request was removed in v2.0.0 deployment optimization
        # Task handling is now done through blueprint endpoints, not a dedicated test handler
        import pytest
        pytest.skip("handle_test_task_request removed in v2.0.0 - task handling moved to blueprints")

    def test_error_handling_consistency(self):
        """Test that error handling is consistent across endpoints"""
        from function_app import handle_test_request, handle_health_request
        
        req = MagicMock(spec=func.HttpRequest)
        
        # Test 1: Force error in test_request function (has logger.info call)
        with patch('function_app.logger') as mock_logger:
            mock_logger.info.side_effect = Exception("Forced error")
            mock_logger.error.return_value = None
            
            response = handle_test_request(req)
            assert response.status_code == 500
            assert response.mimetype == "application/json"
            response_data = json.loads(response.get_body().decode())
            assert "error" in response_data
        
        # Test 2: Force error in health_request function (using different approach)
        with patch('function_app.datetime') as mock_datetime:
            # Make datetime.now fail to trigger exception handling
            mock_datetime.now.side_effect = Exception("Forced error")
            
            response = handle_health_request(req)
            assert response.status_code == 503
            assert response.mimetype == "application/json"
            response_data = json.loads(response.get_body().decode())
            assert "error" in response_data or "status" in response_data


class TestBlueprintIntegration:
    """Integration tests for blueprint functionality"""

    def test_blueprint_import_safety(self):
        """Test that blueprint imports don't crash the app"""
        # This test verifies that even if blueprints have issues, 
        # the main app continues to work
        
        blueprint_modules = [
            'api.user_endpoints',
            'api.account_endpoints', 
            'api.auth_endpoints',
            'api.cors_handler'
        ]
        
        for module_name in blueprint_modules:
            try:
                __import__(module_name)
                # If import succeeds, that's good
            except ImportError:
                # If import fails, that's also okay for this test
                pass
            except Exception as e:
                # Other exceptions should not crash the test
                pytest.skip(f"Blueprint {module_name} has import issues: {str(e)}")

    def test_shared_module_availability(self):
        """Test that shared modules are available for blueprints"""
        shared_modules = [
            'shared.common',
            'shared.config',
            'shared.cors_middleware'
        ]
        
        for module_name in shared_modules:
            try:
                module = __import__(module_name, fromlist=[''])
                assert module is not None
            except ImportError:
                pytest.skip(f"Shared module {module_name} not available")

    def test_cors_integration(self):
        """Test CORS integration across the app"""
        try:
            from shared.cors_middleware import add_cors_headers
            
            # Test CORS headers can be applied
            # Create a proper HttpResponse object, not a string
            response_body = "test"
            response = func.HttpResponse(
                body=response_body,
                status_code=200,
                mimetype="text/plain"
            )
            cors_response = add_cors_headers(response, "http://localhost:3000")
            
            assert cors_response is not None
            # Verify CORS headers were added
            assert hasattr(cors_response, 'headers')
            
        except ImportError:
            pytest.skip("CORS middleware not available")
        except Exception as e:
            pytest.skip(f"CORS integration test failed: {str(e)}")


class TestEnvironmentIntegration:
    """Integration tests for environment-specific functionality"""

    @patch('shared.common.is_local_dev')
    def test_local_development_configuration(self, mock_is_local_dev):
        """Test configuration in local development environment"""
        mock_is_local_dev.return_value = True
        
        try:
            from shared.common import is_local_dev
            assert is_local_dev() is True
            
        except ImportError:
            pytest.skip("Environment detection not available")

    @patch('shared.common.is_test_env')
    def test_test_environment_configuration(self, mock_is_test_env):
        """Test configuration in test environment"""
        mock_is_test_env.return_value = True
        
        try:
            from shared.common import is_test_env
            assert is_test_env() is True
            
        except ImportError:
            pytest.skip("Test environment detection not available")

    def test_environment_variable_handling(self):
        """Test that environment variables are handled safely"""
        # Test with missing environment variables
        with patch.dict(os.environ, {}, clear=True):
            try:
                from shared.common import is_local_dev, is_test_env
                
                # Should not crash with missing env vars
                local_result = is_local_dev()
                test_result = is_test_env()
                
                assert isinstance(local_result, bool)
                assert isinstance(test_result, bool)
                
            except ImportError:
                pytest.skip("Environment detection modules not available")


class TestDataIntegration:
    """Integration tests for data handling"""

    def test_json_serialization_compatibility(self):
        """Test that data structures can be serialized to JSON"""
        from datetime import datetime
        
        # Test common data structures used in the app
        test_data = {
            "string_value": "test",
            "int_value": 123,
            "bool_value": True,
            "list_value": [1, 2, 3],
            "dict_value": {"nested": "value"}
        }
        
        # Should serialize without issues
        json_str = json.dumps(test_data)
        assert isinstance(json_str, str)
        
        # Should deserialize back
        parsed_data = json.loads(json_str)
        assert parsed_data == test_data

    def test_azure_functions_compatibility(self):
        """Test compatibility with Azure Functions runtime"""
        # Test that we can create Azure Functions objects
        response = func.HttpResponse("test", status_code=200)
        assert response.status_code == 200
        
        # Test request simulation
        req = MagicMock(spec=func.HttpRequest)
        assert req is not None


@pytest.mark.slow
class TestPerformanceIntegration:
    """Performance integration tests (marked as slow)"""

    def test_endpoint_response_times(self):
        """Test that endpoints respond within reasonable time"""
        import time
        from function_app import handle_test_request
        
        req = MagicMock(spec=func.HttpRequest)
        
        # Measure response time
        start_time = time.time()
        response = handle_test_request(req)
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # Should respond within 5 seconds (generous for testing)
        assert response_time < 5.0
        assert response.status_code == 200

    def test_multiple_requests_handling(self):
        """Test handling multiple requests"""
        from function_app import handle_test_request
        
        req = MagicMock(spec=func.HttpRequest)
        
        # Make multiple requests
        responses = []
        for _ in range(5):
            response = handle_test_request(req)
            responses.append(response)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200
