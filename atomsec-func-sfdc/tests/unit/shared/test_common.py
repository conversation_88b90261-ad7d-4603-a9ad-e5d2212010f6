"""
Unit tests for the shared common module

This module tests the common utility functions in shared/common.py
"""

import pytest
import os
from unittest.mock import patch


class TestCommon:
    """Test cases for common utility functions"""

    def test_is_local_dev_with_is_local_dev_flag(self):
        """Test is_local_dev with IS_LOCAL_DEV environment variable"""
        with patch.dict(os.environ, {'IS_LOCAL_DEV': 'true'}):
            from shared.common import is_local_dev
            assert is_local_dev() is True

    def test_is_local_dev_with_is_local_dev_false(self):
        """Test is_local_dev when IS_LOCAL_DEV is false"""
        with patch.dict(os.environ, {'IS_LOCAL_DEV': 'false'}):
            from shared.common import is_local_dev
            # Should continue checking other conditions
            # Result depends on other environment variables

    def test_is_local_dev_with_functions_worker_runtime_version_none(self):
        """Test is_local_dev when FUNCTIONS_WORKER_RUNTIME_VERSION is None"""
        env_vars = {key: value for key, value in os.environ.items() 
                   if key != 'FUNCTIONS_WORKER_RUNTIME_VERSION'}
        env_vars.pop('FUNCTIONS_WORKER_RUNTIME_VERSION', None)
        
        with patch.dict(os.environ, env_vars, clear=True):
            from shared.common import is_local_dev
            # Should detect local dev when FUNCTIONS_WORKER_RUNTIME_VERSION is not set
            # Note: This may vary based on actual environment

    def test_is_local_dev_with_use_local_storage_flag(self):
        """Test is_local_dev with USE_LOCAL_STORAGE flag"""
        with patch.dict(os.environ, {'USE_LOCAL_STORAGE': 'true'}, clear=True):
            from shared.common import is_local_dev
            assert is_local_dev() is True

    def test_is_local_dev_with_localhost_website_hostname(self):
        """Test is_local_dev with localhost WEBSITE_HOSTNAME"""
        with patch.dict(os.environ, {'WEBSITE_HOSTNAME': 'localhost:7071'}, clear=True):
            from shared.common import is_local_dev
            assert is_local_dev() is True

    def test_is_local_dev_production_environment(self):
        """Test is_local_dev in production-like environment"""
        production_env = {
            'FUNCTIONS_WORKER_RUNTIME_VERSION': '4.0',
            'WEBSITE_HOSTNAME': 'myapp.azurewebsites.net',
            'USE_LOCAL_STORAGE': 'false',
            'IS_LOCAL_DEV': 'false'
        }
        
        with patch.dict(os.environ, production_env, clear=True):
            from shared.common import is_local_dev
            assert is_local_dev() is False

    def test_is_test_env_with_atomsec_test_env(self):
        """Test is_test_env with ATOMSEC_TEST_ENV flag"""
        with patch.dict(os.environ, {'ATOMSEC_TEST_ENV': 'true'}):
            from shared.common import is_test_env
            assert is_test_env() is True

    def test_is_test_env_with_pytest_running(self):
        """Test is_test_env with PYTEST_RUNNING flag"""
        with patch.dict(os.environ, {'PYTEST_RUNNING': 'true'}):
            from shared.common import is_test_env
            assert is_test_env() is True

    def test_is_test_env_false(self):
        """Test is_test_env returns false in normal environment"""
        # Remove test environment variables
        env_vars = {key: value for key, value in os.environ.items() 
                   if key not in ['ATOMSEC_TEST_ENV', 'PYTEST_RUNNING']}
        
        with patch.dict(os.environ, env_vars, clear=True):
            from shared.common import is_test_env
            assert is_test_env() is False

    def test_is_test_env_false_with_false_values(self):
        """Test is_test_env with explicit false values"""
        with patch.dict(os.environ, {
            'ATOMSEC_TEST_ENV': 'false',
            'PYTEST_RUNNING': 'false'
        }, clear=True):
            from shared.common import is_test_env
            assert is_test_env() is False

    @patch('shared.common.logger')
    def test_is_local_dev_logs_detection(self, mock_logger):
        """Test that is_local_dev logs environment detection"""
        with patch.dict(os.environ, {'IS_LOCAL_DEV': 'true'}):
            from shared.common import is_local_dev
            is_local_dev()
            
            # Should log the detection method
            mock_logger.info.assert_called_with(
                "Local development environment detected via IS_LOCAL_DEV flag"
            )

    @patch('shared.common.logger')
    def test_is_local_dev_logs_production_detection(self, mock_logger):
        """Test that is_local_dev logs production environment detection"""
        production_env = {
            'FUNCTIONS_WORKER_RUNTIME_VERSION': '4.0',
            'WEBSITE_HOSTNAME': 'myapp.azurewebsites.net',
            'USE_LOCAL_STORAGE': 'false',
            'IS_LOCAL_DEV': 'false'
        }
        
        with patch.dict(os.environ, production_env, clear=True):
            from shared.common import is_local_dev
            is_local_dev()
            
            # Should log production detection
            mock_logger.info.assert_called_with("Production environment detected")

    def test_environment_detection_priority(self):
        """Test the priority order of environment detection"""
        # IS_LOCAL_DEV should take highest priority
        with patch.dict(os.environ, {
            'IS_LOCAL_DEV': 'true',
            'FUNCTIONS_WORKER_RUNTIME_VERSION': '4.0',  # This would normally indicate production
            'WEBSITE_HOSTNAME': 'myapp.azurewebsites.net'
        }):
            from shared.common import is_local_dev
            assert is_local_dev() is True

    def test_empty_environment_variables(self):
        """Test behavior with empty environment variables"""
        with patch.dict(os.environ, {}, clear=True):
            from shared.common import is_local_dev, is_test_env
            
            # With no environment variables, should detect local dev
            # (because FUNCTIONS_WORKER_RUNTIME_VERSION is None)
            local_result = is_local_dev()
            test_result = is_test_env()
            
            # is_test_env should be False
            assert test_result is False
            # is_local_dev behavior may vary, but should not crash
