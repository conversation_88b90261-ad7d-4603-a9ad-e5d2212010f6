"""
Unit tests for the main function app endpoints

This module tests the core Azure Function endpoints defined in function_app.py
"""

import pytest
import json
from datetime import datetime
from unittest.mock import patch, MagicMock
import azure.functions as func


class TestFunctionApp:
    """Test cases for the main function app endpoints"""

    @patch('function_app.datetime')
    def test_test_function_success(self, mock_datetime):
        """Test the test endpoint returns success"""
        # Mock the new timezone-aware datetime pattern
        mock_now = MagicMock()
        mock_now.isoformat.return_value = "2024-01-01T12:00:00+00:00"
        mock_datetime.now.return_value = mock_now
        
        # Import the handler function directly
        from function_app import handle_test_request
        
        # Create mock request
        req = MagicMock(spec=func.HttpRequest)
        
        # Call the handler function
        response = handle_test_request(req)
        
        # Verify response
        assert response.status_code == 200
        assert response.mimetype == "application/json"
        
        # Parse response data
        response_data = json.loads(response.get_body().decode())
        assert response_data["status"] == "success"
        assert response_data["service"] == "atomsec-func-sfdc"
        assert response_data["version"] == "2.0.0"
        assert "timestamp" in response_data

    @patch('function_app.datetime')
    def test_health_check_success(self, mock_datetime):
        """Test the health check endpoint returns healthy status"""
        # Mock the new timezone-aware datetime pattern
        mock_now = MagicMock()
        mock_now.isoformat.return_value = "2024-01-01T12:00:00+00:00"
        mock_datetime.now.return_value = mock_now
        
        # Import the handler function
        from function_app import handle_health_request
        
        # Create mock request
        req = MagicMock(spec=func.HttpRequest)
        
        # Call the handler function
        response = handle_health_request(req)
        
        # Verify response
        assert response.status_code == 200
        assert response.mimetype == "application/json"
        
        # Parse response data
        response_data = json.loads(response.get_body().decode())
        assert response_data["status"] == "healthy"
        assert response_data["service"] == "atomsec-func-sfdc"
        assert "environment" in response_data
        assert "scope" in response_data
        assert "dependencies" in response_data

    @patch('function_app.datetime')
    def test_info_endpoint_success(self, mock_datetime):
        """Test the info endpoint returns service information"""
        # Mock the new timezone-aware datetime pattern
        mock_now = MagicMock()
        mock_now.isoformat.return_value = "2024-01-01T12:00:00+00:00"
        mock_datetime.now.return_value = mock_now
        
        # Import the handler function
        from function_app import handle_info_request
        
        # Create mock request
        req = MagicMock(spec=func.HttpRequest)
        
        # Call the handler function
        response = handle_info_request(req)
        
        # Verify response
        assert response.status_code == 200
        assert response.mimetype == "application/json"
        
        # Parse response data
        response_data = json.loads(response.get_body().decode())
        assert response_data["service"] == "atomsec-func-sfdc"
        assert response_data["version"] == "2.0.0"
        assert "endpoints" in response_data
        assert "scope" in response_data
        assert "Salesforce integration and security scanning only" in response_data["scope"]
        # Check that user management note is present
        assert "user_management_note" in response_data
        assert "atomsec-func-db-r" in response_data["user_management_note"]

    @patch('function_app.datetime')
    def test_status_endpoint_success(self, mock_datetime):
        """Test the status endpoint returns online status"""
        # Mock the new timezone-aware datetime pattern
        mock_now = MagicMock()
        mock_now.isoformat.return_value = "2024-01-01T12:00:00+00:00"
        mock_datetime.now.return_value = mock_now
        
        # Import the handler function
        from function_app import handle_status_request
        
        # Create mock request
        req = MagicMock(spec=func.HttpRequest)
        
        # Call the handler function
        response = handle_status_request(req)
        
        # Verify response
        assert response.status_code == 200
        assert response.mimetype == "application/json"
        
        # Parse response data
        response_data = json.loads(response.get_body().decode())
        assert response_data["status"] == "online"
        assert response_data["service"] == "atomsec-func-sfdc"
        assert "timestamp" in response_data

    @patch('function_app.datetime')  
    def test_debug_version_endpoint_success(self, mock_datetime):
        """Test the debug version endpoint returns system information"""
        # Mock the new timezone-aware datetime pattern
        mock_now = MagicMock()
        mock_now.isoformat.return_value = "2024-01-01T12:00:00+00:00"
        mock_datetime.now.return_value = mock_now
        
        # Import the handler function
        from function_app import handle_debug_version_request
        
        # Create mock request
        req = MagicMock(spec=func.HttpRequest)
        
        # Call the handler function
        response = handle_debug_version_request(req)
        
        # Verify response
        assert response.status_code == 200
        assert response.mimetype == "application/json"
        
        # Parse response data
        response_data = json.loads(response.get_body().decode())
        assert response_data["service"] == "atomsec-func-sfdc"
        assert response_data["version"] == "2.0.0"
        assert "python_version" in response_data
        assert "platform" in response_data

    @patch('function_app.logger')
    def test_test_function_exception_handling(self, mock_logger):
        """Test that the test function handles exceptions properly"""
        # Mock logger to raise an exception
        mock_logger.info.side_effect = Exception("Test exception")
        
        # Import the handler function
        from function_app import handle_test_request
        
        # Create mock request
        req = MagicMock(spec=func.HttpRequest)
        
        # Call the handler function
        response = handle_test_request(req)
        
        # Verify error response
        assert response.status_code == 500
        assert response.mimetype == "application/json"
        
        # Parse response data
        response_data = json.loads(response.get_body().decode())
        assert response_data["status"] == "error"
        assert "error" in response_data

    def test_health_check_exception_handling(self):
        """Test that the health check handles exceptions properly"""
        # Import the handler function
        from function_app import handle_health_request
        
        # Create mock request
        req = MagicMock(spec=func.HttpRequest)
        
        # Call the handler function with patched datetime that raises exception
        with patch('function_app.datetime') as mock_datetime:
            # Make datetime.now fail to trigger exception handling
            mock_datetime.now.side_effect = Exception("DateTime error")
            response = handle_health_request(req)
        
        # Verify error response
        assert response.status_code == 503
        assert response.mimetype == "application/json"
        
        # Parse response data
        response_data = json.loads(response.get_body().decode())
        assert response_data["status"] == "unhealthy"
        assert "error" in response_data
