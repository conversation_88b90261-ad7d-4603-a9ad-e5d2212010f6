#!/usr/bin/env python3
"""
Test Runner for AtomSec SFDC Function App

This script provides an easy way to run all tests with proper configuration
and reporting.
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_command(command, description):
    """Run a command and return the result"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(command)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            capture_output=False,  # Let output go to console
            text=True,
            cwd=project_root
        )
        return result.returncode == 0
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def run_unit_tests():
    """Run unit tests"""
    return run_command([
        sys.executable, '-m', 'pytest', 
        'tests/test_function_app.py',
        'tests/unit/',
        '-v',
        '--tb=short'
    ], "Unit Tests")

def run_integration_tests():
    """Run integration tests"""
    return run_command([
        sys.executable, '-m', 'pytest', 
        'tests/test_integration.py',
        '-v',
        '--tb=short'
    ], "Integration Tests")

def run_deployment_tests():
    """Run deployment verification tests"""
    return run_command([
        sys.executable, '-m', 'pytest', 
        'tests/test_deployment_simple.py',
        'tests/test_deployment_verification.py',
        '-v',
        '--tb=short'
    ], "Deployment Tests")

def run_all_tests():
    """Run all tests"""
    return run_command([
        sys.executable, '-m', 'pytest', 
        'tests/',
        '-v',
        '--tb=short',
        '--ignore=tests/unit/api/',  # These may have import issues
        '--ignore=tests/unit/blueprints/',
        '--ignore=tests/unit/shared/'
    ], "All Tests")

def run_fast_tests():
    """Run only fast tests (exclude slow/deployment tests)"""
    return run_command([
        sys.executable, '-m', 'pytest', 
        'tests/',
        '-v',
        '--tb=short',
        '-m', 'not slow',
        '--ignore=tests/test_deployment_verification.py'
    ], "Fast Tests")

def check_test_environment():
    """Check if the test environment is properly set up"""
    print("\n" + "="*60)
    print("Checking Test Environment")
    print("="*60)
    
    # Check Python version
    print(f"Python version: {sys.version}")
    
    # Check if we're in the right directory
    if not (project_root / 'function_app.py').exists():
        print("❌ ERROR: Not in atomsec-func-sfdc directory or function_app.py missing")
        return False
    else:
        print("✅ Found function_app.py")
    
    # Check if pytest is available
    try:
        import pytest
        print(f"✅ pytest version: {pytest.__version__}")
    except ImportError:
        print("❌ ERROR: pytest not installed. Run: pip install pytest")
        return False
    
    # Check if tests directory exists
    tests_dir = project_root / 'tests'
    if not tests_dir.exists():
        print("❌ ERROR: tests directory not found")
        return False
    else:
        print(f"✅ Tests directory found: {tests_dir}")
    
    # List available test files
    test_files = list(tests_dir.glob('test_*.py'))
    print(f"✅ Found {len(test_files)} test files:")
    for test_file in test_files:
        print(f"   - {test_file.name}")
    
    return True

def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description='Run tests for AtomSec SFDC Function App')
    parser.add_argument(
        'test_type', 
        nargs='?',
        default='check',
        choices=['check', 'unit', 'integration', 'deployment', 'all', 'fast'],
        help='Type of tests to run'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Verbose output'
    )
    
    args = parser.parse_args()
    
    print("AtomSec SFDC Function App Test Runner")
    print("="*60)
    
    # Check environment first
    if not check_test_environment():
        print("\n❌ Environment check failed. Please fix the issues above.")
        sys.exit(1)
    
    if args.test_type == 'check':
        print("\n✅ Environment check passed. Use one of these commands to run tests:")
        print("  python tests/run_tests.py unit         # Run unit tests")
        print("  python tests/run_tests.py integration  # Run integration tests") 
        print("  python tests/run_tests.py deployment   # Run deployment tests")
        print("  python tests/run_tests.py all          # Run all tests")
        print("  python tests/run_tests.py fast         # Run fast tests only")
        return
    
    # Run the specified tests
    success = False
    if args.test_type == 'unit':
        success = run_unit_tests()
    elif args.test_type == 'integration':
        success = run_integration_tests()
    elif args.test_type == 'deployment':
        success = run_deployment_tests()
    elif args.test_type == 'all':
        success = run_all_tests()
    elif args.test_type == 'fast':
        success = run_fast_tests()
    
    # Report results
    print(f"\n{'='*60}")
    if success:
        print("✅ Tests completed successfully!")
    else:
        print("❌ Some tests failed. Check the output above for details.")
        sys.exit(1)

if __name__ == '__main__':
    main()
