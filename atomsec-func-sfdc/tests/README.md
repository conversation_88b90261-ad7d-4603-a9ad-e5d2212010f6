# AtomSec SFDC Function App - Test Suite

This directory contains the test suite for the AtomSec SFDC Function App. The tests have been updated to match the current codebase structure and remove outdated/invalid tests.

## Test Structure

### Core Test Files

- **`test_function_app.py`** - Unit tests for the main function app endpoints (test, health, info, task/test)
- **`test_integration.py`** - Integration tests that verify overall app functionality
- **`test_deployment_simple.py`** - Basic deployment verification tests for CI/CD pipeline
- **`test_deployment_verification.py`** - Comprehensive deployment verification tests
- **`test_dummy.py`** - Simple test that always passes (for CI pipeline stability)

### Unit Tests (by Module)

- **`unit/shared/test_config.py`** - Tests for configuration management
- **`unit/shared/test_common.py`** - Tests for common utility functions
- **`unit/api/test_cors_handler.py`** - Tests for CORS handling functionality
- **`unit/api/test_account_endpoints.py`** - Tests for account management endpoints

### Test Infrastructure

- **`conftest.py`** - pytest configuration and fixtures
- **`mock_azure_functions.py`** - Mock implementations for Azure Functions
- **`mock_table_storage.py`** - Mock table storage for testing
- **`make_requests.py`** - Utility for making HTTP requests during tests
- **`run_tests.py`** - Test runner script with environment checking

## Removed Invalid Tests

The following test files were removed as they were testing deprecated or non-existent functionality:

- `test_auth.py` - Tested functions that don't exist in current codebase
- `test_auth_fixed.py` - Duplicate and outdated auth tests
- `test_fastapi_integration.py` - All tests were skipped, FastAPI not actively used
- `test_my_work.py` - Contained only placeholder tests
- `test_WrapperFunction.py` - Tested deprecated WrapperFunction functionality
- `test_fastapi.py` - Tested deprecated FastAPI functionality
- `test_user_repository.py` - Database operations moved to atomsec-func-db-r service

## Running Tests

### Using the Test Runner (Recommended)

```bash
# Check test environment
python tests/run_tests.py check

# Run unit tests
python tests/run_tests.py unit

# Run integration tests
python tests/run_tests.py integration

# Run deployment tests
python tests/run_tests.py deployment

# Run all tests
python tests/run_tests.py all

# Run fast tests only (excludes slow/deployment tests)
python tests/run_tests.py fast
```

### Using pytest directly

```bash
# Run all tests
pytest tests/ -v

# Run specific test file
pytest tests/test_function_app.py -v

# Run tests with coverage
pytest tests/ --cov=. --cov-report=html

# Run tests excluding slow ones
pytest tests/ -m "not slow" -v
```

### Environment Variables

For deployment tests, set these environment variables:

```bash
export RUN_DEPLOYMENT_TESTS=true
export FUNCTION_APP_URL=https://your-function-app.azurewebsites.net
```

## Test Categories

### Unit Tests
- Test individual functions and modules in isolation
- Use mocks for external dependencies
- Fast execution
- Should run in any environment

### Integration Tests
- Test interaction between components
- Verify overall app functionality
- Test configuration and environment detection
- Mock external services but test internal integration

### Deployment Tests
- Verify deployed function app is working
- Test actual HTTP endpoints
- Require deployed environment
- May be slower due to network calls

## Current Test Coverage

### Tested Components
- ✅ Main function app endpoints (test, health, info, task/test)
- ✅ Configuration management (local dev vs production)
- ✅ Environment detection (is_local_dev, is_test_env)
- ✅ CORS handling functionality
- ✅ Account management endpoints (with mocks)
- ✅ Error handling and response formatting
- ✅ Blueprint registration and graceful failure handling

### Components That Need More Tests
- 🔄 Other API endpoints (auth, security, integration, etc.)
- 🔄 Shared utilities (salesforce_client, auth_utils, etc.)
- 🔄 Background task processing
- 🔄 Queue management functionality
- 🔄 PMD component functionality

## Test Development Guidelines

### Writing New Tests
1. Follow the existing naming convention: `test_<module_name>.py`
2. Use descriptive test method names: `test_<what_is_being_tested>`
3. Include docstrings explaining what the test verifies
4. Use appropriate mocking to isolate components under test
5. Handle import errors gracefully with `pytest.skip()`

### Test Structure
```python
"""
Unit tests for <module_name>

This module tests <description>
"""

import pytest
from unittest.mock import patch, MagicMock

class Test<ModuleName>:
    """Test cases for <module_name>"""
    
    def test_<specific_functionality>(self):
        """Test <what this test verifies>"""
        # Arrange
        # Act  
        # Assert
```

### Mocking Guidelines
- Mock external dependencies (database, HTTP requests, Azure services)
- Use `@patch` decorators for cleaner test code
- Create fixtures in `conftest.py` for commonly used mocks
- Mock at the appropriate level (function, class, or module)

## Continuous Integration

The test suite is designed to work in CI/CD environments:

- **Fast Tests**: Run on every commit/PR (unit tests + basic integration)
- **Full Test Suite**: Run on deployments (includes deployment verification)
- **Environment Detection**: Tests adapt based on environment variables
- **Graceful Failure**: Tests skip rather than fail when dependencies are missing

## Troubleshooting

### Common Issues

1. **Import Errors**: 
   - Tests are designed to skip when modules can't be imported
   - Check that you're running from the project root directory
   - Ensure all dependencies are installed

2. **Mock Issues**:
   - Verify mock paths match actual import paths
   - Use `pytest -s` to see print statements for debugging
   - Check that fixtures are properly configured in `conftest.py`

3. **Deployment Test Failures**:
   - Verify `FUNCTION_APP_URL` environment variable is set correctly
   - Ensure the function app is deployed and accessible
   - Check that endpoints in `test_deployment_verification.py` match actual deployment

### Getting Help

1. Check the test output for specific error messages
2. Run tests with `-v` for verbose output
3. Use `pytest -s` to see print statements
4. Check the `conftest.py` file for fixture configuration
5. Verify the function app is working by testing endpoints manually

## Future Improvements

- Add more comprehensive unit tests for all API endpoints
- Implement performance tests for critical endpoints
- Add contract tests for external API integrations
- Implement load testing for deployed environments
- Add security-focused tests for authentication and authorization
