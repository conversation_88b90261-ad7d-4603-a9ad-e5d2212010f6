# Core dependencies - pure V2 programming model
# azure-functions-worker is NOT needed for V2 programming model
# All functions are defined with @app.route decorators in function_app.py

# Core dependencies
azure-functions==1.17.0
requests==2.32.3
pandas==2.2.2
# REMOVED: pyodbc>=4.0.39  # Database operations moved to DB service
simple-salesforce>=1.12.6  # Salesforce API client

# Azure SDKs
azure-identity>=1.15.0
azure-keyvault-secrets>=4.7.0
azure-mgmt-keyvault>=10.2.3
azure-storage-blob>=12.19.0
azure-storage-queue>=12.8.0
azure-servicebus>=7.11.4
# REMOVED: azure-data-tables>=12.5.0  # Table operations moved to DB service
# REMOVED: azure-cosmos>=4.5.1  # Database operations moved to DB service
azure-functions-durable>=1.2.10 # For complex workflow orchestration

# Web frameworks
fastapi>=0.109.0
uvicorn>=0.27.0
jinja2>=3.1.3
httpx>=0.27.0  # Required for FastAPI testing

# Async support
aiohttp>=3.9.3
# Removed asyncio package as it's part of Python standard library

# Utilities
python-dateutil>=2.8.2
pytz>=2023.3
loguru>=0.7.2
PyYAML>=6.0
psutil>=5.9.8
azure-monitor-opentelemetry>=1.0.0

# Authentication and security
pyjwt>=2.8.0
cryptography>=41.0.5
passlib>=1.7.4
python-jose>=3.3.0

# Testing
hypothesis>=6.98.0
pytest>=7.4.4
pytest-asyncio>=0.23.5