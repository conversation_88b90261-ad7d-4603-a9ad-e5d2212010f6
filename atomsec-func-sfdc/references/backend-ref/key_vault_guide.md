# Azure Key Vault Integration Guide

This guide explains how to use the Azure Key Vault integration to securely store and retrieve client credentials and other secrets.

## Overview

The application provides a comprehensive set of tools for managing Azure Key Vault resources and secrets. This includes creating Key Vaults, managing access policies, and storing/retrieving secrets using the `azure-mgmt-keyvault` and `azure-keyvault-secrets` packages.

## Prerequisites

1. Azure subscription with access to Azure Key Vault
2. Proper permissions to access the Key Vault (Contributor or Key Vault Administrator role)
3. Azure CLI installed and configured (`az login`)
4. Valid authentication token (user must be logged in)

## Available Endpoints

### Key Vault API Endpoints

- `GET /api/key-vault/secrets`: List secrets metadata (names and properties; values are not returned)
- `POST /api/key-vault/create`: Create a new secret in Key Vault
- `POST /api/key-vault/access-policy`: Add an access policy to a Key Vault (stubbed response)
- `POST /api/key-vault/client-credentials`: Generate and store client credentials in Azure Key Vault

## Using the Endpoints

## Using the API Endpoints

### Create a Key Vault

```http
POST /api/key-vault/create
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "vault_name": "my-key-vault",
  "resource_group": "my-resource-group",
  "location": "eastus",
  "add_current_user": true
}
```

### Add an Access Policy

```http
POST /api/key-vault/access-policy
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "vault_name": "my-key-vault",
  "resource_group": "my-resource-group",
  "object_id": "00000000-0000-0000-0000-000000000000",
  "tenant_id": "00000000-0000-0000-0000-000000000000" (optional)
}
```

### List Secrets (metadata)

```http
GET /api/key-vault/secrets
Authorization: Bearer your-jwt-token
```

### Generate and Store Client Credentials

```http
POST /api/key-vault/client-credentials
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "client_name": "my-service",
  "service_type": "integration",
  "description": "optional"
}
```

The service generates a `client_id` and a secret, and stores the secret in Key Vault tagged with metadata. The secret value is only returned once in the response.

## Secret Naming Convention

When storing client credentials, the following naming convention is used:

- Client ID: `{service_name}-client-id`
- Client Secret: `{service_name}-client-secret`

For example, if you specify `service_name` as `azure-ad`, the secrets will be stored as:

- `azure-ad-client-id`
- `azure-ad-client-secret`

## Using Azure Key Vault in Code

The `shared.azure_services` module provides functions for working with Azure Key Vault:

### Key Vault Management

```python
from shared.azure_services import create_key_vault_if_not_exists, add_access_policy, get_current_object_id

# Create a Key Vault if it doesn't exist
vault_url = create_key_vault_if_not_exists(
    vault_name="my-key-vault",
    resource_group="my-resource-group",
    location="eastus"
)

# Get current user's object ID
object_id = get_current_object_id()

# Add an access policy
success = add_access_policy(
    vault_name="my-key-vault",
    resource_group="my-resource-group",
    object_id=object_id
)
```

### Retrieving Secrets

To retrieve secrets in your code, use the `get_secret` function:

```python
from shared.azure_services import get_secret

# Get client ID
client_id = get_secret("azure-ad-client-id")

# Get client secret
client_secret = get_secret("azure-ad-client-secret")
```

### Setting Secrets

Storing generic secrets via HTTP endpoint is not exposed. Use `azure.keyvault.secrets.SecretClient.set_secret` in deployment scripts or admin tools if needed.

### Storing Client Credentials

To store client credentials in your code, use the `store_client_credentials` function:

```python
from shared.azure_services import store_client_credentials

# Store client credentials
result = store_client_credentials(
    client_id="your-client-id",
    client_secret="your-client-secret",
    service_name="azure-ad",  # Optional, defaults to "default"
    vault_url=None  # Optional
)

if result.get('success', False):
    print("Client credentials stored successfully")
    print(f"Client ID secret name: {result.get('client_id_secret_name')}")
    print(f"Client Secret secret name: {result.get('client_secret_secret_name')}")
else:
    print(f"Failed to store client credentials: {result.get('message')}")
```

## Testing the Functionality

You can test the Azure Key Vault functionality using the provided test scripts:

### Basic Key Vault Operations

```bash
python test_key_vault.py
```

This script demonstrates how to set and get secrets using the Azure Key Vault integration.

### Key Vault Management Operations

```bash
python test_keyvault_mgmt.py
```

This script demonstrates how to create Key Vaults, manage access policies, and work with secrets using the enhanced functionality.

## Authentication

All Key Vault operations require authentication. The endpoints are protected and will only work for authenticated users with a valid JWT token.

### Web Form Authentication

No web UI is provided by this service for Key Vault operations.

### API Authentication

When making API calls, you must include a valid JWT token in the Authorization header:

```http
POST /api/key-vault/client-credentials
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "client_name": "my-service",
  "service_type": "integration"
}
```

You can obtain a JWT token by logging in through the application's authentication endpoints.

## Troubleshooting

### Authentication Issues

If you encounter authentication issues, make sure you are logged in to Azure CLI:

```bash
az login
```

For API authentication issues, ensure that:

1. You are logged in to the application
2. Your JWT token is valid and not expired
3. You are including the token in the Authorization header

### Permission Issues

If you encounter permission issues, make sure you have the necessary permissions to access the Key Vault:

1. Go to the Azure Portal
2. Navigate to your Key Vault
3. Go to "Access policies"
4. Add your user account with the necessary permissions (Get, List, Set, Delete)

### Local Development

For local development, make sure the following environment variables are set in your `local.settings.json` file:

```json
{
  "Values": {
    "USE_LOCAL_STORAGE": "true",
    "KEY_VAULT_URL": "https://akv-atomsec-dev.vault.azure.net/",
    "AZURE_SUBSCRIPTION_ID": "your-subscription-id",
    "AZURE_TENANT_ID": "your-tenant-id",
    "AZURE_RESOURCE_GROUP": "your-resource-group"
  }
}
```

## Security Considerations

1. **Access Control**: Always use the principle of least privilege when granting access to Key Vault.
2. **Secrets Rotation**: Regularly rotate client secrets and other credentials.
3. **Audit Logging**: Enable audit logging for Key Vault operations.
4. **Network Security**: Consider using Private Link or service endpoints to restrict network access to Key Vault.
5. **Purge Protection**: Enable purge protection to prevent accidental deletion of secrets.
