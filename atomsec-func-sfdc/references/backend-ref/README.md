# AtomSec Azure Functions Documentation

This directory contains comprehensive documentation for the AtomSec Azure Functions application.

## Available Documentation

### Development Guides

- [Local Development Guide](local_development_guide.md): Detailed instructions for setting up and running the application locally
- [Troubleshooting Guide](troubleshooting_guide.md): Solutions to common issues you might encounter

### Architecture and Design

- [Architecture Diagram](architecture_diagram.md): Visual representation of the application architecture
- [Function App Structure and Billing](function_app_structure_and_billing.md): Current Function App structure (minimal endpoints + optional blueprints) and Azure billing model

### Code Organization

- [Legacy Code Cleanup](legacy_code_cleanup.md): Information about the code cleanup process
- [Duplicate Files Cleanup](duplicate_files_cleanup.md): Explanation of the duplicate files cleanup
- [Restructure Plan](restructure_plan.md): Updated module layout (`api/`, `routers/`, `shared/`)

### Testing

- [Test Structure](test_structure.md): Documentation about the test structure and how to run tests

## Database Schema

- [Database Schema](chart.md): Entity-relationship diagram of the database schema

## How to Use This Documentation

- Start with the [Local Development Guide](local_development_guide.md) if you're setting up the project for the first time
- Refer to the [Troubleshooting Guide](troubleshooting_guide.md) if you encounter issues
- Check the [Architecture Diagram](architecture_diagram.md) to understand the overall structure
- Use the [Test Structure](test_structure.md) documentation when writing or running tests

## Contributing to Documentation

When adding new documentation:

1. Create a Markdown file in this directory
2. Add a link to the new file in this README
3. Reference the new documentation in the main project README if appropriate

## Additional Resources

- [Azure Functions Documentation](https://learn.microsoft.com/en-us/azure/azure-functions/)
- [Python Developer Guide for Azure Functions](https://learn.microsoft.com/en-us/azure/azure-functions/functions-reference-python)
- [Azure Storage Documentation](https://learn.microsoft.com/en-us/azure/storage/)
