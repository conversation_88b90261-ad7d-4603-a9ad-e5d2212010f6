# Azure Functions Backend Restructuring Plan

## New Folder Structure

```
atomsec-func-sfdc/
│
├── function_app.py              # Main entry point with function app definition
├── api/                         # Azure Functions Blueprints (HTTP endpoints)
│   ├── __init__.py
│   ├── integration_endpoints.py # Integration CRUD and data endpoints
│   ├── key_vault_endpoints.py   # Key Vault-related endpoints
│   ├── account_endpoints.py     # Accounts
│   ├── auth_endpoints.py        # Auth helpers
│   ├── policy_endpoints.py      # Policies
│   ├── security_endpoints.py    # Security/health data
│   └── ...                      # Other API modules
│
├── routers/                     # FastAPI routers (ASGI testing/local dev)
│   └── integration_router.py    # Integration endpoints via FastAPI
│
├── shared/                      # Shared code and utilities
│   ├── __init__.py              # Makes the directory a package
│   ├── azure_services.py        # Azure service connections
│   ├── data_access.py           # Data access repositories
│   └── utils.py                 # Utility functions
│
├── tests/                       # Test cases
│   ├── __init__.py
│   ├── test_profile_metadata.py
│   └── test_security_health_check.py
│
├── host.json                    # Host configuration
├── local.settings.json          # Local settings (not checked into source control)
├── requirements.txt             # Project dependencies
├── .funcignore                  # Files to exclude from deployment
└── README.md                    # Project documentation
```

## Implementation Steps

1. Create the new directory structure
2. Migrate functions to the blueprint pattern in `api/`
3. Update the main `function_app.py` to register blueprints defensively (graceful failures)
4. Centralize configuration in host.json
5. Update dependencies in requirements.txt
6. Test the restructured application

## Benefits

- **Improved Maintainability**: Clear separation of concerns with logical grouping
- **Better Performance**: Optimized configuration and optional FastAPI router for local ASGI testing
- **Enhanced Reliability**: Consistent error handling and logging
- **Easier Development**: Standardized approach across the codebase
- **Better Security**: Proper secret management and configuration
