"""
PMD Results Processor Module

This module handles processing and storing PMD scan results in the dedicated PMDScans table.
It includes comprehensive PMD-specific columns for detailed analysis.
"""

import logging
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

# Import from the parent directory's shared module
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from shared.db_service_client import get_db_client

logger = logging.getLogger(__name__)

class PMDResultsProcessor:
    """Processor for PMD scan results"""
    
    def __init__(self):
        """Initialize PMD results processor"""
        self.db_client = get_db_client()
    
    def _get_weakness_type(self, rule_name: str, category: str) -> Tuple[str, str]:
        """
        Map PMD rule names and categories to standardized weakness types
        
        Args:
            rule_name: PMD rule name
            category: PMD category
            
        Returns:
            Tuple of (weakness_type, description)
        """
        rule_name_lower = rule_name.lower()
        category_lower = category.lower()
        
        # Security-related rules
        if any(security_term in rule_name_lower for security_term in ['security', 'injection', 'xss', 'csrf', 'soql', 'sharing', 'crypto', 'redirect', 'endpoint']):
            return ('Security Vulnerability', 'Security vulnerabilities that could lead to unauthorized access or data breaches')
        elif 'performance' in category_lower or any(perf_term in rule_name_lower for perf_term in ['performance', 'debug', 'console', 'loop', 'query']):
            return ('Performance Issue', 'Performance issues that could impact system responsiveness or efficiency')
        elif 'documentation' in category_lower:
            return ('Development Best Practice', 'Documentation issues affecting code understanding and maintainability')
        else:
            return ('Code Quality', 'General code quality issues that should be addressed for better maintainability')  # Default fallback
    
    def _get_component_type(self, file_path: str, file_name: str) -> str:
        """
        Determine the Salesforce component type from file path and name
        
        Args:
            file_path: Full file path
            file_name: File name
            
        Returns:
            Component type string (Classes, Triggers, LWC, Aura, Pages, Components)
        """
        if not file_path:
            return 'Unknown'
        
        # Normalize path for consistent comparison
        normalized_path = file_path.lower().replace('\\', '/')
        
        # Check for component types based on path structure
        if '/classes/' in normalized_path or file_name.lower().endswith('.cls'):
            return 'Classes'
        elif '/triggers/' in normalized_path or file_name.lower().endswith('.trigger'):
            return 'Triggers'
        elif '/lwc/' in normalized_path:
            return 'LWC'
        elif '/aura/' in normalized_path:
            return 'Aura'
        elif '/pages/' in normalized_path or file_name.lower().endswith('.page'):
            return 'Pages'
        elif '/components/' in normalized_path or file_name.lower().endswith('.component'):
            return 'Components'
        elif '/staticresources/' in normalized_path:
            return 'Static Resources'
        elif '/layouts/' in normalized_path:
            return 'Layouts'
        elif '/objects/' in normalized_path:
            return 'Objects'
        elif '/profiles/' in normalized_path:
            return 'Profiles'
        elif '/permissionsets/' in normalized_path:
            return 'Permission Sets'
        else:
            # Try to infer from file extension
            if file_name.lower().endswith('.cls'):
                return 'Classes'
            elif file_name.lower().endswith('.trigger'):
                return 'Triggers'
            elif file_name.lower().endswith('.page'):
                return 'Pages'
            elif file_name.lower().endswith('.component'):
                return 'Components'
            elif file_name.lower().endswith('.js'):
                return 'JavaScript'
            elif file_name.lower().endswith('.html'):
                return 'HTML'
            elif file_name.lower().endswith('.xml'):
                return 'XML Metadata'
            else:
                return 'Other'
    
    def _get_language_from_component(self, component_type: str, file_name: str) -> str:
        """
        Determine the programming language based on the component type and file name.
        
        Args:
            component_type: The type of Salesforce component (e.g., 'Classes', 'Triggers', 'LWC', 'Aura', 'Pages', 'Components')
            file_name: The name of the file.
            
        Returns:
            A string indicating the programming language (e.g., 'Apex', 'JavaScript', 'HTML', 'XML').
        """
        if not file_name:
            return 'Unknown'

        file_name_lower = file_name.lower()

        if component_type == 'Classes':
            if file_name_lower.endswith('.cls'):
                return 'Apex'
            elif file_name_lower.endswith('.trigger'):
                return 'Apex'
            elif file_name_lower.endswith('.page'):
                return 'Apex'
            elif file_name_lower.endswith('.component'):
                return 'Apex'
            elif file_name_lower.endswith('.js'):
                return 'JavaScript'
            elif file_name_lower.endswith('.html'):
                return 'HTML'
            elif file_name_lower.endswith('.xml'):
                return 'XML'
            else:
                return 'Apex'
        elif component_type == 'Triggers':
            if file_name_lower.endswith('.trigger'):
                return 'Apex'
            elif file_name_lower.endswith('.page'):
                return 'Apex'
            elif file_name_lower.endswith('.component'):
                return 'Apex'
            elif file_name_lower.endswith('.js'):
                return 'JavaScript'
            elif file_name_lower.endswith('.html'):
                return 'HTML'
            elif file_name_lower.endswith('.xml'):
                return 'XML'
            else:
                return 'Apex'
        elif component_type == 'LWC':
            if file_name_lower.endswith('.js'):
                return 'JavaScript'
            elif file_name_lower.endswith('.html'):
                return 'HTML'
            elif file_name_lower.endswith('.xml'):
                return 'XML'
            else:
                return 'JavaScript'
        elif component_type == 'Aura':
            if file_name_lower.endswith('.js'):
                return 'JavaScript'
            elif file_name_lower.endswith('.html'):
                return 'HTML'
            elif file_name_lower.endswith('.xml'):
                return 'XML'
            else:
                return 'JavaScript'
        elif component_type == 'Pages':
            if file_name_lower.endswith('.page'):
                return 'Apex'
            elif file_name_lower.endswith('.component'):
                return 'Apex'
            elif file_name_lower.endswith('.js'):
                return 'JavaScript'
            elif file_name_lower.endswith('.html'):
                return 'HTML'
            elif file_name_lower.endswith('.xml'):
                return 'XML'
            else:
                return 'Apex'
        elif component_type == 'Components':
            if file_name_lower.endswith('.component'):
                return 'Apex'
            elif file_name_lower.endswith('.js'):
                return 'JavaScript'
            elif file_name_lower.endswith('.html'):
                return 'HTML'
            elif file_name_lower.endswith('.xml'):
                return 'XML'
            else:
                return 'Apex'
        elif component_type == 'Static Resources':
            if file_name_lower.endswith('.js'):
                return 'JavaScript'
            elif file_name_lower.endswith('.html'):
                return 'HTML'
            elif file_name_lower.endswith('.xml'):
                return 'XML'
            else:
                return 'JavaScript'
        elif component_type == 'Layouts':
            if file_name_lower.endswith('.html'):
                return 'HTML'
            elif file_name_lower.endswith('.xml'):
                return 'XML'
            else:
                return 'HTML'
        elif component_type == 'Objects':
            if file_name_lower.endswith('.cls'):
                return 'Apex'
            elif file_name_lower.endswith('.trigger'):
                return 'Apex'
            elif file_name_lower.endswith('.page'):
                return 'Apex'
            elif file_name_lower.endswith('.component'):
                return 'Apex'
            elif file_name_lower.endswith('.js'):
                return 'JavaScript'
            elif file_name_lower.endswith('.html'):
                return 'HTML'
            elif file_name_lower.endswith('.xml'):
                return 'XML'
            else:
                return 'Apex'
        elif component_type == 'Profiles':
            if file_name_lower.endswith('.cls'):
                return 'Apex'
            elif file_name_lower.endswith('.trigger'):
                return 'Apex'
            elif file_name_lower.endswith('.page'):
                return 'Apex'
            elif file_name_lower.endswith('.component'):
                return 'Apex'
            elif file_name_lower.endswith('.js'):
                return 'JavaScript'
            elif file_name_lower.endswith('.html'):
                return 'HTML'
            elif file_name_lower.endswith('.xml'):
                return 'XML'
            else:
                return 'Apex'
        elif component_type == 'Permission Sets':
            if file_name_lower.endswith('.cls'):
                return 'Apex'
            elif file_name_lower.endswith('.trigger'):
                return 'Apex'
            elif file_name_lower.endswith('.page'):
                return 'Apex'
            elif file_name_lower.endswith('.component'):
                return 'Apex'
            elif file_name_lower.endswith('.js'):
                return 'JavaScript'
            elif file_name_lower.endswith('.html'):
                return 'HTML'
            elif file_name_lower.endswith('.xml'):
                return 'XML'
            else:
                return 'Apex'
        else:
            return 'Unknown'

    def process_findings(self, 
                        findings: List[Dict[str, Any]], 
                        org_id: str, 
                        task_id: str,
                        execution_log_id: Optional[str] = None,
                        blob_prefix: Optional[str] = None) -> Dict[str, Any]:
        """
        Process PMD findings and store them in the PMDScans table
        
        Args:
            findings: List of PMD findings from scanner
            org_id: Organization ID
            task_id: Task ID
            execution_log_id: Optional execution log ID
            blob_prefix: Optional blob prefix for context
            
        Returns:
            Dictionary containing processing results
        """
        logger.info(f"Processing {len(findings)} PMD findings for org_id: {org_id}")
        
        processed_count = 0
        error_count = 0
        processed_findings = []
        
        for finding in findings:
            try:
                processed_finding = self._create_pmd_scan_entity(
                    finding, org_id, task_id, execution_log_id, blob_prefix
                )

                # Store PMD finding via DB service (maintaining microservice architecture)
                success = self.db_client.store_pmd_finding(org_id, processed_finding)
                if success:
                    processed_count += 1
                    processed_findings.append(processed_finding)
                else:
                    logger.error(f"Failed to store PMD finding via DB service: {processed_finding.get('RowKey', 'unknown')}")
                    error_count += 1
                
            except Exception as e:
                logger.error(f"Error processing finding: {e}")
                error_count += 1
                continue
        
        logger.info(f"Successfully processed {processed_count} findings, {error_count} errors")
        
        return {
            "processed_count": processed_count,
            "error_count": error_count,
            "total_findings": len(findings),
            "processed_findings": processed_findings
        }
    
    def _create_pmd_scan_entity(self, 
                               finding: Dict[str, Any], 
                               org_id: str, 
                               task_id: str,
                               execution_log_id: Optional[str] = None,
                               blob_prefix: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a PMD scan entity from PMD finding
        
        Args:
            finding: PMD finding dictionary
            org_id: Organization ID
            task_id: Task ID
            execution_log_id: Optional execution log ID
            blob_prefix: Optional blob prefix for context
            
        Returns:
            PMD scan entity dictionary
        """
        timestamp = datetime.now()
        timestamp_str = timestamp.strftime('%Y%m%d%H%M%S%f')
        
        # Create unique RowKey
        row_key = f"pmd-{timestamp_str}-{finding.get('file_name', 'unknown').replace('.cls', '')}"
        
        # Build policy name with category
        category = finding.get('category', 'Code Quality')
        policy_name = f'Static Code Analysis (PMD) - {category}'
        
        # Get weakness type
        rule_name = finding.get('rule_name', 'Unknown Rule')
        weakness_type, description = self._get_weakness_type(rule_name, category)
        
        # Get component type
        file_path = finding.get('file_path', '')
        file_name = finding.get('file_name', '')
        component_type = self._get_component_type(file_path, file_name)
        
        # Determine language based on component type and file extension
        language = self._get_language_from_component(component_type, file_name)
        
        # Create entity for PMDScans table
        entity = {
            # Standard table fields
            'PartitionKey': org_id,
            'RowKey': row_key,
            'TaskStatusId': execution_log_id or task_id,
            'IntegrationId': org_id,
            'TaskId': task_id,
            'ExecutionLogId': execution_log_id or task_id,
            'BlobPrefix': blob_prefix or '',
            
            # Scan metadata
            'ScanType': 'PMD',
            'Language': language,
            'Tool': 'PMD',
            
            # Policy information
            'PolicyName': policy_name,
            'RuleName': rule_name,
            'Status': finding.get('status', 'failed'),
            'Severity': finding.get('severity', 'Medium'),
            
            # File and location information
            'FileName': finding.get('file_name', 'Unknown'),
            'FilePath': finding.get('file_path', ''),
            'LineNumber': finding.get('line_number', ''),
            'Package': finding.get('package', ''),
            
            # Issue details
            'IssueType': rule_name,
            'IssueCategory': category,
            'IssueDescription': finding.get('description', ''),
            'IssuePriority': finding.get('priority', ''),
            
            # PMD-specific information
            'PMDRuleSet': finding.get('rule_set', ''),
            'PMDProblem': finding.get('problem', ''),
            'WeaknessType': weakness_type,
            'WeaknessTypeDescription': description,
            'ComponentType': component_type,
            
            # Metadata
            'CreatedAt': timestamp.isoformat()
        }
        
        return entity
    
    def get_findings_summary(self, findings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a summary of PMD findings
        
        Args:
            findings: List of PMD findings
            
        Returns:
            Dictionary containing findings summary
        """
        if not findings:
            return {
                "total_findings": 0,
                "severity_breakdown": {},
                "category_breakdown": {},
                "files_affected": 0,
                "unique_rules": 0
            }
        
        # Count by severity
        severity_counts = {}
        category_counts = {}
        files_affected = set()
        unique_rules = set()
        
        for finding in findings:
            # Severity breakdown
            severity = finding.get('severity', 'Unknown')
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # Category breakdown
            category = finding.get('category', 'Unknown')
            category_counts[category] = category_counts.get(category, 0) + 1
            
            # Files affected
            file_name = finding.get('file_name', 'Unknown')
            files_affected.add(file_name)
            
            # Unique rules
            rule_name = finding.get('rule_name', 'Unknown')
            unique_rules.add(rule_name)
        
        return {
            "total_findings": len(findings),
            "severity_breakdown": severity_counts,
            "category_breakdown": category_counts,
            "files_affected": len(files_affected),
            "unique_rules": len(unique_rules),
            "files_list": list(files_affected),
            "rules_list": list(unique_rules)
        } 