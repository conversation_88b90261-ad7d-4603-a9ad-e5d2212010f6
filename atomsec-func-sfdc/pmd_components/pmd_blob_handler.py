"""
PMD Blob Storage Handler Module

This module handles blob storage operations for PMD scanning of Salesforce components.
It now supports scanning all Salesforce component types: Apex classes, triggers, LWC, and Aura components.
"""

import logging
import os
import tempfile
from typing import Dict, Any, List, Optional, Tuple, Iterator
from datetime import datetime

# Import from the parent directory's shared module
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from shared.data_access import BlobStorageRepository

logger = logging.getLogger(__name__)

class PMDBlobHandler:
    """Handler for blob storage operations in PMD scanning"""
    
    def __init__(self, container_name: str = "salesforce-metadata"):
        """
        Initialize PMD blob handler
        
        Args:
            container_name: Azure blob storage container name
        """
        self.container_name = container_name
        self.blob_repo = BlobStorageRepository(container_name=container_name)
        
        # Define Salesforce component types and their file extensions
        # Only include core development components that should be scanned by PMD
        self.component_types = {
            "classes": {
                "path": "classes",
                "extensions": [".cls"],
                "pmd_language": "apex"
            },
            "triggers": {
                "path": "triggers",
                "extensions": [".trigger"],
                "pmd_language": "apex"
            },
            "aura": {
                "path": "aura",
                "extensions": [".js", ".html", ".css", ".xml"],
                "pmd_language": "javascript"
            },
            "components": {
                "path": "components",
                "extensions": [".js", ".html", ".css", ".xml"],
                "pmd_language": "javascript"
            },
            "lwc": {
                "path": "lwc",
                "extensions": [".js", ".html", ".css", ".xml"],
                "pmd_language": "javascript"
            },
            "pages": {
                "path": "pages",
                "extensions": [".page"],
                "pmd_language": "visualforce"
            }
        }
    
    def get_all_salesforce_components_from_blob(self, blob_prefix: str) -> Dict[str, List[str]]:
        """
        Get only core Salesforce development components from blob storage
        
        This method ONLY looks for the specific component types we want to scan:
        - classes/ (Apex classes)
        - triggers/ (Apex triggers) 
        - aura/ (Aura components)
        - components/ (LWC and other components)
        - pages/ (Visualforce pages)
        
        It completely ignores all other Salesforce metadata types.
        
        Args:
            blob_prefix: Blob storage prefix path
            
        Returns:
            Dictionary mapping component types to lists of blob names
        """
        all_components = {}
        
        # Only scan the specific component directories we care about
        for component_type, config in self.component_types.items():
            # Handle the case where blob_prefix might already end with a slash
            if blob_prefix.endswith('/'):
                component_path = f"{blob_prefix}{config['path']}"
            else:
                component_path = f"{blob_prefix}/{config['path']}"
                
            try:
                blob_list = self.blob_repo.list_blobs(name_starts_with=component_path)
                
                # Filter for files with the specified extensions
                component_files = []
                for blob_name in blob_list:
                    if any(blob_name.endswith(ext) for ext in config['extensions']):
                        component_files.append(blob_name)
                
                all_components[component_type] = component_files
                logger.info(f"Found {len(component_files)} {component_type} in blob storage at {component_path}")
                
            except Exception as e:
                logger.warning(f"Error accessing {component_type} at {component_path}: {e}")
                all_components[component_type] = []
        
        # DO NOT scan for embedded components - we only want the specific directories
        # This prevents scanning through all blob content for any file references
        
        return all_components
    
    def iterate_all_salesforce_components(self, blob_prefix: str) -> Iterator[Tuple[str, bytes, str, str]]:
        """
        Iterate through core Salesforce development components from blob storage
        
        This method ONLY processes components from the specific directories we care about.
        It completely ignores all other Salesforce metadata types.
        
        Args:
            blob_prefix: Blob storage prefix path
            
        Yields:
            Tuple of (file_name, file_content_bytes, component_type, pmd_language)
        """
        all_components = self.get_all_salesforce_components_from_blob(blob_prefix)
        
        for component_type, blob_names in all_components.items():
            config = self.component_types[component_type]
            
            for blob_name in blob_names:
                try:
                    # Get the actual blob content
                    file_content = self.blob_repo.get_blob_bytes(blob_name)
                    if file_content:
                        file_name = os.path.basename(blob_name)
                        yield file_name, file_content, component_type, config['pmd_language']
                    else:
                        logger.warning(f"Failed to get content for blob: {blob_name}")
                        
                except Exception as e:
                    logger.error(f"Error processing blob {blob_name}: {e}")
                    continue
    
    def get_apex_classes_from_blob(self, blob_prefix: str) -> List[str]:
        """
        Get Apex classes from blob storage (maintained for backward compatibility)
        
        Args:
            blob_prefix: Blob storage prefix path
            
        Returns:
            List of blob names for Apex classes
        """
        components = self.get_all_salesforce_components_from_blob(blob_prefix)
        return components.get("classes", [])
    
    def iterate_apex_classes(self, blob_prefix: str) -> Iterator[Tuple[str, bytes]]:
        """
        Iterate through Apex classes from blob storage (maintained for backward compatibility)
        
        Args:
            blob_prefix: Blob storage prefix path
            
        Yields:
            Tuple of (file_name, file_content_bytes)
        """
        for file_name, file_content, component_type, pmd_language in self.iterate_all_salesforce_components(blob_prefix):
            if component_type == "classes":
                yield file_name, file_content
    
    def create_temp_scan_directory(self, blob_prefix: str, temp_dir: Optional[str] = None) -> Tuple[str, Dict[str, int]]:
        """
        Create a temporary directory with all Salesforce components for scanning
        
        Args:
            blob_prefix: Blob storage prefix path
            temp_dir: Optional temporary directory path
            
        Returns:
            Tuple of (temp_directory_path, component_counts_dict)
        """
        if temp_dir:
            os.makedirs(temp_dir, exist_ok=True)
            temp_path = temp_dir
        else:
            temp_path = tempfile.mkdtemp(prefix="pmd_scan_")
        
        component_counts = {component_type: 0 for component_type in self.component_types.keys()}
        
        for file_name, file_content, component_type, pmd_language in self.iterate_all_salesforce_components(blob_prefix):
            try:
                # Preserve the subfolder structure by extracting the relative path from blob_name
                # Get the blob name that contains the full path
                blob_name = None
                for blob_path in self.get_all_salesforce_components_from_blob(blob_prefix).get(component_type, []):
                    if os.path.basename(blob_path) == file_name:
                        blob_name = blob_path
                        break
                
                if blob_name:
                    # Extract the relative path from the blob prefix
                    if blob_name.startswith(blob_prefix):
                        relative_path = blob_name[len(blob_prefix):].lstrip('/')
                        # Create the full file path preserving subfolder structure
                        file_path = os.path.join(temp_path, relative_path)
                        # Ensure the directory exists
                        os.makedirs(os.path.dirname(file_path), exist_ok=True)
                    else:
                        # Fallback to root if path extraction fails
                        file_path = os.path.join(temp_path, file_name)
                else:
                    # Fallback to root if blob name not found
                    file_path = os.path.join(temp_path, file_name)
                
                with open(file_path, "wb") as f:
                    f.write(file_content)
                component_counts[component_type] += 1
                logger.debug(f"Created temporary file: {file_path} (type: {component_type})")
            except Exception as e:
                logger.error(f"Error creating temporary file {file_name}: {e}")
                continue
        
        total_files = sum(component_counts.values())
        logger.info(f"Created {total_files} temporary files in {temp_path}")
        for component_type, count in component_counts.items():
            if count > 0:
                logger.info(f"  - {component_type}: {count} files")
        
        return temp_path, component_counts
    
    def scan_all_salesforce_components_in_blob(self, 
                                             blob_prefix: str, 
                                             scanner, 
                                             output_file: str = None,  # Made optional since we don't use it anymore
                                             categories: Optional[List[str]] = None,  # Kept for compatibility but not used
                                             temp_dir: Optional[str] = None,
                                             custom_ruleset_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Scan all Salesforce components directly from blob storage
        
        Args:
            blob_prefix: Blob storage prefix path
            scanner: PMD scanner instance
            output_file: Path to output CSV file (optional, kept for compatibility)
            categories: Optional list of rule categories to include (optional, kept for compatibility)
            temp_dir: Optional temporary directory for files
            custom_ruleset_file: Optional path to custom ruleset file
            
        Returns:
            Dictionary containing scan results and metadata
        """
        logger.info(f"Starting PMD scan of all Salesforce components from blob prefix: {blob_prefix}")
        
        # Get component counts first
        all_components = self.get_all_salesforce_components_from_blob(blob_prefix)
        total_files = sum(len(files) for files in all_components.values())
        
        if total_files == 0:
            logger.warning(f"No Salesforce components found in blob storage at {blob_prefix}")
            return {
                "success": True,
                "findings_count": 0,
                "files_scanned": 0,
                "message": "No Salesforce components found to scan",
                "component_counts": all_components
            }
        
        # Create temporary directory with all components
        temp_path, component_counts = self.create_temp_scan_directory(blob_prefix, temp_dir)
        
        try:
            # Run PMD scan on the temporary directory using new interface
            if custom_ruleset_file and os.path.exists(custom_ruleset_file):
                logger.info(f"Using custom ruleset file: {custom_ruleset_file}")
                # For custom ruleset, we need to handle differently since scan_directory doesn't support it
                logger.warning("Custom ruleset support not yet implemented in new scanner interface")
                scan_result = scanner.scan_directory(
                    source_dir=temp_path,
                    temp_dir=temp_dir
                )
            else:
                # Use new scanner interface - no categories parameter, no output_file
                scan_result = scanner.scan_directory(
                    source_dir=temp_path,
                    temp_dir=temp_dir
                )
            
            # Add blob-specific metadata
            scan_result.update({
                "blob_prefix": blob_prefix,
                "temp_directory": temp_path,
                "component_counts": component_counts,
                "total_components_found": total_files,
                "custom_ruleset_used": custom_ruleset_file is not None and os.path.exists(custom_ruleset_file)
            })
            
            return scan_result
            
        except Exception as e:
            logger.error(f"Error during blob-based PMD scan: {e}")
            return {
                "success": False,
                "error": str(e),
                "findings_count": 0,
                "blob_prefix": blob_prefix,
                "temp_directory": temp_path,
                "component_counts": component_counts
            }
        finally:
            # Clean up temporary directory if we created it
            if not temp_dir and os.path.exists(temp_path):
                try:
                    import shutil
                    shutil.rmtree(temp_path)
                    logger.debug(f"Cleaned up temporary directory: {temp_path}")
                except Exception as e:
                    logger.warning(f"Failed to clean up temporary directory {temp_path}: {e}")
    
    def scan_classes_in_blob(self, 
                            blob_prefix: str, 
                            scanner, 
                            output_file: str,
                            categories: Optional[List[str]] = None,
                            temp_dir: Optional[str] = None,
                            custom_ruleset_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Scan Apex classes directly from blob storage (maintained for backward compatibility)
        
        Args:
            blob_prefix: Blob storage prefix path
            scanner: PMD scanner instance
            output_file: Path to output CSV file
            categories: Optional list of rule categories to include
            temp_dir: Optional temporary directory for files
            custom_ruleset_file: Optional path to custom ruleset file
            
        Returns:
            Dictionary containing scan results and metadata
        """
        logger.info(f"Starting PMD scan of Apex classes from blob prefix: {blob_prefix}")
        
        # Get class count first
        apex_classes = self.get_apex_classes_from_blob(blob_prefix)
        if not apex_classes:
            logger.warning(f"No Apex classes found in blob storage at {blob_prefix}/classes/")
            return {
                "success": True,
                "findings_count": 0,
                "files_scanned": 0,
                "message": "No Apex classes found to scan"
            }
        
        # Create temporary directory with classes only
        temp_path, component_counts = self.create_temp_scan_directory(blob_prefix, temp_dir)
        
        try:
            # Run PMD scan on the temporary directory
            if custom_ruleset_file and os.path.exists(custom_ruleset_file):
                logger.info(f"Using custom ruleset file: {custom_ruleset_file}")
                scan_result = scanner.scan_directory_with_ruleset(
                    source_dir=temp_path,
                    output_file=output_file,
                    ruleset_file=custom_ruleset_file
                )
            else:
                scan_result = scanner.scan_directory(
                    source_dir=temp_path,
                    output_file=output_file,
                    categories=categories
                )
            
            # Add blob-specific metadata
            scan_result.update({
                "blob_prefix": blob_prefix,
                "temp_directory": temp_path,
                "files_created": component_counts.get("classes", 0),
                "total_classes_found": len(apex_classes),
                "custom_ruleset_used": custom_ruleset_file is not None and os.path.exists(custom_ruleset_file)
            })
            
            return scan_result
            
        except Exception as e:
            logger.error(f"Error during blob-based PMD scan: {e}")
            return {
                "success": False,
                "error": str(e),
                "findings_count": 0,
                "blob_prefix": blob_prefix,
                "temp_directory": temp_path
            }
        finally:
            # Clean up temporary directory if we created it
            if not temp_dir and os.path.exists(temp_path):
                try:
                    import shutil
                    shutil.rmtree(temp_path)
                    logger.debug(f"Cleaned up temporary directory: {temp_path}")
                except Exception as e:
                    logger.warning(f"Failed to clean up temporary directory {temp_path}: {e}")
    
    def upload_scan_results(self, 
                           results_file: str, 
                           blob_prefix: str,
                           results_type: str = "pmd_results") -> Optional[str]:
        """
        Upload PMD scan results to blob storage
        
        Args:
            results_file: Path to results file to upload
            blob_prefix: Blob storage prefix path
            results_type: Type of results (e.g., "pmd_results", "findings")
            
        Returns:
            Blob URL of uploaded file or None if upload failed
        """
        if not os.path.exists(results_file):
            logger.error(f"Results file not found: {results_file}")
            return None
        
        try:
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            blob_name = f"{blob_prefix}/{results_type}/pmd_scan_results_{timestamp}.csv"
            
            with open(results_file, 'rb') as f:
                self.blob_repo.upload_blob(blob_name, f.read())
            
            blob_url = self.blob_repo.get_blob_url(blob_name)
            logger.info(f"Uploaded scan results to blob storage: {blob_url}")
            
            return blob_url
            
        except Exception as e:
            logger.error(f"Error uploading scan results: {e}")
            return None
    
    def get_blob_info(self, blob_prefix: str) -> Dict[str, Any]:
        """
        Get information about Apex classes in blob storage
        
        Args:
            blob_prefix: Blob storage prefix path
            
        Returns:
            Dictionary containing blob information
        """
        apex_classes = self.get_apex_classes_from_blob(blob_prefix)
        
        # Calculate total size
        total_size = 0
        for blob_name in apex_classes:
            try:
                content = self.blob_repo.get_blob_bytes(blob_name)
                if content:
                    total_size += len(content)
            except Exception as e:
                logger.warning(f"Error getting size for blob {blob_name}: {e}")
        
        return {
            "blob_prefix": blob_prefix,
            "classes_count": len(apex_classes),
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "classes": [os.path.basename(cls) for cls in apex_classes]
        } 