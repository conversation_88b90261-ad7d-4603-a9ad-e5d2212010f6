"""
PMD Scanner Module

This module handles PMD scanning operations for Salesforce components analysis.
It now supports Apex (classes/triggers), LWC (JavaScript/HTML/CSS), and Aura components.
It includes environment detection, command execution, and result parsing.
Results are processed and stored in the PMDScans database table.
"""

import logging
import os
import platform
import subprocess
import tempfile
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from .pmd_rules_config import PMDRulesConfig

logger = logging.getLogger(__name__)

class PMDScanner:
    """PMD scanner for Salesforce components analysis"""
    
    def __init__(self, custom_rules_dir: Optional[str] = None):
        """
        Initialize PMD scanner
        
        Args:
            custom_rules_dir: Directory containing custom PMD rules (optional)
        """
        self.rules_config = PMDRulesConfig(custom_rules_dir)
        self.is_local = self._is_running_locally()
    
    def _is_running_locally(self) -> bool:
        """Determine if the code is running locally or in Azure"""
        if os.environ.get('WEBSITE_INSTANCE_ID'):
            return False
        return True
    
    def _get_pmd_command(self) -> List[str]:
        """Get the appropriate PMD command based on environment"""
        if self.is_local:
            # Local environment - use installed PMD
            if platform.system() == "Windows":
                cmd = ["pmd.bat"]
            else:
                cmd = ["pmd"]
            
            logger.info("Using local PMD installation")
            return cmd
        else:
            # Production Azure environment - use PMD installed in the function app
            logger.info("Using Azure-deployed PMD installation")
            return ["/opt/pmd/bin/run.sh", "pmd"]
    
    def _count_files_by_extension(self, source_dir: str, extensions: List[str]) -> int:
        """Count files with specified extensions in a directory"""
        count = 0
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                if any(file.endswith(ext) for ext in extensions):
                    count += 1
        return count
    
    def _get_language_specific_rulesets(self, source_dir: str) -> str:
        """
        Determine and combine rulesets based on detected file types
        
        Args:
            source_dir: Directory containing files to scan
            
        Returns:
            Comma-separated string of rulesets to use
        """
        # Count files by type - only core Salesforce development components
        apex_files = self._count_files_by_extension(source_dir, [".cls", ".trigger"])
        js_files = self._count_files_by_extension(source_dir, [".js"])
        html_files = self._count_files_by_extension(source_dir, [".html"])
        css_files = self._count_files_by_extension(source_dir, [".css"])
        page_files = self._count_files_by_extension(source_dir, [".page"])
        xml_files = self._count_files_by_extension(source_dir, [".xml"])
        
        logger.info(f"File counts - Apex: {apex_files}, JavaScript: {js_files}, HTML: {html_files}, CSS: {css_files}, Visualforce: {page_files}, XML: {xml_files}")
        
        # Build ruleset string based on file types found
        rulesets = []
        
        if apex_files > 0:
            # Add Apex rulesets for classes and triggers
            apex_rulesets = self.rules_config.get_rulesets_by_language("apex")
            rulesets.extend(apex_rulesets)
            logger.info(f"Added {len(apex_rulesets)} Apex rulesets")
        
        if js_files > 0:
            # Add JavaScript rulesets for LWC and Aura JS files
            # PMD detects .js files as 'ecmascript' language, but we use 'javascript' rulesets
            js_rulesets = self.rules_config.get_rulesets_by_language("javascript")
            rulesets.extend(js_rulesets)
            logger.info(f"Added {len(js_rulesets)} JavaScript rulesets for .js files")
        
        if html_files > 0:
            # Add HTML rulesets for LWC and Aura HTML files
            html_rulesets = self.rules_config.get_rulesets_by_language("html")
            rulesets.extend(html_rulesets)
            logger.info(f"Added {len(html_rulesets)} HTML rulesets for .html files")
        
        if css_files > 0:
            # Add CSS rulesets for LWC and Aura CSS files
            css_rulesets = self.rules_config.get_rulesets_by_language("css")
            rulesets.extend(css_rulesets)
            logger.info(f"Added {len(css_rulesets)} CSS rulesets for .css files")
        
        if page_files > 0:
            # Add XML rulesets for Visualforce pages (they're XML-based)
            # PMD detects .page files as 'visualforce' language, but we use 'xml' rulesets
            xml_rulesets = self.rules_config.get_rulesets_by_language("xml")
            rulesets.extend(xml_rulesets)
            logger.info(f"Added {len(xml_rulesets)} XML rulesets for Visualforce .page files")
        
        if xml_files > 0 and page_files == 0:
            # Add XML rulesets for other XML files (but not if we already added them for pages)
            xml_rulesets = self.rules_config.get_rulesets_by_language("xml")
            rulesets.extend(xml_rulesets)
            logger.info(f"Added {len(xml_rulesets)} XML rulesets for other .xml files")
        
        if not rulesets:
            logger.warning("No rulesets found for any file types")
            return ""
        
        # Remove duplicates and join
        unique_rulesets = list(dict.fromkeys(rulesets))
        ruleset_string = ",".join(unique_rulesets)
        
        logger.info(f"Using combined rulesets: {ruleset_string}")
        return ruleset_string
    
    def get_scanner_info(self) -> Dict[str, Any]:
        """
        Get information about the PMD scanner
        
        Returns:
            Dictionary containing scanner information
        """
        return {
            "environment": "local" if self.is_local else "azure",
            "pmd_command": self._get_pmd_command(),
            "rules_config_info": self.rules_config.get_ruleset_info(),
            "supports_languages": ["apex", "ecmascript", "html", "visualforce", "xml"],
            "component_types_supported": ["classes", "triggers", "lwc", "aura", "pages"],
            "output_format": "database",  # Results are stored in PMDScans table
            "csv_generation": False  # No longer generates CSV files
        }
    
    def scan_directory(self, 
                      source_dir: str, 
                      output_file: str = None,
                      temp_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Scan a directory of Salesforce components using PMD
        
        Args:
            source_dir: Directory containing Salesforce components to scan
            output_file: Optional path to output file (kept for compatibility, not used)
            temp_dir: Optional temporary directory for PMD output
        
        Returns:
            Dictionary containing scan results and findings
        """
        logger.info(f"Starting PMD scan of directory: {source_dir}")
        
        # Ensure source directory exists
        if not os.path.exists(source_dir):
            error_msg = f"Source directory does not exist: {source_dir}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
        
        # Count total files by type - only core Salesforce development components
        total_files = 0
        file_counts = {}
        
        for file_type, extensions in [
            ("apex", [".cls", ".trigger"]),
            ("javascript", [".js"]),
            ("html", [".html"]),
            ("css", [".css"]),
            ("visualforce", [".page", ".component"]),  # Added .component files
            ("xml", [".xml"])
        ]:
            count = self._count_files_by_extension(source_dir, extensions)
            file_counts[file_type] = count
            total_files += count
            logger.info(f"File counts - {file_type}: {count}")
        
        if total_files == 0:
            logger.warning(f"No Salesforce component files found in directory: {source_dir}")
            return {
                "success": True,
                "findings_count": 0,
                "files_scanned": 0,
                "message": "No Salesforce component files found to scan",
                "file_counts": file_counts
            }
        
        # Run PMD scan for each file type separately to ensure proper language detection
        all_findings = []
        total_findings = 0
        
        # Scan Apex files (classes and triggers)
        apex_files = self._count_files_by_extension(source_dir, [".cls", ".trigger"])
        if apex_files > 0:
            logger.info(f"Scanning {apex_files} Apex files (classes and triggers)")
            apex_results = self._run_pmd_for_language(source_dir, "apex", temp_dir)
            if apex_results["success"]:
                all_findings.extend(apex_results.get("findings", []))
                total_findings += apex_results.get("findings_count", 0)
                logger.info(f"Apex scan completed: {apex_results.get('findings_count', 0)} findings")
            else:
                logger.warning(f"Apex scan failed: {apex_results.get('error', 'Unknown error')}")
        
        # Scan JavaScript files
        js_files = self._count_files_by_extension(source_dir, [".js"])
        if js_files > 0:
            logger.info(f"Scanning {js_files} JavaScript files")
            # PMD detects .js files as 'ecmascript' language, not 'javascript'
            # Use more comprehensive ecmascript rulesets for better analysis
            js_results = self._run_pmd_for_language(source_dir, "ecmascript", temp_dir)
            if js_results["success"]:
                all_findings.extend(js_results.get("findings", []))
                total_findings += js_results.get("findings_count", 0)
                logger.info(f"JavaScript scan completed: {js_results.get('findings_count', 0)} findings")
            else:
                logger.warning(f"JavaScript scan failed: {js_results.get('error', 'Unknown error')}")
        
        # Scan HTML files
        html_files = self._count_files_by_extension(source_dir, [".html"])
        if html_files > 0:
            logger.info(f"Scanning {html_files} HTML files")
            html_results = self._run_pmd_for_language(source_dir, "html", temp_dir)
            if html_results["success"]:
                all_findings.extend(html_results.get("findings", []))
                total_findings += html_results.get("findings_count", 0)
                logger.info(f"HTML scan completed: {html_results.get('findings_count', 0)} findings")
            else:
                logger.warning(f"HTML scan failed: {html_results.get('error', 'Unknown error')}")
        
        # Scan CSS files - PMD doesn't natively support CSS, so we'll skip them
        css_files = self._count_files_by_extension(source_dir, [".css"])
        if css_files > 0:
            logger.info(f"Found {css_files} CSS files, but PMD doesn't natively support CSS analysis")
            logger.info("CSS files will be skipped as they're not supported by PMD")
        
        # Scan Visualforce pages and components
        page_files = self._count_files_by_extension(source_dir, [".page"])
        component_files = self._count_files_by_extension(source_dir, [".component"])
        visualforce_total = page_files + component_files
        
        if visualforce_total > 0:
            logger.info(f"Scanning {visualforce_total} Visualforce files ({page_files} pages, {component_files} components)")
            # Use Visualforce rulesets for both .page and .component files
            vf_results = self._run_pmd_for_language(source_dir, "visualforce", temp_dir)
            if vf_results["success"]:
                all_findings.extend(vf_results.get("findings", []))
                total_findings += vf_results.get("findings_count", 0)
                logger.info(f"Visualforce scan completed: {vf_results.get('findings_count', 0)} findings")
            else:
                logger.warning(f"Visualforce scan failed: {vf_results.get('error', 'Unknown error')}")
        
        # Scan other XML files (metadata files)
        xml_files = self._count_files_by_extension(source_dir, [".xml"])
        if xml_files > 0:
            logger.info(f"Scanning {xml_files} XML metadata files")
            xml_results = self._run_pmd_for_language(source_dir, "xml", temp_dir)
            if xml_results["success"]:
                all_findings.extend(xml_results.get("findings", []))
                total_findings += xml_results.get("findings_count", 0)
                logger.info(f"XML metadata scan completed: {xml_results.get('findings_count', 0)} findings")
            else:
                logger.warning(f"XML metadata scan failed: {xml_results.get('error', 'Unknown error')}")
        
        # Return comprehensive scan results
        scan_result = {
            "success": True,
            "findings": all_findings,
            "findings_count": total_findings,
            "files_scanned": total_files,
            "file_counts": file_counts,
            "scan_timestamp": datetime.now().isoformat(),
            "message": f"PMD scan completed successfully. Found {total_findings} findings across {total_files} files."
        }
        
        logger.info(f"PMD scan completed successfully. Total findings: {total_findings}")
        return scan_result
    
    def _run_pmd_for_language(self, source_dir: str, language: str, temp_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Run PMD scan for a specific language
        
        Args:
            source_dir: Directory containing files to scan
            language: Language to scan for
            temp_dir: Optional temporary directory for PMD output
            
        Returns:
            Dictionary containing scan results
        """
        # Get rulesets for the specific language
        if language == "visualforce":
            # For Visualforce pages and components, use Visualforce rulesets
            rulesets = self.rules_config.get_rulesets_by_language("visualforce")
        elif language == "xml":
            # For metadata XML files, use XML rulesets
            rulesets = self.rules_config.get_rulesets_by_language("xml")
        elif language == "ecmascript":
            # PMD uses 'ecmascript' for JavaScript files
            # Use comprehensive rulesets for better analysis
            rulesets = self.rules_config.get_rulesets_by_language("ecmascript")
        else:
            rulesets = self.rules_config.get_rulesets_by_language(language)
        
        if not rulesets:
            logger.warning(f"No rulesets found for language: {language}")
            return {
                "success": False,
                "error": f"No rulesets found for language: {language}",
                "findings_count": 0
            }
        
        # Create temporary output file for this language
        if temp_dir:
            output_file = os.path.join(temp_dir, f"pmd_{language}_output.csv")
        else:
            output_file = tempfile.mktemp(suffix=f"_pmd_{language}.csv")
        
        # Build PMD command
        base_cmd = self._get_pmd_command()
        pmd_command = base_cmd + [
            'check',
            '-d', source_dir,
            '-R', ','.join(rulesets),
            '-f', 'csv',
            '--report-file', output_file,
            '--debug',
            '--no-cache'
        ]
        
        # Log command for debugging
        env_type = "local" if self.is_local else "Azure"
        logger.info(f"Running in {env_type} environment")
        logger.info(f"Running PMD for {language} with command: {' '.join(pmd_command)}")
        
        # Execute PMD
        try:
            result = subprocess.run(
                pmd_command, 
                capture_output=True, 
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            # PMD exit codes: 0 = no violations, 4 = violations found, 5 = processing errors
            if result.returncode in (0, 4, 5):
                findings = self._parse_pmd_results(output_file)
                findings_count = len(findings)
                
                if result.returncode == 5:
                    logger.warning(f"PMD {language} scan completed with errors (code 5). Parsed {findings_count} findings from report")
                else:
                    logger.info(f"PMD {language} scan completed (code {result.returncode}): {findings_count} findings")
                
                return {
                    "success": True,
                    "findings": findings,
                    "findings_count": findings_count,
                    "output_file": output_file,
                    "pmd_return_code": result.returncode
                }
            else:
                # Treat other codes as failure
                error_msg = f"PMD {language} scan failed with return code {result.returncode}"
                if result.stderr:
                    error_msg += f". Error: {result.stderr.strip()}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "findings_count": 0,
                    "pmd_return_code": result.returncode
            }
            
        except subprocess.TimeoutExpired:
            error_msg = f"PMD {language} scan timed out after 5 minutes"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
        except Exception as e:
            error_msg = f"PMD {language} scan failed with exception: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
    
    def scan_directory_with_ruleset(self, 
                                   source_dir: str, 
                                   output_file: str,
                                   ruleset_file: str,
                                   temp_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Scan a directory of Apex classes using PMD with a custom ruleset file
        
        Args:
            source_dir: Directory containing Apex classes to scan
            output_file: Path to output CSV file
            ruleset_file: Path to custom ruleset XML file
            temp_dir: Optional temporary directory for PMD output
        
        Returns:
            Dictionary containing scan results and metadata
        """
        logger.info(f"Starting PMD scan of directory: {source_dir} with custom ruleset: {ruleset_file}")
        
        # Ensure source directory exists
        if not os.path.exists(source_dir):
            error_msg = f"Source directory does not exist: {source_dir}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
        
        # Ensure ruleset file exists
        if not os.path.exists(ruleset_file):
            error_msg = f"Custom ruleset file does not exist: {ruleset_file}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
        
        # Count Apex files
        apex_files = self._count_apex_files(source_dir)
        if apex_files == 0:
            logger.warning(f"No Apex files found in directory: {source_dir}")
            return {
                "success": True,
                "findings_count": 0,
                "files_scanned": 0,
                "message": "No Apex files found to scan"
            }
        
        # Build PMD command with custom ruleset
        base_cmd = self._get_pmd_command()
        pmd_command = base_cmd + [
            'check',
            '-d', source_dir,
            '-R', ruleset_file,
            '-f', 'csv',
            '--report-file', output_file,
            '--debug'  # Add debug flag for detailed error information
        ]
        
        # Log command for debugging
        env_type = "local" if self.is_local else "Azure"
        logger.info(f"Running in {env_type} environment")
        logger.info(f"PMD command with custom ruleset: {' '.join(pmd_command)}")
        
        # Execute PMD
        try:
            result = subprocess.run(
                pmd_command, 
                capture_output=True, 
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            # PMD exits with 4 if violations are found (this is normal)
            # PMD exits with 5 if errors occurred but may still have partial results
            if result.returncode not in [0, 4, 5]:
                error_msg = f"PMD execution failed with return code {result.returncode}. Stderr: {result.stderr}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "findings_count": 0
                }
            
            # Check if we have results file even if PMD had errors
            if result.returncode == 5:
                logger.warning(f"PMD completed with errors (return code 5), but checking for partial results")
                if result.stderr:
                    logger.warning(f"PMD stderr: {result.stderr}")
            
            logger.info(f"PMD execution completed with return code {result.returncode}")
            if result.stdout:
                logger.debug(f"PMD stdout: {result.stdout}")
            if result.stderr and result.returncode != 5:  # Don't log stderr again for return code 5
                logger.warning(f"PMD stderr: {result.stderr}")
            
            # Parse results - even if PMD had errors, we might have partial results
            findings = self._parse_pmd_results(output_file)
            
            # If PMD had errors but we got no findings, consider it a failure
            if result.returncode == 5 and len(findings) == 0:
                error_msg = f"PMD execution completed with errors and no findings generated. Stderr: {result.stderr}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "findings_count": 0
                }
            
            # If we have findings, consider it a success (even with PMD errors)
            success = result.returncode in [0, 4] or (result.returncode == 5 and len(findings) > 0)
            
            return {
                "success": success,
                "findings_count": len(findings),
                "files_scanned": apex_files,
                "findings": findings,
                "custom_ruleset_file": ruleset_file,
                "scan_timestamp": datetime.now().isoformat(),
                "pmd_return_code": result.returncode,
                "pmd_errors": result.stderr if result.returncode == 5 else None
            }
            
        except subprocess.TimeoutExpired:
            error_msg = "PMD scan timed out after 5 minutes"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
        except Exception as e:
            error_msg = f"Error during PMD scan: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "findings_count": 0
            }
    
    def _count_apex_files(self, directory: str) -> int:
        """Count the number of Apex files in a directory"""
        count = 0
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.cls'):
                    count += 1
        return count
    
    def _parse_pmd_results(self, csv_file: str) -> List[Dict[str, Any]]:
        """
        Parse PMD CSV results into structured findings
        
        Args:
            csv_file: Path to PMD CSV output file
        
        Returns:
            List of finding dictionaries
        """
        if not os.path.exists(csv_file):
            logger.warning(f"PMD results file not found: {csv_file}")
            return []
        
        findings = []
        try:
            with open(csv_file, 'r', encoding='utf-8') as f:
                # Skip header line
                next(f, None)
                
                for line_num, line in enumerate(f, 1):
                    try:
                        finding = self._parse_csv_line(line.strip(), line_num)
                        if finding and not self._is_salesforce_metadata_false_positive(finding):
                            findings.append(finding)
                    except Exception as e:
                        logger.warning(f"Error parsing line {line_num}: {e}")
                        continue
            
                logger.info(f"Parsed {len(findings)} findings from PMD results (filtered)")
            
        except Exception as e:
            logger.error(f"Error reading PMD results file: {e}")
        
        return findings

    def _is_salesforce_metadata_false_positive(self, finding: Dict[str, Any]) -> bool:
        """
        Check if a finding is a false positive specific to Salesforce metadata files
        
        Args:
            finding: PMD finding dictionary
            
        Returns:
            True if the finding should be filtered out as a false positive
        """
        rule_name = finding.get('rule_name', '').lower()
        file_path = finding.get('file_path', '').lower()
        
        # Filter out "MissingEncoding" for Salesforce metadata XML files
        # These are configuration files that don't need XML encoding declarations
        if (rule_name == 'missingencoding' and 
            (file_path.endswith('.js-meta.xml') or 
             file_path.endswith('.cmp-meta.xml') or
             file_path.endswith('.app-meta.xml') or
             file_path.endswith('.page-meta.xml'))):
            return True
        
        return False
    
    def _parse_csv_line(self, line: str, line_num: int) -> Optional[Dict[str, Any]]:
        """
        Parse a single CSV line from PMD output
        
        Args:
            line: CSV line to parse
            line_num: Line number for error reporting
        
        Returns:
            Parsed finding dictionary or None if parsing fails
        """
        if not line:
            return None
        
        try:
            # Split by comma, but handle quoted fields
            parts = self._split_csv_line(line)
            
            if len(parts) < 6:
                logger.warning(f"Invalid CSV line {line_num}: insufficient fields")
                return None
            
            # Extract fields (PMD CSV format: Problem,Package,File,Priority,Line,Description,Rule set,Rule)
            problem = parts[0].strip('"') if len(parts) > 0 else ""
            package = parts[1].strip('"') if len(parts) > 1 else ""
            file_path = parts[2].strip('"') if len(parts) > 2 else ""
            priority = parts[3].strip('"') if len(parts) > 3 else ""
            line_number = parts[4].strip('"') if len(parts) > 4 else ""
            description = parts[5].strip('"') if len(parts) > 5 else ""
            rule_set = parts[6].strip('"') if len(parts) > 6 else ""
            rule_name = parts[7].strip('"') if len(parts) > 7 else ""
            
            # Map PMD priority to standardized severity
            severity = self._map_priority_to_severity(priority)
            
            # Extract category and clean rule name
            category, clean_rule_name = self._extract_category_and_rule(rule_name)
            
            # Get file name from path
            file_name = os.path.basename(file_path) if file_path else "Unknown"
            
            return {
                'problem': problem,
                'package': package,
                'file_path': file_path,
                'file_name': file_name,
                'priority': priority,
                'line_number': line_number,
                'description': description,
                'rule_set': rule_set,
                'rule_name': clean_rule_name,
                'category': category,
                'severity': severity,
                'status': 'failed'  # Default status
            }
            
        except Exception as e:
            logger.warning(f"Error parsing CSV line {line_num}: {e}")
            return None
    
    def _split_csv_line(self, line: str) -> List[str]:
        """Split CSV line while handling quoted fields"""
        parts = []
        current_part = ""
        in_quotes = False
        
        for char in line:
            if char == '"':
                in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                parts.append(current_part)
                current_part = ""
            else:
                current_part += char
        
        parts.append(current_part)
        return parts
    
    def _map_priority_to_severity(self, priority: str) -> str:
        """Map PMD priority to standardized severity levels"""
        try:
            priority_num = int(priority)
            if priority_num <= 2:
                return "High"
            elif priority_num == 3:
                return "Medium"
            else:
                return "Low"
        except (ValueError, TypeError):
            return "Medium"  # Default severity
    
    def _extract_category_and_rule(self, rule_name: str) -> Tuple[str, str]:
        """Extract category and clean rule name from PMD rule name"""
        if ":" in rule_name:
            parts = rule_name.split(":", 1)
            if len(parts) > 1:
                return parts[0].strip(), parts[1].strip()
        
        # Default category if no separator found
        return "Code Quality", rule_name